require("@rushstack/eslint-patch/modern-module-resolution");

module.exports = {
  root: true,
  extends: [
    "plugin:vue/vue3-essential", // Vue 3 基础检查
    "eslint:recommended", // 官方推荐的基本规则
    "@vue/eslint-config-typescript", // Vue + TypeScript 支持
    "@vue/eslint-config-prettier/skip-formatting", // 禁用 ESLint 对格式的干预，交给 Prettier
    "plugin:prettier/recommended" // 启用 Prettier 插件
  ],
  parserOptions: {
    ecmaVersion: "latest", // 支持最新的 ECMAScript
    sourceType: "module" // 使用 ES Modules
  },
  rules: {
    semi: ["warn", "always"], // 语句末尾要求分号
    "no-unused-vars": 0, // 允许未使用的变量（开发中常见，减少干扰）
    "no-shadow": "off", // 允许变量名遮蔽外层变量（实用）
    "no-delete-var": "off", // 允许 delete 操作变量（极少用到，关闭）
    "no-duplicate-case": "warn", // switch 中禁止重复 case
    "no-unreachable": "warn", // 禁止不可达代码
    "no-empty-function": "warn", // 禁止空函数（有需要时例外）
    "no-redeclare": "warn", // 禁止变量重复声明
    "no-multi-spaces": "warn", // 禁止多余空格
    "no-trailing-spaces": "warn", // 禁止行尾空格
    "no-mixed-spaces-and-tabs": "warn", // 禁止空格和 tab 混合
    "space-before-blocks": "warn", // 块前空格
    "space-infix-ops": "warn", // 操作符前后空格
    "arrow-spacing": "warn", // 箭头函数空格
    "brace-style": "warn", // 大括号风格一致
    "array-bracket-spacing": "warn", // 数组括号内的空格统一
    "switch-colon-spacing": "warn", // switch 的 case 冒号空格一致
    "max-nested-callbacks": ["warn", 3], // 最大回调嵌套 3 层，防止回调地狱
    "vue/multi-word-component-names": 0, // 允许单词组件名（项目习惯）
    "comma-dangle": 0 // 不强制要求末尾逗号
  }
};
