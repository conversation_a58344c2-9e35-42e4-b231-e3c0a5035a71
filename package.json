{"name": "apm-web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --open", "build": "pnpm run type-check && pnpm run build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:lint-staged": "lint-staged", "format": "prettier --write src/", "prepare": "husky", "commitlint": "commitlint --config commitlint.config.cjs -e -V", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "{!(package)*.json,*.code-snippets,.!(browserslist)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["prettier --write", "eslint --fix"], "*.{scss,less,styl,html}": ["prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@ant-design/icons": "^6.0.0", "@antv/g6": "^5.0.49", "@element-plus/icons-vue": "^2.3.1", "@flowgram.ai/fixed-layout-editor": "^0.4.2", "@flowgram.ai/free-layout-editor": "^0.4.2", "@logicflow/core": "^2.0.10", "@logicflow/extension": "^2.0.14", "@logicflow/vue-node-registry": "^1.0.12", "@types/sm-crypto": "^0.3.4", "@vue-flow/core": "^1.46.0", "@vueuse/core": "^10.10.0", "ant-design-vue": "^4.2.6", "apmagent-monitor": "file:apmagent-monitor-9.2.7.tgz", "axios": "^1.7.2", "dagre": "^0.8.5", "dayjs": "^1.11.12", "echarts": "^5.5.1", "element-plus": "^2.10.7", "html2canvas": "^1.4.1", "ids": "^1.0.5", "jsencrypt": "^3.3.2", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "skywalking-client-js": "^1.0.1", "skywalking-mointor": "^1.0.75", "sm-crypto": "^0.3.13", "uuid": "^11.1.0", "vue": "^3.4.27", "vue-router": "^4.3.2", "vuera": "^0.2.7"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@rushstack/eslint-patch": "^1.10.3", "@tsconfig/node20": "^20.1.4", "@types/dagre": "^0.7.53", "@types/lodash": "^4.17.20", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.2", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "cssnano": "^7.0.6", "cz-git": "^1.9.3", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "mockjs": "^1.1.0", "npm-run-all2": "^6.2.0", "postcss": "^8.4.47", "prettier": "^3.3.1", "sass": "^1.77.4", "terser": "^5.32.0", "typescript": "~5.4.5", "unocss": "^0.61.3", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.12", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.0.19"}, "repository": "git clone https://git.code.tencent.com/curtzhang/apm-web.git", "author": "", "license": "MIT"}