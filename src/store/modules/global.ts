import { defineStore } from "pinia";
interface IServiceTimeConfig {
  remberTime: number; // 是否记住时间
  start_time: string; // 开始时间
  end_time: string; // 结束时间
}
// 获取事件选择信息
export const serviceTimeStore = defineStore(
  "serviceTime",
  () => {
    const serviceTimeData = ref<IServiceTimeConfig>({
      remberTime: 0,
      start_time: "",
      end_time: ""
    });
    const setServiceTIme = (val: string[], isRemberTime?: number) => {
      serviceTimeData.value = {
        remberTime: isRemberTime || 0,
        start_time: val?.[0] || "",
        end_time: val?.[1] || ""
      };
    };
    const setRemberTime = (val: number) => {
      serviceTimeData.value.remberTime = val;
    };
    return {
      serviceTimeData,
      setServiceTIme,
      setRemberTime
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
      key: "serviceTime"
    }
  }
);

export const useGlobalStore = defineStore("global", () => {
  /**
   * 侧边栏显示隐藏
   */
  const sidebar = reactive({
    isActive: false
  });

  const toggleClick = () => {
    sidebar.isActive = !sidebar.isActive;
  };

  return {
    sidebar,
    toggleClick
  };
});
