import { defineStore } from "pinia";

export const breadcrumbStore = defineStore(
  "breadcrumbConfig",
  () => {
    const breadcrumbTitle = ref("");
    const applicationMonitorName = ref("");
    const method = ref("");
    const appOption = ref("");
    const basePath = ref("");
    const startRequest = ref(false);
    const topSelect = ref("");

    const setBreadcrumb = (val: string) => {
      breadcrumbTitle.value = val;
    };
    const setSelectAppName = (val: string) => {
      applicationMonitorName.value = val;
    };
    const setMethod = (val: string) => {
      method.value = val;
    };
    const setAppOption = (val: string) => {
      appOption.value = val;
    };
    const setBasePath = (val: string) => {
      basePath.value = val;
    };
    const setStartRequest = (val: boolean) => {
      startRequest.value = val;
    };
    const setTopSelect = (val: string) => {
      topSelect.value = val;
    };
    return {
      breadcrumbTitle,
      applicationMonitorName,
      method,
      setBreadcrumb,
      setSelectAppName,
      setMethod,
      appOption,
      setAppOption,
      basePath,
      setBasePath,
      startRequest,
      setStartRequest,
      topSelect,
      setTopSelect
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
      key: "breadcrumbConfig"
    }
  }
);
