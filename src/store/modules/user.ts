import { defineStore } from "pinia";
import { IUserInfo } from "@/api/user/type";

enum IUserType {
  Admin = 0, // 超级管理员
  Normal = 1 // 普通用户
}

// 获取用户信息
export const userInfoStore = defineStore(
  "userInfo",
  () => {
    const userInfoData = ref<IUserInfo>({} as IUserInfo);
    const setUserInfo = (user: IUserInfo | {}) => {
      userInfoData.value = user;
    };
    return {
      userInfoData,
      setUserInfo
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
      key: "userInfo"
    }
  }
);
