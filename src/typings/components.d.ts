/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddNode: typeof import('./../views/ToPo/PropertySetting/components/addNode.vue')['default']
    AddText: typeof import('./../views/ToPo/PropertySetting/components/addText.vue')['default']
    AppBreadBack: typeof import('./../layout/components/DateBack/components/AppBreadBack.vue')['default']
    BaseEcharts: typeof import('./../components/baseEcharts/index.vue')['default']
    Collapse: typeof import('./../layout/components/Header/components/Collapse.vue')['default']
    Components: typeof import('./../views/module/database/components/index.vue')['default']
    Control: typeof import('./../views/warnManage/analysis/components/Control.vue')['default']
    copy: typeof import('./../views/cozi/resourceLibrary/flowgram/components/SpecialNode copy.vue')['default']
    Date: typeof import('./../layout/components/DateBack/components/Date.vue')['default']
    DateBack: typeof import('./../layout/components/DateBack/index.vue')['default']
    Detail: typeof import('./../views/module/database/components/detail.vue')['default']
    Details: typeof import('./../views/ToPo/PropertySetting/components/details.vue')['default']
    Dialog: typeof import('./../views/cozi/development/components/components/dialog.vue')['default']
    DialogForm: typeof import('./../views/system/user/components/DialogForm.vue')['default']
    Documentation: typeof import('./../components/documentation/index.vue')['default']
    DownloadGuide: typeof import('./../layout/components/Header/components/DownloadGuide.vue')['default']
    Editor: typeof import('./../views/ToPo/PropertySetting/components/editor.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLoadingSpinner: typeof import('element-plus/es')['ElLoadingSpinner']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    Email: typeof import('./../views/warnManage/method/components/Email.vue')['default']
    FullScreen: typeof import('./../layout/components/Header/components/FullScreen.vue')['default']
    Group: typeof import('./../views/module/queue/components/group.vue')['default']
    Header: typeof import('./../layout/components/Header/index.vue')['default']
    HeadMenu: typeof import('./../layout/components/HeadMenu/index.vue')['default']
    Indicator: typeof import('./../components/Indicator/index.vue')['default']
    InterfaceDetail: typeof import('./../views/h5/interfacePerformance/components/interfaceDetail.vue')['default']
    IpDetail: typeof import('./../views/flowMonitoring/ipList/components/IpDetail.vue')['default']
    Logo: typeof import('./../layout/components/Logo/index.vue')['default']
    MiniAjaxinterfaceDetail: typeof import('./../views/miniprogram/miniAjaxPer/components/MiniAjaxinterfaceDetail.vue')['default']
    MyColumn: typeof import('./../components/table/my-column.vue')['default']
    MyTable: typeof import('./../components/table/my-table.vue')['default']
    NetWorkDetail: typeof import('./../views/application/networkRequest/components/NetWorkDetail.vue')['default']
    NodePanel: typeof import('./../views/warnManage/analysis/components/NodePanel.vue')['default']
    NotificationGroup: typeof import('./../views/warnManage/notification/components/notificationGroup.vue')['default']
    NotificationObject: typeof import('./../views/warnManage/notification/components/notificationObject.vue')['default']
    OverView: typeof import('./../views/module/queue/components/overView.vue')['default']
    PluginDemo: typeof import('./../views/cozi/development/components/components/pluginDemo.vue')['default']
    PopupLayer: typeof import('./../components/PopupLayer/index.vue')['default']
    PromptDialog: typeof import('./../views/cozi/development/bot/components/promptDialog.vue')['default']
    PropertyPanel: typeof import('./../views/warnManage/analysis/components/PropertyPanel.vue')['default']
    Ranking: typeof import('./../components/Ranking/index.vue')['default']
    Refresh: typeof import('./../layout/components/Header/components/Refresh.vue')['default']
    ReleaseDialog: typeof import('./../views/cozi/development/bot/components/releaseDialog.vue')['default']
    ReuseDialog: typeof import('./../views/cozi/resourceLibrary/components/reuseDialog.vue')['default']
    ReuseDiaolg: typeof import('./../views/cozi/development/components/components/reuseDiaolg.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchInput: typeof import('./../components/SearchInput/index.vue')['default']
    SidebarMenu: typeof import('./../layout/components/SidebarMenu/index.vue')['default']
    SidebarMenuItem: typeof import('./../layout/components/SidebarMenu/components/SidebarMenuItem.vue')['default']
    Speciainode: typeof import('./../views/cozi/resourceLibrary/flowgram/components/speciainode.vue')['default']
    SpecialEdge: typeof import('./../views/cozi/resourceLibrary/flowgram/components/SpecialEdge.vue')['default']
    SpecialNode: typeof import('./../views/cozi/resourceLibrary/flowgram/components/SpecialNode.vue')['default']
    StartPerforDetail: typeof import('./../views/application/startPerformance/components/StartPerforDetail.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SysMenu: typeof import('./../layout/components/SysMenu/index.vue')['default']
    Tabs: typeof import('./../layout/components/Tabs/index.vue')['default']
    TagsView: typeof import('./../layout/components/TagsView/index.vue')['default']
    TextMessage: typeof import('./../views/warnManage/method/components/textMessage.vue')['default']
    TitleCom: typeof import('./../components/TitleCom/index.vue')['default']
    Topic: typeof import('./../views/module/queue/components/topic.vue')['default']
    Trend: typeof import('./../views/module/snmp/components/trend.vue')['default']
    UnitArray: typeof import('./../views/flowMonitoring/TCPdetail/components/unitArray.vue')['default']
    User: typeof import('./../layout/components/Header/components/User.vue')['default']
    UserDialog: typeof import('./../views/cozi/development/app/components/userDialog.vue')['default']
    ViewDialog: typeof import('./../views/cozi/development/bot/components/viewDialog.vue')['default']
    WorkflowDetail: typeof import('./../views/warnManage/workflow/components/WorkflowDetail.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
