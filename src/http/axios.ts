import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
  AxiosRequestConfig
} from "axios";
import { getMessageInfo } from "./status";
import { ElMessage } from "element-plus";

interface BaseResponse<T = any> {
  code: number | string;
  desc: string;
  data: T;
}

// 创建 Axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASEURL,
  timeout: 15000
});

// ---------------- 错误提示节流逻辑 ---------------- //
const errorMessageCache = new Map<number | string, number>();

function showThrottledErrorMessage(message: string, code?: number | string, duration = 3000) {
  const now = Date.now();
  const key = code ?? message;
  const lastShown = errorMessageCache.get(key);

  if (lastShown && now - lastShown < duration) {
    return;
  }

  errorMessageCache.set(key, now);

  ElMessage({
    message,
    type: "error"
  });
}

// 定期清理缓存，防止内存泄漏（可选）
setInterval(() => {
  const now = Date.now();
  for (const [key, time] of errorMessageCache) {
    if (now - time > 30000) {
      errorMessageCache.delete(key);
    }
  }
}, 10000);

// ---------------- 会话超时自动跳转 ---------------- //
let timer: ReturnType<typeof setTimeout>;
function resetTimer() {
  clearTimeout(timer);
  timer = setTimeout(() => {
    window.location.href = window.location.origin;
  }, 900000); // 15分钟
}
resetTimer();

// ---------------- 请求拦截器 ---------------- //
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    resetTimer();
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// ---------------- 响应拦截器 ---------------- //
service.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.status === 200) {
      return response;
    }
    const message = response.data?.desc?.trim() || getMessageInfo(response.status);
    showThrottledErrorMessage(message, response.status);
    return response;
  },
  (error: any) => {
    const { response } = error;
    if (response) {
      const code = response.status;
      const message = response.data?.desc?.trim() || getMessageInfo(code);

      if (code === 401 || code === 403) {
        showThrottledErrorMessage(message, code);
        setTimeout(() => {
          window.location.href = window.location.origin;
        }, 500);
        return;
      }

      showThrottledErrorMessage(message, code);
      return Promise.reject(response.data);
    }

    showThrottledErrorMessage("网络连接异常,请稍后再试!", "network");
    return Promise.reject(error);
  }
);

// ---------------- 请求函数封装 ---------------- //
const requestInstance = <T = any>(
  config: AxiosRequestConfig,
  skipIntercept: boolean = false
): Promise<T> => {
  const conf = config;
  return new Promise((resolve, reject) => {
    service
      .request<any, AxiosResponse<BaseResponse>>(conf)
      .then((res: AxiosResponse<BaseResponse>) => {
        if (skipIntercept) {
          resolve(res as unknown as T);
          return;
        }

        if (res.status === 204) {
          resolve({ code: 204, desc: "Success", data: null } as T);
          return;
        }

        const data = res.data || {};
        if (![0, 200, 201, 204].includes(Number(data.code))) {
          if (data.code === -1000) {
            window.location.href = window.location.origin;
            return;
          }
          showThrottledErrorMessage(data.desc, data.code);
          reject(data.desc);
        } else {
          resolve(data as T);
        }
      })
      .catch(err => {
        reject(err);
      });
  });
};

// ---------------- 具体方法封装 ---------------- //
export function get<T = any, U = any>(
  config: AxiosRequestConfig,
  url: string,
  parms?: U
): Promise<T> {
  return requestInstance({ ...config, url, method: "GET", params: parms });
}

export function post<T = any, U = any>(
  config: AxiosRequestConfig,
  url: string,
  data: U,
  useQuery: boolean = false,
  JsonArr: boolean = false
): Promise<T> {
  const finalConfig = { ...config, url, method: "POST" };
  if (JsonArr) {
    finalConfig.data = Object.values(data as { [key: string]: any });
    return requestInstance(finalConfig);
  }
  if (useQuery) {
    finalConfig.params = data;
  } else {
    finalConfig.data = data;
  }
  return requestInstance(finalConfig);
}

export function put<T = any, U = any>(
  config: AxiosRequestConfig,
  url: string,
  data?: U,
  useQuery: boolean = false,
  JsonArr: boolean = false
): Promise<T> {
  const finalConfig = { ...config, url, method: "PUT" };
  if (JsonArr) {
    finalConfig.data = Object.values(data as { [key: string]: any });
    return requestInstance(finalConfig);
  }
  if (useQuery) {
    finalConfig.params = data;
  } else {
    finalConfig.data = data;
  }
  return requestInstance(finalConfig);
}

export function del<T = any, U = any>(
  config: AxiosRequestConfig,
  url: string,
  data: U
): Promise<T> {
  return requestInstance({ ...config, url, method: "DELETE", data: data });
}

export function getBlob<T = any, U = any>(
  config: AxiosRequestConfig,
  url: string,
  params?: U
): Promise<T> {
  const blobConfig = {
    ...config,
    responseType: "blob" as const,
    timeout: config.timeout || 60000
  };
  return requestInstance(
    {
      ...blobConfig,
      url,
      method: "GET",
      params: params
    },
    true
  );
}
