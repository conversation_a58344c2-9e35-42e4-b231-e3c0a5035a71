// 处理单位
interface IIFromatNums {
  fixValue: string;
  unit?: string;
}
export function formatNums(num: number): IIFromatNums {
  if (num >= 100000000) {
    // 超过亿的数字
    return {
      fixValue: (Math.floor(num / 1000000) / 100).toFixed(2),
      unit: "亿"
    };
  } else if (num >= 10000) {
    // 超过万的数字
    return {
      fixValue: (Math.floor(num / 100) / 100).toFixed(2),
      unit: "万"
    };
  }
  // 小于万的数字
  return {
    fixValue: num.toString(),
    unit: ""
  };
}
