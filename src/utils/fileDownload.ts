import { ElLoading, ElMessage } from 'element-plus';
import type { AxiosResponse } from 'axios';

// 下载配置选项
interface DownloadOptions {
  loadingText?: string; // 加载提示文本
  allowedTypes?: string[]; // 允许的文件类型
  defaultFileName?: string; // 默认文件名
  timeout?: number; // 清理资源的延迟时间(ms)
}

// 解析响应头信息
const extractHeaders = (headers: Record<string, string>) => {
  return {
    contentType: headers['content-type'] || headers['Content-Type'] || '',
    contentDisposition: headers['content-disposition'] || headers['Content-Disposition'] || ''
  };
};

// 解析Blob中的JSON错误信息
const parseJsonError = async (blob: Blob): Promise<string | null> => {
  try {
    const text = await blob.text();
    const errorObj = JSON.parse(text);
    return errorObj.message || errorObj.desc || null;
  } catch {
    return '解析错误信息失败';
  }
};

// 解析文件名（兼容各种Content-Disposition格式）
const parseFileName = (disposition: string): string | null => {
  if (!disposition) return null;

  // 匹配filename=xxx或filename*=UTF-8''xxx格式
  const filenameMatch = disposition.match(/filename(?:\*=UTF-8'')?="?([^";]+)"?/i);
  if (filenameMatch && filenameMatch[1]) {
    return decodeURIComponent(filenameMatch[1]);
  }
  return null;
};

// 触发文件下载
const triggerFileDownload = (
  blob: Blob, 
  fileName: string, 
  contentType: string, 
  timeout: number = 100
) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  
  a.href = url;
  a.download = fileName;
  a.type = contentType;

  document.body.appendChild(a);
  a.click();
  
  // 延迟清理，避免某些浏览器下载失败
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, timeout);
};

/**
 * 通用文件下载函数
 * @param requestFn 接口请求函数（需返回Promise<AxiosResponse<Blob>>）
 * @param options 下载配置选项
 * @returns Promise<void>
 */
export const downloadFile = async <T = any>(
  requestFn: () => Promise<AxiosResponse<Blob>>,
  options: DownloadOptions = {}
): Promise<void> => {
  // 默认配置
  const {
    loadingText = '正在下载文件...',
    allowedTypes = ['application/pdf', 'text/plain', 'application/octet-stream', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    defaultFileName = 'download',
    timeout = 100
  } = options;

  // 显示加载
  const loading = ElLoading.service({
    text: loadingText,
    lock: true,
    background: 'rgba(255, 255, 255, 0.7)'
  });

  try {
    // 执行请求
    const response = await requestFn();
    const { data: blob, headers } = response;

    // 验证响应是否为Blob
    if (!(blob instanceof Blob)) {
      throw new Error('下载响应不是有效的文件流');
    }

    // 提取响应头
    const { contentType, contentDisposition } = extractHeaders(headers);

    // 处理JSON错误响应
    if (contentType.includes('application/json')) {
      const errorInfo = await parseJsonError(blob);
      throw new Error(errorInfo || '服务器返回错误，请联系管理员');
    }

    // 验证文件类型
    if (!allowedTypes.some(type => contentType.includes(type))) {
      throw new Error(`不支持的文件类型：${contentType}`);
    }

    // 解析文件名
    const fileName = parseFileName(contentDisposition) || defaultFileName;

    // 触发下载
    triggerFileDownload(blob, fileName, contentType, timeout);

    ElMessage.success(`文件已开始下载：${fileName}`);

  } catch (error: any) {
    const errorMsg = error.message || '下载失败，请稍后重试';
    ElMessage.error(errorMsg);
    console.error('文件下载失败：', error);
    throw error; // 允许上层捕获错误
  } finally {
    loading.close();
  }
};
