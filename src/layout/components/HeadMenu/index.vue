<template>
  <div class="project-title mr-3">
    <div class="mr-2 w-4 h-4 mt--3px">
      <img src="../../../image/logo.png" alt="" />
    </div>
    <span class="title">运维可观测平台</span>
    <el-divider direction="vertical" />
    <el-dropdown
      class="dropdown"
      :hide-on-click="false"
      placement="bottom-start"
      trigger="click"
      ref="dropdownRef"
      popper-class="dropdown-custome-container"
      @visible-change="handleVisible"
      :teleported="false"
    >
      <span class="el-dropdown-link" @click="handleIconClick">
        <span class="text-white text-sm leading-5 menu-spec-title">{{ activeName }}</span>
        <el-icon :class="['icon-caretbottom', { 'icon-rotated': transIconFlag }]">
          <caret-bottom />
        </el-icon>
      </span>

      <template #dropdown>
        <div class="table-wrapper" @click.stop>
          <div class="flex justify-between items-center mb-4">
            <SearchInput
              @search="performSearch"
              @searchChange="searchChange"
              @enterChange="enterChange"
            />
            <el-cascader
              v-if="showOrganCascader"
              placeholder="请选择所属单位"
              :props="props1"
              :options="treesData"
              @change="handleCascaderChange"
              v-model="pageParams.organId"
              placement="right"
              filterable
              clearable
              :teleported="false"
              style="width: 200px"
            />
          </div>
          <MyTable
            :data="applicationList"
            :total="appListTotal"
            style="width: 658px"
            height="270"
            class="flex-1"
            @currentChange="handleCurrentChange"
            pagination-layout="total, prev, pager, next, jumper"
          >
            <my-column property="name" label="应用名称">
              <template #default="scope">
                <span @click="handleRowClick(scope.row)" class="service-name cursor-pointer">
                  {{ scope.row.name }}
                </span>
              </template>
            </my-column>
            <my-column property="organName" label="单位名称">
              <template v-slot="{ row }">
                <span class="cursor-pointer" @click="handleOrganNameClick(row)">
                  {{ row.organName }}
                </span>
              </template>
            </my-column>
          </MyTable>
        </div>
      </template>
    </el-dropdown>
    <!-- <span v-else class="text-white text-sm leading-5 menu-spec-title"> 深圳地铁第一 </span> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, reactive } from "vue";
import { getUserApps, IUserAppItem } from "@/api/application/index";
import { useRouter, useRoute } from "vue-router";
import { CaretBottom } from "@element-plus/icons-vue";
import { DropdownInstance } from "element-plus";
import { applicationStore } from "@/store/modules/application";
import { useSubMenuStore } from "@/store/modules/tagsView";
import { getOrganList } from "@/api/system/organ";
const subMenuStore = useSubMenuStore();

const useApplicationStore = applicationStore();
const activeName = ref("应用加载中");
const appid = ref("");
const organId = ref("");
const applicationList = ref<IUserAppItem[]>([]);
const transIconFlag = ref(false);
const applicationName = ref("");
const router = useRouter();
const route = useRoute();
const appListTotal = ref(0);
const dropdownRef = ref<DropdownInstance>();
const pageParams = reactive({
  page: 1,
  pageSize: 10,
  name: "",
  organId: ""
});

const props1 = {
  checkStrictly: true
};

const treesData = ref([]);

const emit = defineEmits(["appChange"]);

// 点击旋转icon
const handleIconClick = () => {
  transIconFlag.value = !transIconFlag.value;
};
// 处理隐藏下拉、旋转icon
const handleVisible = (visible: boolean) => {
  transIconFlag.value = visible;
};
// 搜索框内容改变
const searchChange = (keywords: string) => {
  applicationName.value = keywords;
};
// 通过/的数量来判断是否是详情页，如果是，则显示返回按钮
function hasMoreThanTwoSlashes(str: string) {
  const parts = str.split("/");
  const slashCount = parts.length - 1;
  return slashCount >= 3;
}
function splitByLastSlash(inputString: string) {
  const lastIndex = inputString.lastIndexOf("/");
  if (lastIndex === -1) {
    // 如果字符串中没有 / ，则返回整个字符串
    return inputString;
  }
  // 返回 / 前面的部分
  return inputString.substring(0, lastIndex);
}

// 表格点击
function handleRowClick(row: IUserAppItem) {
  dropdownRef.value?.handleClose();
  activeName.value = row.name;
  appid.value = row.appid;
  organId.value = row.organId;
  // 点击表格项时，隐藏下拉菜单
  transIconFlag.value = false;
  useApplicationStore.setAppName(activeName.value);
  useApplicationStore.setAppId(appid.value);
  useApplicationStore.setOrganId(organId.value);
  subMenuStore.reset();
  router.push("/Topology");
  emit("appChange");
  // const url = hasMoreThanTwoSlashes(route.path);
  // if (url) {
  //   router.push(splitByLastSlash(route.path));
  // } else {
  //   emit("appChange");
  // }
}

// 处理单位名称点击
const handleOrganNameClick = (row: IUserAppItem) => {
  // dropdownRef.value?.handleClose();
  pageParams.organId = row.organId;
  getUserAppsData();
};

// 点击搜索
const performSearch = (keywords: string) => {
  pageParams.page = 1;
  pageParams.name = keywords;
  getUserAppsData();
};
const enterChange = (keywords: string) => {
  performSearch(keywords);
};
// 分页
function handleCurrentChange(page: number) {
  pageParams.page = page;
  getUserAppsData();
}

async function getUserAppsData() {
  const params = {
    page: pageParams.page,
    rows: pageParams.pageSize,
    name: pageParams.name,
    organId: pageParams.organId
      ? Array.isArray(pageParams.organId)
        ? pageParams.organId.slice(-1)[0]
        : pageParams.organId
      : ""
  };
  try {
    const res = await getUserApps(params);
    applicationList.value = res?.records || [];
    activeName.value =
      useApplicationStore.appName || res.records?.[0]?.name || activeName.value || "";
    appid.value = useApplicationStore.appId || appid.value || res.records?.[0]?.appid || "";
    // 确保在第一次加载时更新 store
    useApplicationStore.$patch(state => {
      state.appName = activeName.value;
      state.appId = appid.value;
    });
    appListTotal.value = Number(res?.total) || 0;
    if (activeName.value === "") {
      router.push("/system/appList");
    }
  } catch (err) {
    console.log(err);
  }
}

// 处理级联选择器变化
function handleCascaderChange() {
  getUserAppsData();
}

// 获取组织结构
const getOrganTree = async () => {
  const params = {
    name: "",
    status: 1
  };
  try {
    const res = await getOrganList(params);
    if (res.code === 0) {
      const processTreeData = (data: any[]): Tree[] => {
        return data.map(item => {
          const node: Tree = {
            label: item.name,
            value: item.id,
            id: item.id,
            children: item.children ? processTreeData(item.children) : []
          };
          return node;
        });
      };

      treesData.value = processTreeData(res.records || []);
    }
  } catch (error) {
    console.error("获取组织结构 ===>>>", error);
  }
};
// 响应 sessionStorage userInfo 变化，保证登录后无需刷新页面
const userInfoRef = ref(sessionStorage.getItem("userInfo"));
function updateUserInfo() {
  userInfoRef.value = sessionStorage.getItem("userInfo");
}
onMounted(() => {
  window.addEventListener("storage", updateUserInfo);
});
onBeforeUnmount(() => {
  window.removeEventListener("storage", updateUserInfo);
});
const showOrganCascader = computed(() => {
  try {
    if (userInfoRef.value) {
      const userInfoObj = JSON.parse(userInfoRef.value);
      const roles = userInfoObj.userInfoData?.roles || [];
      return roles.includes("TENANT-ADMIN") || roles.includes("PLATFORM-ADMIN");
    }
  } catch (e) {}
  return false;
});
onMounted(async () => {
  await getUserAppsData();
  await getOrganTree();
});
// 在现有代码后添加watch监听
watch(
  () => useApplicationStore.appName,
  newName => {
    if (newName && newName !== activeName.value) {
      activeName.value = newName;
      console.log("应用名称已更新:", newName);
    }
  }
);

watch(
  () => useApplicationStore.appId,
  newId => {
    if (newId && newId !== appid.value) {
      appid.value = newId;
      console.log("应用ID已更新:", newId);
      // 如果需要可以重新加载数据
      // getUserAppsData("")
    }
  }
);

// 监听路由变化，处理特殊逻辑
watch(
  () => route.path,
  newPath => {
    if (newPath.includes("/Topology")) {
      // 拓扑页面特殊处理
    }
  }
);

// 监听下拉菜单状态
watch(transIconFlag, isOpen => {
  if (!isOpen) {
    // 菜单关闭时清理或重置状态
  }
});
onBeforeUnmount(() => {
  // 清理不再需要的事件监听
});
</script>
<style>
/* .dropdown-custome-container {
  inset: 57px auto auto 181px !important;
} */
</style>
<style lang="scss" scoped>
.el-menu--horizontal > .el-menu-item:nth-child(1) {
  margin-right: auto;
}
.dropdown {
  height: 20px;
}
.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #fff !important;
}
.project-title {
  max-width: 364px;
  width: 364px;
  height: 60px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 16px;
  .title {
    min-width: 120px;
    color: #fff;
  }
  .el-dropdown-link {
    width: 100%;
  }
}
.menu-spec-title {
  max-width: 100px;
  width: 100px;
  white-space: nowrap; /* 保持文本在一行内 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 添加省略号 */
}
.el-menu-vertical-demo {
  border-right: none;
  background-color: #445fde;
  color: #fff;
  position: relative;
  :deep(.el-sub-menu__title i) {
    color: #fff;
  }
  :deep(.el-sub-menu__title .el-sub-menu__icon-arrow) {
    display: none;
  }
}
.transIcon i {
  transform: rotate(180deg);
}

.icon-caretbottom {
  transition: transform 0.5s ease;
  color: #fff;
}

.icon-rotated {
  transform: rotate(180deg); /* 旋转180度 */
}
.table-wrapper {
  height: 400px;
  min-width: 700px;
  padding: 8px 20px 20px 20px;
  // overflow: auto;
}

.el-divider--vertical {
  border-left: 1px var(--el-border-color) var(--el-border-style);
}
:deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover) {
  background-color: unset !important;
}
.service-name {
  color: #0064c8;
}
.my-cascader-dropdown {
  margin-top: 8px !important; /* 控制弹出层垂直偏移 */
  left: -220px !important; /* 控制水平方向偏移 */
  z-index: 3000 !important; /* 确保不会被遮住 */
  position: absolute !important; /* 确保位置正确 */
}
</style>
