<template>
  <div class="download-icon-and-text" @click="triggerDownload">
    <el-icon color="#fff" size="18" class="cursor-pointer"><Memo /></el-icon>
    <span class="operation-guide font-bold">使用指南</span>
  </div>
</template>
<script setup lang="ts">
const emit = defineEmits(["download"]);
const triggerDownload = () => {
  window.open("/锋驰可观测平台-使用指南.pdf", "_blank");
  emit("download");
};
</script>
<style lang="scss" scoped>
.download-icon-and-text {
  display: flex;
  align-items: center;

  .operation-guide {
    color: #fff;
    margin-left: 5px;
    font-size: 14px;
    white-space: nowrap;
    cursor: pointer;
    margin-right: 20px;
  }
}
</style>
