<template>
  <div class="w-130px text-center leading-2">
    <el-dropdown trigger="click">
      <div class="flex items-center cursor-pointer font-bold">
        <!-- <div class="user-avatar">A</div> -->
        <div style="font-size: 14px" class="ml-6px text-white">{{ userName }}</div>
        <el-icon class="ml-2 icon-c"><CaretBottom /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="editPwd"> 修改密码 </el-dropdown-item>
          <el-dropdown-item v-if="isAdmin" @click="switchTenant"> 切换租户 </el-dropdown-item>
          <el-dropdown-item @click="logout" divided> 注销登录 </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-dialog
      destroy-on-close
      v-model="centerDialogVisible"
      title="修改密码"
      class="text-left"
      width="500"
      :close-on-click-modal="false"
      :align-center="true"
    >
      <el-form
        ref="loginFormRef"
        :model="editPwdData"
        style="width: 100%; text-align: center"
        :rules="editRules"
      >
        <div class="flex ml-10 mb-1">
          <div class="mr-10 mt-3">原密码：</div>
          <el-form-item prop="oldPwd">
            <div>
              <el-input
                type="password"
                show-password
                style="width: 240px"
                v-model="editPwdData.oldPwd"
                placeholder="原密码"
                clearable
              />
            </div>
          </el-form-item>
        </div>
        <div class="flex ml-10 mb-1">
          <div class="mr-10 mt-3">新密码：</div>
          <el-form-item prop="newPwd">
            <div>
              <el-input
                type="password"
                show-password
                style="width: 240px"
                v-model="editPwdData.newPwd"
                placeholder="新密码"
                clearable
              />
            </div>
          </el-form-item>
        </div>
        <div class="flex mb-4 ml-10">
          <div class="mr-24px mt-3">确认密码：</div>
          <el-form-item prop="conNewpwd">
            <div>
              <el-input
                type="password"
                show-password
                style="width: 240px"
                v-model="editPwdData.conNewpwd"
                placeholder="确认密码"
                clearable
              />
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEditPwd"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="tenantDialogVisible"
      title="切换租户"
      class="text-left"
      width="500"
      :close-on-click-modal="false"
      :align-center="true"
    >
      <div class="mb-4 font-medium">您的账号对应多个租户，请选择要查看的租户</div>

      <el-radio-group v-model="selectedTenantId" class="tenant-selector">
        <div v-for="tenant in tenantList" :key="tenant.id" class="tenant-card">
          <div>
            <el-radio :label="tenant.id">
              <div class="flex items-center">
                <el-icon class="mr-2"><User /></el-icon>
                <span class="el-radio__text">{{ tenant.name }}</span>
              </div>
            </el-radio>
          </div>
          <div v-if="currentId === tenant.id" class="tenant-card-text">当前查看的租户</div>
        </div>
      </el-radio-group>

      <template #footer>
        <el-button @click="tenantDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSwitchTenant">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { editPassword, logOut } from "@/api/user/index";
import { IBaseResult } from "@/api/user/type";
import { useRouter } from "vue-router";
import { userInfoStore } from "@/store/modules/user";
import { useSubMenuStore } from "@/store/modules/tagsView";
import { getUserInfo, getTenantInfo, switchTenants } from "@/api/user";
import { IUserDetailInfo } from "@/api/user/type";
import { sm4, sm2 } from "sm-crypto";
import { v4 as uuidv4 } from "uuid";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();

interface IEditPwd {
  oldPwd: string;
  newPwd: string;
  conNewpwd: string;
}
const useUserInfoStore = userInfoStore();
const router = useRouter();
const centerDialogVisible = ref(false);
const userInfo = ref<IUserDetailInfo>({});
const currentId = ref("");
// const newPwd = ref("");
// const conNewpwd = ref("");
const editPwdData = ref<IEditPwd>({
  oldPwd: "",
  newPwd: "",
  conNewpwd: ""
});

// 租户相关
const tenantDialogVisible = ref(false);
const tenantList = ref([]);
const selectedTenantId = ref("");

// 打开弹窗并加载租户列表
function switchTenant() {
  tenantDialogVisible.value = true;
  selectedTenantId.value = selectedTenantId.value || "";
}

// 确认切换租户
const confirmSwitchTenant = async () => {
  if (!selectedTenantId.value) {
    ElMessage.warning("请选择要访问的租户");
    return;
  }
  try {
    // 调用后端接口切换租户
    await switchTenants(selectedTenantId.value);
  } catch (error) {
    console.error("切换租户失败:", error);
    ElMessage.error("租户切换失败，请稍后重试");
    return;
  }
  ElMessage.success("租户切换成功");
  tenantDialogVisible.value = false;
  useApplicationStore.setAppName("");
  useApplicationStore.setAppId("");
  useApplicationStore.setOrganId("");
  // 刷新权限/菜单
  window.location.reload();
};
// 获取租户信息
const getTenantList = async () => {
  const params = {
    page: 1,
    rows: 10,
    name: "",
    code: "",
    type: null
  };
  try {
    const res = await getTenantInfo(params);
    if (res.code === 0) {
      tenantList.value = res.records || [];
      const currentTenant = res.records.find(tenant => tenant.isCurrentTenant);
      selectedTenantId.value = currentId.value = currentTenant?.id || "";
      // 如果当前选中的租户不在列表中，重置为第一个租户
      // if (!tenantList.value.some(tenant => tenant.id === selectedTenantId.value)) {
      //   selectedTenantId.value = tenantList.value[0]?.id || "";
      // }
    }
  } catch (error) {
    console.error("获取租户信息失败:", error);
  }
};

const editRules = computed(() => {
  return {
    oldPwd: [{ required: true, trigger: "blur", message: `请输入原密码` }],
    newPwd: [{ required: true, trigger: "blur", message: "请输入新密码" }],
    conNewpwd: [{ required: true, trigger: "blur", message: "请输入新密码" }]
  };
});
// const userName = ref("");
function editPwd() {
  centerDialogVisible.value = true;
}
const userName = computed(() => {
  return useUserInfoStore.userInfoData.entity?.name || userInfo.value?.name || "";
});

// 确认修改密码函数
function confirmEditPwd() {
  if (
    editPwdData.value.oldPwd === "" ||
    editPwdData.value.newPwd === "" ||
    editPwdData.value.conNewpwd === ""
  ) {
    ElMessage.error("表单输入项不能为空");
    return;
  }
  centerDialogVisible.value = false;

  // 生成 16 字节的 SM4 密钥
  const sm4Key = uuidv4().replace(/-/g, "");
  // 用 SM2 加密 SM4 密钥
  const sm2PublicKey =
    "04D969AAF5ECCFFFC381674E6C3E46A0F81B761F162A6EED0795136F0200C7C750273BBC393BADC723685F81E1137D5BCC270B7341D01574E6C4943BD56AF11E55";
  const encryptedOldPwd = sm4.encrypt(editPwdData.value.oldPwd.trim(), sm4Key);
  const encryptedNewPwd = sm4.encrypt(editPwdData.value.newPwd.trim(), sm4Key);
  const desUuid = sm2.doEncrypt(sm4Key, sm2PublicKey, 1);

  editPassword({
    oldPassword: encryptedOldPwd,
    password: encryptedNewPwd,
    aesKey: desUuid
  })
    .then((res: IBaseResult) => {
      if (res.code === 0) {
        ElMessage({
          message: "修改密码成功，请重新登陆",
          type: "success",
          duration: 2000
        });
        setTimeout(() => {
          router.push("/login"); // 跳转到登陆页
        }, 2000);
        sessionStorage.clear();
      }
      // console.log(res);
    })
    .catch(err => {
      console.log(err);
    });
}
// 将密码清空
watch(centerDialogVisible, val => {
  if (!val) {
    editPwdData.value.oldPwd = "";
    editPwdData.value.newPwd = "";
    editPwdData.value.conNewpwd = "";
  }
});
function logout() {
  logOut()
    .then(() => {
      sessionStorage.clear();
      useSubMenuStore().reset();
      router.push("/login");
    })
    .catch(err => {
      console.log(err);
    });
}

const isAdmin = ref(false);
onMounted(async () => {
  if (!useUserInfoStore.userInfoData.entity?.name) {
    try {
      const res = await getUserInfo({});
      if (res.code === 0) {
        userInfo.value = res.entity || {};
        useUserInfoStore.setUserInfo(res.entity || {});
        // 看roles是否包含"PLATFORM-ADMIN"
        isAdmin.value = res.entity?.roles?.includes("PLATFORM-ADMIN");
        if (isAdmin.value) {
          await getTenantList();
        }
      }
    } catch (error) {
      console.log(error);
    }
  }
});
</script>

<style lang="scss" scoped>
.user-avatar {
  width: 30px;
  height: 30px;
  background: #2c68ff;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
}
.icon-c {
  color: #fff;
}
// :deep(.el-form-item__content) {
//   justify-content: center;
// }

.tenant-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: space-between;
}

.tenant-card {
  height: 65px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 5px 16px;
  width: 48%;
  cursor: pointer;
  transition:
    border-color 0.3s,
    box-shadow 0.3s;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  .tenant-card-text {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    margin-left: 20px;
  }

  .el-radio {
    width: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  .el-radio__text {
    width: 150px;
    height: 100%;
    line-height: 30px;
    text-align: left;
    display: inline-block;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.el-radio.is-checked .tenant-card {
  border-color: #409eff;
}
</style>
