<template>
  <div class="mt-20px">
    <el-menu
      :default-active="currentRoute.path"
      class="el-menu-vertical-demo"
      router
      :unique-opened="true"
    >
      <!-- system时使用分组菜单 -->
      <template v-if="currentMenuType === 'system'">
        <el-sub-menu v-for="group in groupedMenuItems" :key="group.title" :index="group.title">
          <template #title>{{ group.title }}</template>
          <el-menu-item
            v-for="item in group.items"
            :key="item.path"
            :index="item.path"
            @click="handleMenuIsLink(item)"
          >
            {{ item.meta?.title || "未命名" }}
          </el-menu-item>
        </el-sub-menu>
      </template>

      <!-- cozi时不使用分组菜单，直接渲染菜单项 -->
      <template v-else-if="currentMenuType === 'cozi'">
        <el-menu-item
          v-for="item in coziChildren"
          :key="item.path"
          :index="item.path"
          @click="handleMenuIsLink(item)"
        >
          {{ item.meta?.title || "未命名" }}
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const currentRoute = useRoute();

const handleMenuIsLink = (item: any) => {
  router.push(item.path);
};

// 从缓存中加载菜单
const cachedMenu = JSON.parse(localStorage.getItem("cachedMenu") || "[]");
const systemMenu = cachedMenu.find((item: any) => item.name === "system");
const coziMenu = cachedMenu.find((item: any) => item.name === "cozi");

// 菜单子项
const systemChildren = systemMenu?.children || [];
const coziChildren = coziMenu?.children || []; // cozi菜单子项

// 分组配置
const groupedMenus = [
  {
    title: "平台管理",
    keywords: ["tenant"]
  },
  {
    title: "组织架构",
    keywords: ["organList", "user", "appList"]
  },
  {
    title: "权限管控",
    keywords: ["role", "resources"]
  },
  {
    title: "模块设置",
    keywords: [
      "flow",
      "topology",
      "boce",
      "boceClient",
      "alert",
      "warnmanage",
      "syslog",
      "snmp",
      "snmpClient",
      "aiPrompt"
    ]
  },
  {
    title: "系统设置",
    keywords: ["warnShortMessage", "warnEmail", "loginLog", "operationLog"]
  },
  {
    title: "帮助文档",
    keywords: ["agent"]
  }
];

// 分组菜单项（仅system使用）
const groupedMenuItems = computed(() => {
  return groupedMenus
    .map(group => {
      const items = systemChildren.filter((item: any) =>
        group.keywords.some(keyword => item.path.includes(keyword))
      );
      return {
        title: group.title,
        items
      };
    })
    .filter(group => group.items.length > 0);
});

// 判断当前菜单类型（system/cozi）
const currentMenuType = computed(() => {
  const path = currentRoute.path;
  if (path.includes("system")) return "system";
  if (path.includes("cozi")) return "cozi";
  return "";
});

// 监听路由变化更新显示状态
watch(
  () => currentRoute.path,
  val => {
    checkUrlHasSys(val);
  }
);

onMounted(() => {
  checkUrlHasSys(currentRoute.path);
});

/**
 * 判断URL是否包含system，用于控制是否显示系统布局
 *
 * @param url 待判断的URL
 * @returns 无返回值，会更新showSysLayout.value的值
 */
function checkUrlHasSys(url: string) {
  showSysLayout.value = url.includes("system") || url.includes("cozi");
}

const showSysLayout = ref(false);
</script>

<style scoped>
.mt-20px {
  margin-top: 20px;
}
</style>
