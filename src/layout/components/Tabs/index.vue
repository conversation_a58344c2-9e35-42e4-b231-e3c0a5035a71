<template>
  <el-tabs
    v-if="cachedViews.length > 1"
    v-model="activeName"
    class="header-tabs"
    @tab-click="handleTabClick"
  >
    <template v-for="item in cachedViews" :key="item.path">
      <el-tab-pane :label="item.meta?.title" :name="item.path"></el-tab-pane>
    </template>
  </el-tabs>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import type { TabsPaneContext } from "element-plus";
import { useSubMenuStore } from "@/store/modules/tagsView";
import { storeToRefs } from "pinia";
const currentRoute = useRoute();
const subMenuStore = useSubMenuStore();
const { cachedViews } = storeToRefs(subMenuStore);
const activeName = ref("");
const router = useRouter();
// tab点击
const emit = defineEmits(["updateLayout"]);
const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  handleTabItem(tab.paneName as string);
  emit("updateLayout");
};
// 跳转路由
function handleTabItem(path: string) {
  router.push(path);
}
// 要先调用一次，将tab选中状态选中
watch(currentRoute, val => {
  subMenuStore.watchRouteTabList(val?.meta, val.path);
  // 如果是详情路由，加上这个标识
  if (val.meta.detailRouter) return;
  activeName.value = val.path;
});

/**
 * 根据最后一个斜杠（/）分割字符串，并返回前半部分字符串
 *
 * @param inputString 需要分割的字符串
 * @returns 返回前半部分字符串
 */

function splitByLastSlash(inputString: string) {
  const lastIndex = inputString.lastIndexOf("/");
  if (lastIndex === -1) {
    // 如果字符串中没有 / ，则返回整个字符串
    return inputString;
  }
  // 返回 / 前面的部分
  return inputString.substring(0, lastIndex);
}
onMounted(() => {
  // 如果是详情页面，这里为了将tab的下划线加上，向前截取一段字符串
  const str = splitByLastSlash(currentRoute.path);
  if (cachedViews.value.some(view => view.path === str)) {
    activeName.value = str;
    return;
  }
  subMenuStore.watchRouteTabList(currentRoute?.meta, currentRoute.path);
  activeName.value = currentRoute.path;
});
</script>

<style lang="scss" scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  max-width: 200px;
}
.header-tabs {
  background-color: #fff;
  margin: 0px 0px;
  .el-tabs__nav {
    justify-content: center;
  }
}
.header-tabs > .el-tabs__content {
  padding: 32px;
  display: inline;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
  text-align: center;
}
</style>
