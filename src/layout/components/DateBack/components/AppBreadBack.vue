<template>
  <template v-if="cachedViews.length > 1 && !showBackBtn">
    <el-select
      v-model="optionValue"
      placeholder="请选择"
      style="width: 160px"
      @change="handleChange"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </template>
  <div v-else class="flex items-center date-back-title">
    <svg-icon name="back" @click="emit('goBack')" class="mr-3 cursor-pointer text-xl" />
    <div class="text-xl mr-3 date-back-desc mt--5p bread-title">
      {{ useBreadcrumbStore.breadcrumbTitle }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSubMenuStore } from "@/store/modules/tagsView";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const useBreadcrumbStore = breadcrumbStore();
const subMenuStore = useSubMenuStore();
const { cachedViews } = storeToRefs(subMenuStore);

// 定义事件和属性
const emit = defineEmits<{
  (e: "goBack"): void;
  (e: "changeOption", value: string): void;
}>();

const props = defineProps<{
  showBackBtn: boolean;
  options: Array<{ value: string; label: string }>;
}>();

// 使用响应式引用
const optionValue = ref<string>("");

// 处理选项更改
const handleChange = () => {
  emit("changeOption", optionValue.value);
  updateAppOption(optionValue.value);
};

// 更新应用选项
const updateAppOption = (value: string) => {
  if (useBreadcrumbStore.appOption !== value) {
    if (useBreadcrumbStore.startRequest) {
      useBreadcrumbStore.setAppOption(value);
    }
    emit("changeOption", optionValue.value);
  }
};

// 监视选项变化
watch(
  () => props.options,
  newOptions => {
    const currentAppOption = useBreadcrumbStore.appOption;
    const remberTime = Number(serTimeStore.serviceTimeData.remberTime);
    if (currentAppOption && remberTime) {
      optionValue.value = currentAppOption;
      updateAppOption(currentAppOption);
    } else if (newOptions.length > 0) {
      optionValue.value = currentAppOption || newOptions[0].value;
      if (currentAppOption !== optionValue.value) {
        updateAppOption(optionValue.value);
      }
    } else {
      optionValue.value = "";
      updateAppOption(optionValue.value);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.date-back {
  align-items: center;
  &-desc {
    font-weight: 500;
    font-size: 20px;
    color: #333333;
  }
  &-title {
    :deep(.el-divider--vertical) {
      border-left: 2px #3275f9 var(--el-border-style);
      height: 0.9em;
      margin: 0px 8px 0px 2px;
    }
  }
  &-wrap {
    :deep(.el-divider--vertical) {
      border-left: 2px #3275f9 var(--el-border-style);
      height: 0.9em;
    }
  }
}
.bread-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
