<template>
  <div class="date-picker-container flex">
    <el-date-picker
      v-model="selectedRange"
      :style="{ width: width }"
      :shortcuts="shortcuts"
      v-bind="$attrs"
      type="datetimerange"
      :clearable="false"
      range-separator="至"
      value-format="YYYY-MM-DD HH:mm:ss"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      placeholder="请选择日期"
      :default-value="[...defaultValue]"
      @focus="handleFocus"
      @change="handleChange"
    />
    <el-checkbox v-model="remberTime" :true-value="1" :false-value="0" @change="remberTimeChange"
      >记住时间</el-checkbox
    >
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { serviceTimeStore } from "@/store/modules/global";

const serTimeStore = serviceTimeStore();
const props = defineProps({
  width: { type: String, default: "400px" }
});
// 时间选择器选中的值
const selectedRange = ref<string[]>([]);
// 初始化的空值
const initTime = ref<string[]>([]);
const emit = defineEmits(["changeDate"]);

// Methods
const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const now = new Date();
const oneWeekAgo = new Date(now);
oneWeekAgo.setDate(now.getDate() - 7);
const defaultValue = ref([
  formatDateTime(dayjs(oneWeekAgo).startOf("day").toDate()),
  formatDateTime(dayjs(now).endOf("day").toDate())
]);

const setDefaultValue = () => {
  //从store中取到服务时间
  remberTime.value = Number(serTimeStore.serviceTimeData.remberTime);
  if (remberTime.value) {
    selectedRange.value = [
      serTimeStore.serviceTimeData.start_time,
      serTimeStore.serviceTimeData.end_time
    ];
  } else {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000); // 最近的五分钟
    selectedRange.value = [formatDateTime(fiveMinutesAgo), formatDateTime(now)];
  }
  serTimeStore.setServiceTIme(selectedRange.value, remberTime.value); // 设置全局的服务器时间
};

const createShortcut = (text: string, minutes: number) => ({
  text,
  value: () => {
    const end = new Date();
    const start = new Date();
    start.setMinutes(end.getMinutes() - minutes);
    return [start, end];
  }
});

function handleFocus() {
  initTime.value = selectedRange.value;
  // selectedRange.value = defaultValue.value;
}

const disabledDate = (time: { getTime: () => number }) => {
  const now = new Date();
  const oneMonthAgoPlusOneDay = new Date();
  oneMonthAgoPlusOneDay.setMonth(now.getMonth() - 1);
  oneMonthAgoPlusOneDay.setDate(now.getDate() - 1);
  return time.getTime() < oneMonthAgoPlusOneDay.getTime() || time.getTime() > now.getTime();
};

const handleChange = () => {
  if (selectedRange.value === initTime.value) return;
  serTimeStore.setServiceTIme(selectedRange.value, remberTime.value); // 设置全局的服务器时间
  emit("changeDate", selectedRange.value);
};
const remberTime = ref(0);

const remberTimeChange = () => {
  serTimeStore.setServiceTIme(selectedRange.value, remberTime.value);
};
// Shortcuts
const shortcuts = [
  createShortcut("最近5分钟", 5),
  createShortcut("最近10分钟", 10),
  createShortcut("最近半小时", 30),
  createShortcut("最近一小时", 60),
  createShortcut("最近一天", 24 * 60),
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 7);
      return [start, end];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(end.getMonth() - 1);
      return [start, end];
    }
  }
];

// Lifecycle hooks
onMounted(() => {
  setDefaultValue();
  // selectedRange.value = defaultValue.value;
  // handleChange();
});
</script>

<style lang="scss" scoped>
.date-picker-container {
  margin-right: 10px;
  margin-left: 10px;
  box-sizing: border-box;
}

.el-checkbox {
  vertical-align: middle;
  margin-left: 15px;
}
</style>
