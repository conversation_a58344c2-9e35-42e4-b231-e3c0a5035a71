<template>
  <el-container class="common-layout">
    <el-header>
      <Header @appChange="updateLayout"></Header>
    </el-header>
    <el-container class="overflow-auto pb-10px">
      <el-aside v-if="showSysLayout">
        <SysMenu />
      </el-aside>
      <el-header v-else class="main-header">
        <DateBack @dateChange="updateLayout" @optionChange="updateLayout"></DateBack>
        <Tabs></Tabs>
      </el-header>
      <!-- TODO:如果是系统界面的时候，验证下有没有东西展示出来 -->
      <el-main
        v-if="useApplicationStore.appId || showSysLayout"
        :class="[{ 'el-other-main': !showSysLayout }]"
      >
        <router-view>
          <template #default="{ Component }">
            <!-- <keep-alive :include="cachedViews"> 先去掉缓存看看 -->
            <component :is="Component" :key="componentKey" />
            <!-- </keep-alive> -->
          </template>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
const currentRoute = useRoute();
const componentKey = ref(0);
const showSysLayout = ref(false);
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
watch(
  () => currentRoute.path,
  val => {
    checkUrlHasSys(val);
  }
);
onMounted(() => {
  checkUrlHasSys(currentRoute.path);
});
/**
 * 判断URL是否包含system，用于控制是否显示系统布局
 *
 * @param url 待判断的URL
 * @returns 无返回值，会更新showSysLayout.value的值
 */
function checkUrlHasSys(url: string) {
  showSysLayout.value = url.includes("system") || url.includes("cozi");
}

function updateLayout() {
  componentKey.value++;
}
</script>

<style lang="scss" scoped>
.common-layout {
  width: 100%;
  height: 100%;
  min-width: 800px;
}
.el-header {
  height: auto;
  border-bottom: 1px solid var(--el-menu-border-color);
  padding-bottom: 1px;
  border-top: none;
  // color: #fff;
  background-color: #445fde;
  // min-width: fit-content;
}
.el-main {
  background-color: #fff;
  padding: 20px;
  overflow: auto;
  // min-width: fit-content;
}
.main-header {
  background-color: #fff;
  border-bottom: unset;
  padding: 10px 20px 0px 20px;
  // min-width: fit-content;
}
.el-aside {
  width: 200px;
  border-right: 1px solid RGBA(225, 228, 233, 1);
}
.el-other-main {
  padding: 0px 20px 4px 20px;
  // min-width: fit-content;
}
</style>
