<template>
  <div class="log-container">
    <div class="search-container">
      <el-form :model="pageParams">
        <el-form-item class="input-group">
          <label>URL：</label>
          <el-select placeholder="请选择URL" v-model="pageParams.url" clearable filterable>
            <el-option
              label="/api/user_apps?page=1&rows=10&name=&organId="
              value="/api/user_apps?page=1&rows=10&name=&organId="
            ></el-option>
            <el-option
              label="/api/sys/organ/trees?name=&status=1"
              value="/api/sys/organ/trees?name=&status=1"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>请求方法：</label>
          <el-select
            placeholder="请选择请求方法"
            v-model="pageParams.httpmethod"
            clearable
            filterable
          >
            <el-option label="get" value="GET"></el-option>
            <el-option label="post" value="POST"></el-option>
            <el-option label="put" value="PUT"></el-option>
            <el-option label="delete" value="DELETE"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>下游组件：</label>
          <el-select
            placeholder="请选择下游组件"
            v-model="pageParams.downstream"
            clearable
            filterable
          >
            <el-option label="CSEConsumer集群" value="CSEConsumer集群"></el-option>
            <el-option label="ApacheHttpClient连接池" value="ApacheHttpClient连接池"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1"> 查询 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper mb-10px">
        <BaseEcharts :options="options1" height="250px" v-loading="StatRequestCountLoading" />
        <BaseEcharts :options="options2" height="250px" v-loading="StatErrorCountLoading" />
        <BaseEcharts :options="options3" height="250px" v-loading="StatAvgCountLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <my-column property="url" label="URL" width="300">
          <template #default="{ row }">
            <span
              class="vertical-line"
              :class="{
                'error-bg': row.errorCount > 0,
                'success-bg': row.errorCount === 0
              }"
            ></span>
            <span style="min-width: 300px">
              {{ row.url }}
            </span>
          </template>
        </my-column>
        <my-column property="method" label="请求方法" />
        <my-column property="downstream" label="下游组件" />
        <my-column property="callNumber" label="调用次数" />
        <my-column property="avgDuration" label="平均响应时间(ms)" />
        <my-column property="errorCount" label="错误次数" />
        <my-column property="concurrencyMax" label="最大并发" />
        <my-column property="callSlow" label="最慢调用(ms)" />
      </MyTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { applicationStore } from "@/store/modules/application";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { getStatRequestCount, getStatErrorCount, getStatAvgCount } from "@/api/service/chart";
const useApplicationStore = applicationStore();
const StatRequestCountLoading = ref(false); //请求统计loading
const StatErrorCountLoading = ref(false); //错误统计loading
const StatAvgCountLoading = ref(false); //耗时统计loading
const pageParams = reactive({
  downstream: "",
  url: "",
  httpmethod: ""
});
//请求数统计
const options1 = ref({});
function StatRequestCount() {
  StatRequestCountLoading.value = true;
  getStatRequestCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "调用次数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options1.value = getChartOptions(params);
        StatRequestCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatRequestCountLoading.value = false;
    });
}
//错误数统计
const options2 = ref({});
function StatErrorCount() {
  StatErrorCountLoading.value = true;
  getStatErrorCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#f56c6c"],
          titleType: "错误次数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options2.value = getChartOptions(params);
        StatErrorCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatErrorCountLoading.value = false;
    });
}
//耗时统计
const options3 = ref({});
function StatAvgCount() {
  StatAvgCountLoading.value = true;
  getStatAvgCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " ms",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "平均响应时间",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        options3.value = getChartOptions(params);
        StatAvgCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatAvgCountLoading.value = false;
    });
}
//修改每页条数
const handleSizeChange = (val: number) => {
  console.log(val);
};
//分页
const handleCurrentChange = (val: number) => {
  console.log(val);
};
const list = reactive({
  records: [
    {
      url: "/api/user_apps?page=1&rows=10&name=&organId=",
      method: "GET",
      downstream: "CSEConsumer集群",
      callNumber: "1",
      avgDuration: "35.00",
      errorCount: 5,
      concurrencyMax: "1",
      callSlow: "35"
    },
    {
      url: "/api/sys/organ/trees?name=&status=1",
      method: "GET",
      downstream: "ApacheHttpClient连接池",
      callNumber: "1",
      avgDuration: "12.00",
      errorCount: 0,
      concurrencyMax: "1",
      callSlow: "12"
    }
  ],
  total: 2
});
onMounted(() => {
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
});
</script>
<style lang="scss" scoped>
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    max-height: 320px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
