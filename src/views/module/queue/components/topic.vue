<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主题名称"
        v-model="pageParams.topic"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'groupCount && totalMessages && totalLag && maxLag && lastReportTime',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="topic" label="主题名称">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">{{ scope.row.topic }}</span>
      </template>
    </my-column>
    <my-column property="clusterName" label="集群名" />
    <my-column property="groupCount" label="消费者分组数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.groupCount).fixValue }}{{ formatNums(row.groupCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalMessages" label="总消息数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.totalMessages).fixValue }}{{ formatNums(row.totalMessages).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalLag" label="总Lag" sortable="custom" />
    <my-column property="maxLag" label="最大Lag" sortable="custom" />
    <my-column property="lastReportTime" label="最近上报时间" sortable="custom">
      <template #default="{ row }">
        {{ formatTime(row.lastReportTime) }}
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">查看详情</span>
      </template>
    </my-column>
  </MyTable>
  <div>
    <el-drawer v-model="detailVisible" :title="detailTitle" size="55%">
      <div v-loading="detailLoading">
        <div class="flex justify-between indicator-wrapper">
          <div v-if="!hasMessageTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'消息量趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="messageTrendChart" width="22vw" height="250px" />
          <div v-if="!hasPartitionTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'分区数趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="partitionTrendChart" width="22vw" height="250px" />
        </div>
        <div class="flex justify-between indicator-wrapper">
          <div v-if="!hasReplicaTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'副本分区数趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="replicaTrendChart" width="22vw" height="250px" />
          <div v-if="!hasUnderReplicatedTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'Under Replicated趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="underReplicatedTrendChart" width="22vw" height="250px" />
        </div>
        <div class="indicator-wrapper w-49.5%">
          <div v-if="!hasLagTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'Lag趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="lagTrendChart" width="22vw" height="250px" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { formatTime } from "@/utils/dateStr";
import { formatNums } from "@/utils/formatStr";
import { getTopicsPage, getTopicMultiTrend } from "@/api/module/queue";
import { serviceNameStore } from "@/store/modules/service";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import TitlecCom from "@/components/TitleCom/index.vue";
import { useRoute } from "vue-router";
const route = useRoute();
const useServiceNameStore = serviceNameStore();
//loading动画
const tableLoading = ref(false);
const detailLoading = ref(false);

// 图表配置
const messageTrendChart = ref({});
const partitionTrendChart = ref({});
const lagTrendChart = ref({});
const replicaTrendChart = ref({});
const underReplicatedTrendChart = ref({});

// 数据判断变量
const hasMessageTrendData = ref(false);
const hasPartitionTrendData = ref(false);
const hasLagTrendData = ref(false);
const hasReplicaTrendData = ref(false);
const hasUnderReplicatedTrendData = ref(false);
const pageParams = reactive({
  page: 1,
  rows: 10,
  clusterName: "",
  topic: "",
  sort: "",
  order: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
function loadData() {
  tableLoading.value = true;
  pageParams.clusterName = useServiceNameStore.serviceName;
  pageParams.topic = pageParams.topic.trim();
  getTopicsPage(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const detailVisible = ref(false); //抽屉是否显示
const detailTitle = ref(""); //抽屉标题
const showDetail = (row: any) => {
  detailVisible.value = true;
  detailTitle.value = "指标信息 【" + row.topic + "】";
  loadTopicTrendData(row);
};

// 加载Topic趋势数据
const loadTopicTrendData = (row: any) => {
  detailLoading.value = true;
  const params = {
    topic: row.topic,
    clusterName: useServiceNameStore.serviceName
  };
  getTopicMultiTrend(params)
    .then(response => {
      if (response.code === 0) {
        if (response.entity && response.entity.multiTrend) {
          updateCharts(response.entity.multiTrend);
          detailLoading.value = false;
        }
      }
    })
    .catch(error => {
      console.error("获取Topic趋势数据失败:", error);
      detailLoading.value = false;
    })
    .finally(() => {
      detailLoading.value = false;
    });
};

// 更新图表数据
const updateCharts = (multiTrend: any) => {
  // 重置数据判断变量
  hasMessageTrendData.value = false;
  hasPartitionTrendData.value = false;
  hasLagTrendData.value = false;
  hasReplicaTrendData.value = false;
  hasUnderReplicatedTrendData.value = false;

  // 消息量趋势图
  if (
    multiTrend.kafka_topic_partition_current_offset &&
    multiTrend.kafka_topic_partition_current_offset.length > 0
  ) {
    const times = multiTrend.kafka_topic_partition_current_offset.map((item: any) =>
      formatTime(item.ts)
    );
    const sumValues = multiTrend.kafka_topic_partition_current_offset.map(
      (item: any) => item.sumValue
    );
    const avgValues = multiTrend.kafka_topic_partition_current_offset.map(
      (item: any) => item.avgValue
    );

    messageTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "消息量趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasMessageTrendData.value = true;
  }

  // 分区数趋势图
  if (multiTrend.kafka_topic_partitions && multiTrend.kafka_topic_partitions.length > 0) {
    const times = multiTrend.kafka_topic_partitions.map((item: any) => formatTime(item.ts));
    const sumValues = multiTrend.kafka_topic_partitions.map((item: any) => item.sumValue);
    const avgValues = multiTrend.kafka_topic_partitions.map((item: any) => item.avgValue);

    partitionTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "分区数趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasPartitionTrendData.value = true;
  }

  // Lag趋势图
  if (multiTrend.kafka_consumergroup_lag && multiTrend.kafka_consumergroup_lag.length > 0) {
    const times = multiTrend.kafka_consumergroup_lag.map((item: any) => formatTime(item.ts));
    const sumValues = multiTrend.kafka_consumergroup_lag.map((item: any) => item.sumValue);
    const avgValues = multiTrend.kafka_consumergroup_lag.map((item: any) =>
      item.avgValue.toFixed(2)
    );

    lagTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "Lag趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasLagTrendData.value = true;
  }

  // 副本分区数趋势图
  if (
    multiTrend.kafka_topic_partition_replicas &&
    multiTrend.kafka_topic_partition_replicas.length > 0
  ) {
    const times = multiTrend.kafka_topic_partition_replicas.map((item: any) => formatTime(item.ts));
    const sumValues = multiTrend.kafka_topic_partition_replicas.map((item: any) => item.sumValue);
    const avgValues = multiTrend.kafka_topic_partition_replicas.map((item: any) => item.avgValue);

    replicaTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "副本分区数趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasReplicaTrendData.value = true;
  }

  // Under Replicated趋势图
  if (
    multiTrend.kafka_topic_partition_under_replicated_partition &&
    multiTrend.kafka_topic_partition_under_replicated_partition.length > 0
  ) {
    const times = multiTrend.kafka_topic_partition_under_replicated_partition.map((item: any) =>
      formatTime(item.ts)
    );
    const sumValues = multiTrend.kafka_topic_partition_under_replicated_partition.map(
      (item: any) => item.sumValue
    );
    const avgValues = multiTrend.kafka_topic_partition_under_replicated_partition.map(
      (item: any) => item.avgValue
    );

    underReplicatedTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "Under Replicated趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasUnderReplicatedTrendData.value = true;
  }
};

onMounted(() => {
  // 如果路由带有topic参数，自动填充并搜索
  if (route.query.topic) {
    pageParams.topic = String(route.query.topic);
    search();
  } else {
    loadData();
  }
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
</style>
