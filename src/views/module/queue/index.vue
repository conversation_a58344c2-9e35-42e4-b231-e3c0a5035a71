<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入集群名"
        v-model="pageParams.clusterName"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'topicCount && groupCount && brokerCount && totalPartitions && totalMessages && lastReportTime',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="clusterName" label="集群名">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">{{ scope.row.clusterName }}</span>
      </template>
    </my-column>
    <my-column property="topicCount" label="主题数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.topicCount).fixValue }}{{ formatNums(row.topicCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="groupCount" label="消费者分组数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.groupCount).fixValue }}{{ formatNums(row.groupCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="brokerCount" label="节点数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.brokerCount).fixValue }}{{ formatNums(row.brokerCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalPartitions" label="总分区数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.totalPartitions).fixValue
          }}{{ formatNums(row.totalPartitions).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalMessages" label="总消息数" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.totalMessages).fixValue }}{{ formatNums(row.totalMessages).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="lastVersion" label="版本号" />
    <my-column property="lastReportTime" label="最近上报时间" sortable="custom">
      <template #default="{ row }">
        {{ formatTime(row.lastReportTime) }}
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">查看详情</span>
        <span class="divider"> / </span>
        <span class="operate" @click="dialogFormVisible = true">编辑别名</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="编辑别名"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form label-width="auto">
      <el-form-item label="别名：">
        <el-input placeholder="请输入别名" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { formatTime } from "@/utils/dateStr";
import { formatNums } from "@/utils/formatStr";
import { Search } from "@element-plus/icons-vue";
import { getKafkaMetricsList } from "@/api/module/queue";
import { useRouter } from "vue-router";
import { serviceNameStore } from "@/store/modules/service";

const router = useRouter();
const useServiceNameStore = serviceNameStore();

//loading动画
const tableLoading = ref(false);
const dialogFormVisible = ref(false);
const pageParams = reactive({
  page: 1,
  rows: 10,
  clusterName: "",
  sort: "",
  order: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

//搜索
function search() {
  pageParams.page = 1;
  loadData();
}

const list = reactive({
  records: [],
  total: 0
});

function loadData() {
  tableLoading.value = true;
  pageParams.clusterName = pageParams.clusterName.trim();
  getKafkaMetricsList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}

// 显示队列详情
const showDetail = (row: any) => {
  useServiceNameStore.setServiceName(row.clusterName);
  router.push("/module/queue/overview");
};

onMounted(() => {
  loadData();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
