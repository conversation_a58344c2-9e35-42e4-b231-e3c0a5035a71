<template>
  <div class="log-container">
    <div class="search-container">
      <el-form :model="pageParams">
        <el-form-item class="input-group">
          <label>容器类型：</label>
          <el-select placeholder="请选择容器类型" v-model="pageParams.name" clearable filterable>
            <el-option label="Tomcat" value="Tomcat"></el-option>
            <el-option label="WebLogic" value="WebLogic"></el-option>
            <el-option label="JBoss" value="JBoss"></el-option>
            <el-option label="Jetty" value="Jetty"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>端口名称：</label>
          <el-select placeholder="请选择端口名称" v-model="pageParams.type" clearable filterable>
            <el-option label="行情查询" value="行情查询"></el-option>
            <el-option label="apm-web" value="apm-web"></el-option>
            <el-option label="apm-collector" value="apm-collector"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1"> 查询 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper mb-10px">
        <BaseEcharts :options="options1" height="250px" v-loading="StatRequestCountLoading" />
        <BaseEcharts :options="options2" height="250px" v-loading="StatErrorCountLoading" />
        <BaseEcharts :options="options3" height="250px" v-loading="StatAvgCountLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <my-column property="version" label="容器类型">
          <template #default="{ row }">
            <span>
              {{ row.version }}
            </span>
          </template>
        </my-column>
        <my-column property="name" label="端口名称" />
        <my-column property="currentThreads" label="当前线程数" />
        <my-column property="busyThreads" label="当前繁忙线程数" />
        <my-column property="maxBusyThreads" label="最大繁忙线程数" />
        <my-column property="maxThreads" label="最大线程数" />
        <my-column property="currentConnect" label="当前连接数" />
        <my-column property="maxConnect" label="最大连接数" />
        <my-column property="connectValue" label="连接数峰值" />
      </MyTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { applicationStore } from "@/store/modules/application";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { getStatRequestCount, getStatErrorCount, getStatAvgCount } from "@/api/service/chart";
const useApplicationStore = applicationStore();
const StatRequestCountLoading = ref(false); //请求统计loading
const StatErrorCountLoading = ref(false); //错误统计loading
const StatAvgCountLoading = ref(false); //耗时统计loading
const pageParams = reactive({
  name: "",
  type: "",
  sql: ""
});
//请求数统计
const options1 = ref({});
function StatRequestCount() {
  StatRequestCountLoading.value = true;
  getStatRequestCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "当前线程数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options1.value = getChartOptions(params);
        StatRequestCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatRequestCountLoading.value = false;
    });
}
//错误数统计
const options2 = ref({});
function StatErrorCount() {
  StatErrorCountLoading.value = true;
  getStatErrorCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#f56c6c"],
          titleType: "当前繁忙线程数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options2.value = getChartOptions(params);
        StatErrorCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatErrorCountLoading.value = false;
    });
}
//耗时统计
const options3 = ref({});
function StatAvgCount() {
  StatAvgCountLoading.value = true;
  getStatAvgCount({ appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "当前连接数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options3.value = getChartOptions(params);
        StatAvgCountLoading.value = false;
      }
    })
    .catch(err => {
      console.log(err);
      StatAvgCountLoading.value = false;
    });
}
//修改每页条数
const handleSizeChange = (val: number) => {
  console.log(val);
};
//分页
const handleCurrentChange = (val: number) => {
  console.log(val);
};
const list = reactive({
  records: [
    {
      version: "Tomcat",
      name: "行情查询",
      currentThreads: "10",
      busyThreads: "0",
      maxBusyThreads: "23",
      maxThreads: "200",
      currentConnect: "8",
      maxConnect: "8192",
      connectValue: "762"
    },
    {
      version: "Jetty",
      name: "apm-web",
      currentThreads: "10",
      busyThreads: "0",
      maxBusyThreads: "1",
      maxThreads: "200",
      currentConnect: "1",
      maxConnect: "8192",
      connectValue: "2"
    }
  ],
  total: 2
});
onMounted(() => {
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
});
</script>
<style lang="scss" scoped>
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    max-height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
