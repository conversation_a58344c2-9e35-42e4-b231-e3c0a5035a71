<template>
  <div>
    <div class="chart-container" v-loading="loading">
      <BaseEcharts
        v-for="chart in chartConfigs"
        :key="chart.metricName"
        :options="chart.options as any"
        height="250px"
        class="chart-item"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { getDevicesTrends } from "@/api/module/snmp";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { formatTime } from "@/utils/dateStr";

const loading = ref(false);
const trendData = ref<any[]>([]);

// 处理多折线数据
const processMultiLineData = (item: any) => {
  // 按子指标名称分组
  const subMetrics: { [key: string]: any[] } = {};

  item.metricTrends.forEach((trend: any) => {
    const subMetricName = trend.metric_name;
    if (!subMetrics[subMetricName]) {
      subMetrics[subMetricName] = [];
    }
    subMetrics[subMetricName].push({
      timestamp: trend.timestamp,
      value: trend.value
    });
  });

  // 获取所有时间点并排序
  const allTimestamps = [...new Set(item.metricTrends.map((t: any) => t.timestamp))].sort();

  // 为每个子指标生成数据系列
  const seriesData: number[][] = [];
  const seriesNames: string[] = [];

  // 基础颜色数组
  const baseColors = [
    "#5470C6",
    "#91CC75",
    "#FAC858",
    "#EE6666",
    "#73C0DE",
    "#3BA272",
    "#FC8452",
    "#9A60B4"
  ];

  // 生成更多颜色的函数
  const generateColors = (count: number) => {
    if (count <= baseColors.length) {
      return baseColors.slice(0, count);
    }

    // 当需要更多颜色时，使用HSL色彩空间生成
    const colors = [...baseColors];
    const additionalCount = count - baseColors.length;

    for (let i = 0; i < additionalCount; i++) {
      // 使用不同的色相角度生成颜色
      const hue = (i * 137.5) % 360; // 黄金角度，确保颜色分布均匀
      const saturation = 60 + (i % 20); // 60-80%饱和度
      const lightness = 45 + (i % 15); // 45-60%亮度

      const color = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
      colors.push(color);
    }

    return colors;
  };

  Object.keys(subMetrics).forEach((subMetricName, index) => {
    const subMetricData = subMetrics[subMetricName];
    const values: number[] = [];

    // 为每个时间点填充数据
    allTimestamps.forEach(timestamp => {
      const dataPoint = subMetricData.find((d: any) => d.timestamp === timestamp);
      values.push(dataPoint ? dataPoint.value : 0);
    });

    seriesData.push(values);
    seriesNames.push(subMetricName);
  });

  // 根据子指标数量生成颜色
  const colors = generateColors(seriesNames.length);

  return {
    times: allTimestamps.map(t => formatTime(t as string)),
    seriesData,
    seriesNames,
    colors
  };
};

// 图表配置
const chartConfigs = computed(() => {
  return trendData.value.map(item => {
    const processedData = processMultiLineData(item);

    return {
      metricName: item.metricName,
      options: getChartOptions({
        color: processedData.colors,
        titleType: `${item.metricName}趋势`,
        originalTimes: processedData.times,
        seriesData: processedData.seriesData,
        names: processedData.seriesNames,
        type: "line",
        areaStyle: false,
        legend: { show: true },
        name: ""
      })
    };
  });
});

// 获取设备趋势数据
async function devicesTrends() {
  loading.value = true;
  try {
    const response = await getDevicesTrends({});
    if (response.code === 0) {
      trendData.value = response.records || [];
    }
  } catch (error) {
    console.error("获取设备趋势数据异常:", error);
    loading.value = false;
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  devicesTrends();
});
</script>

<style lang="scss" scoped>
.chart-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  // margin-top: 8px;
}

.chart-item {
  flex: 0 0 calc(33.333% - 7px);
  min-width: 300px;
}
</style>
