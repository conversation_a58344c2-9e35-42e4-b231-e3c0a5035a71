<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入实例ID"
        v-model="pageParams.instanceId"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'timestamp && statusConnections && errorRate && avgDuration',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="instanceId" label="实例ID">
      <template #default="scope">
        <span
          class="vertical-line"
          :class="{
            'error-bg': scope.row.up == 0,
            'success-bg': scope.row.up == 1
          }"
        ></span>
        <span class="service-name" @click="showAgg(scope.row)">
          {{ scope.row.instanceId }}
        </span>
      </template>
    </my-column>
    <my-column property="up" label="运行状态">
      <template #default="{ row }">
        <el-tag :type="row.up == 1 ? 'success' : 'danger'">
          {{ row.up == 1 ? "正常" : "异常" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="version" label="MySQL版本" />
    <my-column property="versionComment" label="版本注释" />
    <my-column property="timestamp" label="采集时间" sortable="custom">
      <template #default="{ row }">
        {{ formatTime(row.timestamp) }}
      </template>
    </my-column>
    <my-column property="statusConnections" label="当前连接数" sortable="custom" />
    <my-column property="statusThreadsRunning" label="活跃线程数" sortable="custom" />
    <my-column property="statusSlowQueries" label="慢查询数" sortable="custom" />
    <my-column property="variablesMaxConnections" label="最大连接数" sortable="custom" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="showAgg(scope.row)">查看详情</span>
        <span class="divider"> / </span>
        <span class="operate" @click="dialogFormVisible = true">编辑别名</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="编辑别名"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form label-width="auto">
      <el-form-item label="别名：">
        <el-input placeholder="请输入别名" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { formatTime } from "@/utils/dateStr";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { getMysqlList } from "@/api/module/database";
import { useRouter } from "vue-router";
const router = useRouter();
import { serviceNameStore } from "@/store/modules/service";
const useServiceNameStore = serviceNameStore();

//loading动画
const tableLoading = ref(false);
const dialogFormVisible = ref(false);

//列表参数
const pageParams = reactive({
  instanceId: "",
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
}

const list = reactive({
  records: [],
  total: 0
});

//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.instanceId = pageParams.instanceId.trim();
  getMysqlList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}

// 显示MySQL实例详情
const showAgg = (row: any) => {
  useServiceNameStore.setServiceName(row.instanceId);
  router.push("/module/database/overview");
};

onMounted(() => {
  loadData();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.indicator-wrapper {
  margin-bottom: 8px;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
