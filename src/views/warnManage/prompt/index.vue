<template>
  <div>
    <!-- 表格头部 -->
    <div class="table-header">
      <div></div>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>

    <MyTable
      :data="promptList"
      :total="total"
      v-loading="tableLoading"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <my-column property="name" label="名称" />
      <my-column property="content" label="内容" />
      <my-column property="version" label="版本" />
      <my-column property="scope" label="作用域" />
      <my-column property="owner_id" label="所属ID" />
      <my-column property="is_active" label="是否激活">
        <template #default="{ row }">
          <el-tag :type="row.is_active !== true ? 'danger' : 'success'">
            {{ row.is_active == true ? "是" : "否" }}
          </el-tag>
        </template>
      </my-column>
      <my-column property="created_at" label="创建时间">
        <template #default="{ row }">
          <span>{{ formatTime(row.created_at) }}</span>
        </template>
      </my-column>
      <my-column property="updated_at" label="更新时间">
        <template #default="{ row }">
          <span>{{ formatTime(row.updated_at) }}</span>
        </template>
      </my-column>
      <my-column label="操作" width="120" align="center" header-align="center">
        <template #default="{ row }">
          <span class="workflow-name" @click="handleDelete(row)">删除</span>
        </template>
      </my-column>
    </MyTable>

    <!-- 新增弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增Prompt模板组"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称" clearable />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="6"
            placeholder="请输入内容"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="温馨提示"
      width="500"
      :close-on-click-modal="false"
    >
      <span>确认要删除prompt「{{ currentDeleteItem?.name }}」吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmDelete" :loading="deleteLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { formatTime } from "@/utils/dateStr";
import { reactive, ref, onMounted } from "vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import {
  getPrompTtemplateGroups,
  createPromptTemplateGroup,
  deletePromptTemplateGroup
} from "@/api/workflow/index";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
const tableLoading = ref(false);
const promptList = ref([]);
const total = ref(0);
const pageParams = reactive({
  page: 1,
  page_size: 10
});

// 新增相关变量
const dialogVisible = ref(false);
const submitLoading = ref(false);
const formRef = ref<FormInstance>();

const formData = reactive({
  name: "",
  content: ""
});

const formRules: FormRules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  content: [{ required: true, message: "请输入内容", trigger: "blur" }]
};

// 删除相关变量
const deleteDialogVisible = ref(false);
const deleteLoading = ref(false);
const currentDeleteItem = ref<any>(null);
// 分页处理
function handleCurrentChange(page: number) {
  pageParams.page = page;
  loadData();
}

function handleSizeChange(size: number) {
  pageParams.page_size = size;
  loadData();
}
function loadData() {
  tableLoading.value = true;
  getPrompTtemplateGroups(pageParams)
    .then(response => {
      if (response && response.data) {
        promptList.value = response.data.items || [];
        total.value = response.data.total || response.data.items?.length || 0;
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});

// 新增相关方法
const handleAdd = () => {
  dialogVisible.value = true;
  formData.name = "";
  formData.content = "";
};

const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    const response = await createPromptTemplateGroup({
      name: formData.name,
      content: formData.content
    });

    // 检查响应状态，201表示创建成功
    if (response && (response.code === 201 || response.code === 200)) {
      ElMessage.success("新增成功");
      handleClose();
      loadData(); // 重新加载数据
    } else {
      ElMessage.error(response?.message || "新增失败");
    }
  } catch (error) {
    console.error("新增失败:", error);
    ElMessage.error("新增失败");
  } finally {
    submitLoading.value = false;
  }
};

// 删除相关方法
const handleDelete = (row: any) => {
  currentDeleteItem.value = row;
  deleteDialogVisible.value = true;
};

const confirmDelete = async () => {
  if (!currentDeleteItem.value) return;

  try {
    deleteLoading.value = true;

    await deletePromptTemplateGroup(currentDeleteItem.value.template_group_id);

    // 删除成功
    ElMessage.success("删除成功");
    deleteDialogVisible.value = false;
    currentDeleteItem.value = null;
    loadData();
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  } finally {
    deleteLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  // padding: 16px;
}

.workflow-name {
  color: #0064c8;
  cursor: pointer;
}
</style>
