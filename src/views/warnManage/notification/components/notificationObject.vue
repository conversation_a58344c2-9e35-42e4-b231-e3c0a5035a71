<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="pageParams.name"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="addObject">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="name" label="姓名" />
    <my-column property="mobile" label="手机号码" />
    <my-column property="email" label="邮箱" />
    <my-column property="createdAt" label="创建时间" />
    <my-column property="remark" label="备注" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="220">
      <template #default="scope">
        <span class="operate" @click="editObject(scope.row)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="deleteObject(scope.row.id)">删除</span>
        <span class="divider"> / </span>
        <span class="operate" @click="bindGroup(scope.row)">绑定通知组</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 新增编辑弹窗-->
  <el-dialog
    v-model="dialogFormVisible"
    :align-center="true"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="姓名：" required>
        <el-input v-model="state.form.name" clearable />
      </el-form-item>
      <el-form-item label="手机号码：" required>
        <el-input v-model="state.form.mobile" clearable />
      </el-form-item>
      <el-form-item label="邮箱：" required>
        <el-input v-model="state.form.email" clearable />
      </el-form-item>
      <el-form-item label="备注：">
        <el-input v-model="state.form.remark" type="textarea" clearable :rows="5" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="formCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="删除通知对象"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--已绑定用户弹窗-->
  <el-dialog
    :align-center="true"
    draggable
    v-model="dialogVisible"
    :title="bindObjectTitle"
    :close-on-click-modal="false"
    width="1100"
    v-if="dialogVisible"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px"
          placeholder="请输入关键字"
          clearable
          v-model="dataToSend.name"
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchFor"
          >搜索</el-button
        >
      </div>
      <div>
        <el-button type="primary" @click="dialogTableVisible = true">添加绑定</el-button>
      </div>
    </div>
    <MyTable
      height="300"
      :data="userData"
      :total="userDataSize"
      style="width: 100%"
      @sizeChange="sizeChange"
      @currentChange="currentChange"
    >
      <my-column property="name" label="组名称" />
      <my-column property="remark" label="备注" />
      <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
        <template #default="scope">
          <span class="operate" @click="unbind(scope)">删除</span>
        </template>
      </my-column>
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--绑定用户弹窗-->
  <el-dialog
    :align-center="true"
    draggable
    v-model="dialogTableVisible"
    title="请选择通知组"
    :close-on-click-modal="false"
    width="900"
    v-if="dialogTableVisible"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px"
          placeholder="请输入关键字"
          v-model="pageParam.name"
          clearable
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchUser"
          >搜索</el-button
        >
      </div>
    </div>
    <MyTable
      height="300"
      :data="tableData"
      :total="totalSize"
      style="width: 100%"
      @sizeChange="SizeChange"
      @currentChange="CurrentChange"
      @selection-change="handleSelectionChange"
    >
      <my-column type="selection" />
      <my-column property="name" label="组名称" />
      <my-column property="remark" label="备注" />
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" @click="submit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--解绑弹窗-->
  <el-dialog
    :align-center="true"
    v-model="unbindVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确定要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="unbindVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBind"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import {
  notifierList,
  notifierAdd,
  notifierEdit,
  notifierDetail,
  notifierDelete,
  notifierByGroup,
  notifierBindGroup,
  notifierGroupList,
  unbindNotifier
} from "@/api/warnManage/notifier";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
//获取坐上角appid
const appId = useApplicationStore.appId;
//列表loading
const tableLoading = ref(false);
//新增编辑弹窗是否显示
const dialogFormVisible = ref(false);
//新增编辑弹窗标题
const title = ref("");
//删除弹窗是否显示
const deleteVisible = ref(false);
//绑定用户弹窗是否显示
const dialogVisible = ref(false);
const bindObjectTitle = ref("");
const dialogTableVisible = ref(false);
const unbindVisible = ref(false);
//新增编辑删除数据
const state = reactive({
  delId: null as any,
  form: {
    name: "",
    mobile: "",
    email: "",
    remark: ""
  }
} as any);
//列表参数
const pageParams = reactive({
  name: "",
  page: 1,
  rows: 10,
  appid: appId
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.name = pageParams.name.trim();
  notifierList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
// const tableData = [
//   {
//     name: "陈傲迪",
//     mobile: "18843107185",
//     email: "<EMAIL>",
//     createdAt: "2024-12-19 14:19:25",
//     remark: "测试数据"
//   }
// ];
//点击新增
const addObject = () => {
  dialogFormVisible.value = true;
  title.value = "新增通知对象";
  state.form = {
    name: "",
    mobile: "",
    email: "",
    remark: ""
  };
};
//点击编辑
const editObject = (row: any) => {
  dialogFormVisible.value = true;
  title.value = "编辑通知对象";
  notifierDetail(row.id).then(res => {
    state.form = res.entity;
  });
};
//新增编辑数据
function formCommit() {
  if (state.form.id) {
    notifierEdit(state.form).then(response => {
      if (response.code === 0) {
        ElMessage.success("修改成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  } else
    notifierAdd({ ...state.form, appid: appId }).then(response => {
      if (response.code === 0) {
        ElMessage.success("新增成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
}
function deleteObject(id: any) {
  state.delId = id;
  deleteVisible.value = true;
}
//删除数据
function delCommit() {
  deleteVisible.value = false;
  notifierDelete(state.delId).then(response => {
    if (response.code === 0) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(response.desc);
    }
  });
}
const dataToSend = reactive({
  page: 1,
  rows: 10,
  notifierId: "",
  appid: appId,
  name: ""
});
function bindGroup(row: any) {
  dialogVisible.value = true;
  bindObjectTitle.value = "【" + row.name + "】已绑定的通知组";
  bindId.value = row.id;
  dataToSend.notifierId = row.id;
  userlist();
}
//修改每页条数
const sizeChange = (val: number) => {
  dataToSend.rows = val;
  userlist();
};
//分页
const currentChange = (val: number) => {
  dataToSend.page = val;
  userlist();
};
const userData = ref([]);
const userDataSize = ref(0);
function searchFor() {
  dataToSend.page = 1;
  userlist();
}
function userlist() {
  notifierByGroup(dataToSend).then(response => {
    if (response.code === 0) {
      userData.value = response.records;
      userDataSize.value = Number(response.total);
    }
  });
}
const ids: any = ref([]);
const bindId: any = ref("");
const handleSelectionChange = (val: any) => {
  let arr: any = [];
  val.forEach((element: any) => {
    arr.push(element.id);
  });
  ids.value = arr;
};
const submit = () => {
  if (ids.value.length == 0) {
    ElMessage.error("请选择要绑定的用户");
  } else {
    let groupIds = ids.value.join(",");
    notifierBindGroup(bindId.value, { groupIds }).then(response => {
      if (response.code === 0) {
        userlist();
        ElMessage.success("绑定成功");
        dialogTableVisible.value = false;
      } else {
        ElMessage.error(response.desc);
      }
    });
  }
};
const pageParam = reactive({
  appid: appId,
  page: 1,
  rows: 10,
  name: ""
});
//修改每页条数
const SizeChange = (val: number) => {
  pageParam.rows = val;
  fetchUserList();
};
//分页
const CurrentChange = (val: number) => {
  pageParam.page = val;
  fetchUserList();
};
const tableData = ref([]);
const totalSize = ref(0);
function searchUser() {
  pageParam.page = 1;
  fetchUserList();
}
function fetchUserList() {
  notifierGroupList(pageParam).then(response => {
    if (response.code === 0) {
      tableData.value = response.records;
      totalSize.value = Number(response.total);
    }
  });
}
const notifierId: any = ref("");
function unbind(scope: any) {
  unbindVisible.value = true;
  notifierId.value = scope.row.id;
}
const submitBind = () => {
  unbindNotifier(notifierId.value, bindId.value).then(response => {
    if (response.code === 0) {
      userlist();
      ElMessage.success("删除成功");
      unbindVisible.value = false;
    } else {
      ElMessage.error(response.desc);
    }
  });
};

onMounted(() => {
  fetchUserList();
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
