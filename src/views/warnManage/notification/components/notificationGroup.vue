<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="searchContent"
        clearable
      ></el-input>

      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="operate(1)">新增</el-button>
    </div>
  </div>
  <MyTable
    class="mt-20px"
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    style="width: 100%"
  >
    <my-column property="name" label="组名称" />
    <my-column property="createdAt" label="创建时间" />
    <my-column property="remark" label="备注" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="180">
      <template v-slot="{ row }">
        <span class="operate" @click="operate(2, row.id)">修改</span>
        <span class="divider"> / </span>
        <span class="operate" @click="operate(4, row.id)">管理对象</span>
        <span class="divider"> / </span>
        <span class="operate" @click="operate(3, row.id)">删除</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    draggable
    v-model="dialogFormVisible"
    :title="bindUserTitle"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    width="30%"
  >
    <el-form
      style="max-width: 500px; margin-left: 30px"
      :model="numberValidateForm"
      label-width="auto"
    >
      <el-form-item label="组名称：" prop="" required>
        <el-input v-model="numberValidateForm.name" type="text" style="width: 350px" />
      </el-form-item>
      <el-form-item label="备注：" prop="" required>
        <el-input
          v-model="numberValidateForm.remark"
          type="textarea"
          style="width: 350px"
          :autosize="{ minRows: 5, maxRows: 10 }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitContent"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="deleteVisible"
    title="删除组"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitContent"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--已绑定用户弹窗-->
  <el-dialog
    :align-center="true"
    draggable
    v-model="dialogVisible"
    :title="bindUserTitle"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    width="1100"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px; margin-left: 15px"
          placeholder="请输入关键字"
          v-model="pageParamOne.name"
          clearable
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchObjectList"
          >搜索</el-button
        >
      </div>
      <div>
        <el-button type="primary" @click="addBinding">添加绑定</el-button>
      </div>
    </div>
    <MyTable
      height="300"
      :data="listOne.records"
      :total="listOne.total"
      style="width: 100%"
      @sizeChange="sizeChange"
      v-loading="objectTableLoading"
      @currentChange="currentChange"
    >
      <MyColumn prop="name" label="姓名" />
      <MyColumn prop="mobile" label="手机号码" />
      <MyColumn prop="email" label="邮箱" />
      <MyColumn prop="createdAt" label="创建时间" />
      <MyColumn prop="remark" label="备注" />
      <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
        <template v-slot="{ row }">
          <span class="operate" @click="unbind(row)">删除</span>
        </template>
      </my-column>
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="closeDialog"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    draggable
    :align-center="true"
    v-model="dialogTableVisible"
    title="请选择对象"
    :close-on-click-modal="false"
    width="900"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px; margin-left: 15px"
          placeholder="请输入关键字"
          v-model="pageParamTwo.name"
          clearable
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchObject"
          >搜索</el-button
        >
      </div>
    </div>
    <MyTable
      height="300"
      :data="listTwo.records"
      :total="listTwo.total"
      style="width: 100%"
      :showSelect="true"
      @sizeChange="sizeChangeOne"
      @currentChange="currentChangeOne"
      @selection-change="handleSelectionChange"
    >
      <MyColumn prop="name" label="姓名" />
      <MyColumn prop="mobile" label="手机号码" />
      <MyColumn prop="email" label="邮箱" />
      <MyColumn prop="createdAt" label="创建时间" />
      <MyColumn prop="remark" label="备注" />
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" @click="submitObject"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible1"
    title="删除通知对象"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible1 = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  getNotifierGroup,
  addNotifierGroup,
  editNotifierGroup,
  delNotifierGroup,
  detailNotifierGroup,
  getBindNotifier,
  getNotifier,
  getNotifierGroupNotifier
} from "@/api/warnManage/group";
import { unbindNotifier } from "@/api/warnManage/notifier";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
import { Search } from "@element-plus/icons-vue";

const tableLoading = ref(false);
const objectTableLoading = ref(false);
const objectLoading = ref(false);
const list = reactive({
  records: [],
  total: 0
});
const listOne = reactive({
  records: [],
  total: 0
});
const listTwo = reactive({
  records: [],
  total: 0
});
//列表参数
const pageParams = reactive({
  appid: useApplicationStore.appId,
  name: "",
  page: 1,
  rows: 10
});

// 用于新增修改弹框
const numberValidateForm = reactive({
  appid: useApplicationStore.appId,
  id: "",
  name: "",
  remark: ""
});

// 已绑定对象列表参数
const pageParamOne = reactive({
  appid: useApplicationStore.appId,
  groupId: "",
  notifierId: "",
  name: "",
  page: 1,
  rows: 10
});

// 全部绑定对象列表参数
const pageParamTwo = reactive({
  appid: useApplicationStore.appId,
  notifierIds: "",
  id: "",
  name: "",
  page: 1,
  rows: 10
});
const searchContent = ref("");
const dialogFormVisible = ref(false);
const deleteVisible = ref(false);
const deleteVisible1 = ref(false);
const dialogVisible = ref(false);
const dialogTableVisible = ref(false);
const bindUserTitle = ref("");

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  getnotificationList();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  getnotificationList();
};
// 查询
const search = () => {
  pageParams.name = searchContent.value.trim();
  getnotificationList();
};
// 获取通知组
const getnotificationList = async () => {
  tableLoading.value = true;
  try {
    const res = await getNotifierGroup(pageParams);
    if (res.code === 0) {
      list.records = res.records;
      list.total = Number(res.total);
    }
    tableLoading.value = false;
  } catch (err) {
    tableLoading.value = false;
    console.error(err);
  }
};

//修改每页条数
const sizeChange = (val: number) => {
  pageParamOne.rows = val;
  getObjectList();
};
//分页
const currentChange = (val: number) => {
  pageParamOne.page = val;
  getObjectList();
};
// 查询
const searchObjectList = () => {
  pageParamOne.name = pageParamOne.name.trim();
  getObjectList();
};
// 获取通知组对象
const getObjectList = async () => {
  objectTableLoading.value = true;
  try {
    const params = {
      appid: pageParamOne.appid,
      groupId: pageParamOne.groupId,
      name: pageParamOne.name,
      page: pageParamOne.page,
      rows: pageParamOne.rows
    };
    const res = await getNotifierGroupNotifier(params);
    if (res.code === 0) {
      listOne.records = res.records;
      listOne.total = Number(res.total);
    }
    objectTableLoading.value = false;
  } catch (err) {
    objectTableLoading.value = false;
    console.error(err);
  }
};

const addBinding = () => {
  getObject();
  dialogTableVisible.value = true;
};
// 查询
const searchObject = () => {
  pageParamTwo.name = pageParamTwo.name.trim();
  getObject();
};
//修改每页条数
const sizeChangeOne = (val: number) => {
  pageParamTwo.rows = val;
  getObject();
};
//分页
const currentChangeOne = (val: number) => {
  pageParamTwo.page = val;
  getObject();
};
// 获取对象
const getObject = async () => {
  objectLoading.value = true;
  try {
    const res = await getNotifier(pageParamTwo);
    if (res.code === 0) {
      listTwo.records = res.records;
      listTwo.total = Number(res.total);
    }
    objectLoading.value = false;
  } catch (err) {
    objectLoading.value = false;
    console.error(err);
  }
};

// 选择对象
const appIds = ref([]);
const handleSelectionChange = (selectedRows: any) => {
  let appIdsArr = [];
  appIdsArr = selectedRows.map((item: any) => item.id);
  appIds.value = appIdsArr.join(",");
};
const submitObject = async () => {
  pageParamTwo.notifierIds = appIds.value;
  pageParamTwo.id = pageParamOne.groupId;
  try {
    const res = await getBindNotifier(pageParamTwo);
    if (res.code === 0) {
      ElMessage.success("绑定成功");
      getObjectList();
      dialogTableVisible.value = false;
      pageParamTwo.id = pageParamTwo.name = pageParamTwo.notifierIds = "";
      pageParamTwo.rows = 10;
      pageParamTwo.page = 1;
    }
  } catch (err) {
    ElMessage.error("绑定失败");
    console.error(err);
  }
};
// 操作
const operantClass = ref("");
const operationMapping = {
  1: { title: "添加通知组", class: "add", dialogFormVisible: true },
  2: { title: "修改通知组", class: "updated", dialogFormVisible: true },
  3: { title: "删除", class: "delete", deleteVisible: true },
  4: { title: "管理对象", class: "manage", dialogVisible: true }
};
const operate = (val: number, typ = undefined) => {
  if (val === 2) {
    notifierGroupDetail(typ);
  }

  const operation = operationMapping[val];

  if (operation) {
    bindUserTitle.value = operation.title;
    operantClass.value = operation.class;
    dialogFormVisible.value = operation.dialogFormVisible ?? false;
    deleteVisible.value = operation.deleteVisible ?? false;
    dialogVisible.value = operation.dialogVisible ?? false;

    if (val === 3 || val === 2) {
      numberValidateForm.id = typ;
    } else if (val === 4) {
      pageParamOne.groupId = typ;
      getObjectList();
    }
  }
};

// 提交框
const submitContent = async () => {
  const operationActions = {
    add: addNotifierGroup,
    updated: editNotifierGroup,
    manage: detailNotifierGroup,
    delete: delNotifierGroup
  };
  const action = operationActions[operantClass.value];
  let res;
  if (action) {
    try {
      res = await action(numberValidateForm);
      if (res.code === 0) {
        ElMessage.success(`${bindUserTitle.value}成功`);
        getnotificationList();
        dialogFormVisible.value = false;
        deleteVisible.value = false;
      }
    } catch (err) {
      console.error(err);
    } finally {
      tableLoading.value = false;
    }
  }
};

// 通知组详情
const notifierGroupDetail = async val => {
  numberValidateForm.id = val;
  try {
    const res = await detailNotifierGroup(numberValidateForm);
    if (res.code === 0) {
      numberValidateForm.name = res.entity.name;
      numberValidateForm.remark = res.entity.remark;
    }
  } catch (err) {
    console.error(err);
  }
};
const unbind = (row: any) => {
  deleteVisible1.value = true;
  pageParamOne.notifierId = row.id;
};
//删除数据
const delCommit = async () => {
  try {
    const res = await unbindNotifier(pageParamOne.groupId, pageParamOne.notifierId);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      getObjectList();
      deleteVisible1.value = false;
    } else {
      ElMessage.error(res.desc);
    }
  } catch (err) {
    ElMessage.error("删除失败");
    console.error(err);
  }
};
// 关闭对话框
const closeDialog = () => {
  numberValidateForm.id = "";
  numberValidateForm.name = "";
  numberValidateForm.remark = "";
  searchContent.value = "";
  pageParamOne.groupId =
    pageParamOne.name =
    pageParamOne.notifierId =
    pageParamOne.notifierIds =
      "";
  pageParamOne.rows = 10;
  pageParamOne.page = 1;
  dialogFormVisible.value = false;
  deleteVisible.value = false;
  dialogVisible.value = false;
};

onMounted(() => {
  getnotificationList();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
