<template>
  <div class="container">
    <div class="left-section">
      <div class="ai-analysis-section">
        <div class="section-title">
          <span class="title-text">AI 错误分析</span>
        </div>
        <div class="analysis-content">
          <div class="input-area">
            <div class="model-select">
              <el-select
                v-model="aiModel"
                placeholder="选择AI模型"
                class="model-dropdown"
                clearable
              >
                <el-option value="deepseek" label="DeepSeek"> </el-option>
                <el-option value="文心一言" label="文心一言"> </el-option>
                <el-option value="通义千问" label="通义千问"> </el-option>
              </el-select>
            </div>
            <div class="input-section">
              <el-input
                type="textarea"
                v-model="messageValue"
                :rows="4"
                placeholder="请输入需要分析的错误信息..."
                class="message-input"
              />
              <el-button type="primary" class="analyze-btn" @click="handleAnalyze">
                <el-icon><Search /></el-icon>
                开始分析
              </el-button>
            </div>
          </div>
          <div class="result-area" v-loading="analyzing">
            <div class="result-title">
              <el-icon><Lightning /></el-icon>
              <span>AI 分析结果</span>
            </div>
            <div class="result-content">
              <template v-if="analysisResult">
                <div class="analysis-item">
                  <div class="item-label">错误原因：</div>
                  <div class="item-content">{{ analysisResult.cause }}</div>
                </div>
                <div class="analysis-item">
                  <div class="item-label">影响范围：</div>
                  <div class="item-content">{{ analysisResult.impact }}</div>
                </div>
                <div class="analysis-item">
                  <div class="item-label">处理建议：</div>
                  <div class="item-content">{{ analysisResult.suggestion }}</div>
                </div>
              </template>
              <div v-else class="empty-state">
                <el-icon><ChatDotRound /></el-icon>
                <span>请选择AI模型并输入错误信息进行分析</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-section">
      <div class="alert-panel">
        <div class="panel-title">
          <el-icon><Warning /></el-icon>
          <span>原始错误信息</span>
        </div>
        <MyTable
          :data="list.records"
          :total="list.total"
          height="240"
          v-loading="tableLoading"
          pagination-layout=""
        >
          <my-column property="time" label="时间" width="180" />
          <my-column property="message" label="错误信息">
            <template #default="{ row }">
              <span class="operate" @click="handleMessageClick(row)">{{ row.message }} </span>
            </template>
          </my-column>
          <my-column property="level" label="错误级别" width="100">
            <template #default="{ row }">
              <el-tag :type="getAlertLevelType(row.level)">{{ row.level }}</el-tag>
            </template>
          </my-column>
          <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
            <template #default>
              <span class="operate" @click="resolveVisible = true">处理方法 </span>
            </template>
          </my-column>
        </MyTable>
      </div>
      <div class="history-panel">
        <div class="panel-title">
          <el-icon><Document /></el-icon>
          <span>处理历史</span>
        </div>
        <div class="mb-15px">
          <el-input
            style="width: 300px"
            placeholder="请输入错误信息"
            clearable
            v-model="message"
          ></el-input>
          <el-button type="primary" style="margin-left: 15px" :icon="Search">搜索</el-button>
        </div>
        <MyTable
          :data="lists.records"
          :total="lists.total"
          height="240"
          v-loading="tableLoading"
          pagination-layout=""
        >
          <my-column property="message" label="错误信息" />
          <my-column property="resolve" label="处理方法" />
          <my-column property="resolver" label="处理人" width="150" />
          <my-column property="contactInformation" label="处理人联系方式" width="150" />
        </MyTable>
      </div>
    </div>
  </div>
  <el-dialog
    :align-center="true"
    v-model="resolveVisible"
    title="处理方法"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="form" label-width="auto" class="demo-ruleForm">
      <el-form-item label="处理方法：">
        <el-input type="textarea" v-model="form.resolve" :rows="4" placeholder="请输入处理方法" />
      </el-form-item>
      <el-form-item label="处理人：">
        <el-select v-model="form.resolver" placeholder="请选择处理人" clearable>
          <el-option value="周琦" label="周琦"> </el-option>
          <el-option value="陈傲迪" label="陈傲迪"> </el-option>
          <el-option value="王晨瑞" label="王晨瑞"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="通知方式：">
        <el-checkbox-group
          v-model="form.noticeWayIdList"
          clearable
          style="width: 200px"
          placeholder="请选择通知方式"
        >
          <el-checkbox label="短信" value="0" />
          <el-checkbox label="邮件" value="1" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="通知对象：">
        <el-select v-model="form.resolver" placeholder="请选择通知对象" clearable>
          <el-option value="周琦" label="周琦"> </el-option>
          <el-option value="陈傲迪" label="陈傲迪"> </el-option>
          <el-option value="王晨瑞" label="王晨瑞"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resolveVisible = false">取消</el-button>
        <el-button type="primary" @click="resolveVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Search, Warning, Lightning, Document, ChatDotRound } from "@element-plus/icons-vue";
const resolveVisible = ref(false);
const message = ref("");
const form = reactive({
  resolve: "",
  resolver: "",
  noticeWayIdList: []
});
interface AnalysisResult {
  cause: string;
  impact: string;
  suggestion: string;
}

const messageValue = ref("");
const aiModel = ref("deepseek");
const tableLoading = ref(false);
const analyzing = ref(false);
const analysisResult = ref<AnalysisResult | null>(null);

const getAlertLevelType = (
  level: string
): "danger" | "warning" | "info" | "success" | "primary" => {
  const types: Record<string, "danger" | "warning" | "info" | "success" | "primary"> = {
    严重: "danger",
    警告: "warning",
    提示: "info"
  };
  return types[level] || "info";
};

const handleAnalyze = async () => {
  if (!aiModel.value || !messageValue.value) {
    ElMessage.warning("请选择AI模型并输入错误信息");
    return;
  }

  analyzing.value = true;
  try {
    // 这里添加实际的AI分析API调用
    await new Promise(resolve => setTimeout(resolve, 1500));
    analysisResult.value = {
      cause: "检测到异常登录尝试",
      impact: "可能涉及安全风险，需要及时处理",
      suggestion: "1. 检查系统日志\n2. 加强密码策略\n3. 考虑启用双因素认证"
    };
    form.resolve = analysisResult.value.suggestion;
    resolveVisible.value = true;
  } catch (error) {
    ElMessage.error("分析失败，请重试");
  } finally {
    analyzing.value = false;
  }
};

const list = reactive({
  records: [
    {
      time: "2025-3-27 16:35:31",
      message: "Invalid user hzzhu from *************** port 35434",
      level: "严重"
    },
    {
      time: "2025-3-27 16:35:47",
      message: "Invalid user hzzhu from ***************",
      level: "警告"
    },
    {
      time: "2025-3-27 16:35:56",
      message: "Invalid user hzzhu from *************** port 35434",
      level: "提示"
    },
    {
      time: "2025-3-27 16:35:59",
      message: "Invalid user hzzhu from *************** port 35434",
      level: "严重"
    },
    {
      time: "2025-3-27 16:36:03",
      message: "Invalid user hzzhu from *************** port 35434",
      level: "警告"
    }
  ],
  total: 5
});

const lists = reactive({
  records: [
    {
      message: "Invalid user hzzhu from *************** port 35434",
      resolve: "已加强密码策略",
      resolver: "XX",
      contactInformation: "18896358751"
    },
    {
      message: "Invalid user hzzhu from *************** port 35434",
      resolve: "已封禁IP",
      resolver: "XX",
      contactInformation: "18896358751"
    },
    {
      message: "Invalid user hzzhu from *************** port 35434",
      resolve: "已更新防火墙规则",
      resolver: "XX",
      contactInformation: "18896358751"
    },
    {
      message: "Invalid user hzzhu from *************** port 35434",
      resolve: "已更新防火墙规则",
      resolver: "XX",
      contactInformation: "18896358751"
    },
    {
      message: "Invalid user hzzhu from *************** port 35434",
      resolve: "已更新防火墙规则",
      resolver: "XX",
      contactInformation: "18896358751"
    }
  ],
  total: 3
});

const handleMessageClick = (row: any) => {
  messageValue.value = row.message;
  message.value = row.message;
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  max-width: 100% !important;
  height: 100%;
  gap: 20px;
  box-sizing: border-box;

  .left-section {
    width: 30%;
    height: 100%;

    .ai-analysis-section {
      height: 100%;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      gap: 16px;

      .section-title {
        .title-text {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .analysis-content {
        flex: 1;
        height: calc(100% - 20px);
        display: flex;
        flex-direction: column;
        gap: 16px;

        .input-area {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .model-select {
            .model-dropdown {
              width: 100%;
            }
          }

          .input-section {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .message-input {
              flex: 1;
            }

            .analyze-btn {
              height: 36px;
              font-size: 14px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
            }
          }
        }

        .result-area {
          height: calc(100% - 200px);
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;
          padding: 16px;
          background-color: #f9fafb;
          border-radius: 8px;

          .result-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
          }

          .result-content {
            flex: 1;
            overflow-y: auto;
            word-break: break-all;

            .analysis-item {
              margin-bottom: 12px;

              .item-label {
                font-weight: 500;
                color: #4b5563;
                margin-bottom: 6px;
              }

              .item-content {
                color: #1f2937;
                line-height: 1.5;
                white-space: pre-line;
              }
            }

            .empty-state {
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              gap: 12px;
              color: #9ca3af;
              font-size: 14px;

              .el-icon {
                font-size: 28px;
              }
            }
          }
        }
      }
    }
  }

  .right-section {
    width: 70%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .alert-panel,
    .history-panel {
      height: calc(50% - 10px);
      flex: 1;
      border-radius: 12px;
      padding: 20px;
      box-sizing: border-box;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      .panel-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.operate {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
