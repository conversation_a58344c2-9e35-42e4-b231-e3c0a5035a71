<template>
  <div class="workflow-container">
    <!-- <div class="table-header">
      <div></div>
      <el-button type="primary" class="create-btn" @click="goCreateWorkflow">创建工作流</el-button>
    </div> -->
    <div v-loading="loading">
      <MyTable
        :data="workflowList"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
        <my-column property="name" label="名称">
          <template #default="{ row }">
            <span class="workflow-name" @click="showDetail(row)">{{ row.name }}</span>
          </template>
        </my-column>
        <my-column property="workflow_type" label="工作流类型">
          <template #default="{ row }">
            {{ row.graph?.workflow_type || "-" }}
          </template>
        </my-column>
        <my-column label="输入参数">
          <template #default="{ row }">
            <div class="input-params">
              <span v-if="row.graph?.inputs?.length"> {{ row.graph.inputs.length }}个参数 </span>
              <span v-else>无</span>
            </div>
          </template>
        </my-column>
        <my-column label="节点数量">
          <template #default="{ row }">
            <span>{{ row.graph?.nodes?.length || 0 }}</span>
          </template>
        </my-column>
        <my-column label="边数量">
          <template #default="{ row }">
            <span>{{ row.graph?.edges?.length || 0 }}</span>
          </template>
        </my-column>
        <my-column prop="created_at" label="创建时间">
          <template #default="{ row }">
            <span>{{ formatTime(row.created_at) }}</span>
          </template>
        </my-column>
        <my-column prop="updated_at" label="更新时间">
          <template #default="{ row }">
            <span>{{ formatTime(row.updated_at) }}</span>
          </template>
        </my-column>
        <my-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <span class="workflow-name" @click="showDetail(row)">查看详情</span>
            <span class="divider"> / </span>
            <span class="workflow-name" @click="showExecutions(row)">执行记录</span>
            <span class="divider"> / </span>
            <span class="workflow-name" @click="handleDelete(row)">删除</span>
            <span class="divider"> / </span>
            <span class="workflow-name" @click="handleExecute(row)">执行</span>
          </template>
        </my-column>
      </MyTable>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer v-model="detailVisible" :title="title" size="60%" v-loading="detailLoading">
      <div v-if="workflowDetail" style="height: 100%">
        <WorkflowDetail :workflow-detail="workflowDetail" />
      </div>
    </el-drawer>
    <!-- 删除确认弹窗 -->
    <el-dialog
      :align-center="true"
      v-model="deleteVisible"
      title="温馨提示"
      width="500"
      :close-on-click-modal="false"
    >
      <span>确认要删除工作流「{{ currentWorkflow?.name }}」吗？</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteVisible = false">取消</el-button>
          <el-button type="primary" :loading="deleteLoading" @click="confirmDelete">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 执行确认弹窗 -->
    <el-dialog
      :align-center="true"
      v-model="executeVisible"
      title="执行工作流"
      width="600"
      :close-on-click-modal="false"
    >
      <div class="execute-form">
        <div class="form-item">
          <label class="form-label">工作流名称：</label>
          <span class="form-value">{{ currentWorkflow?.name }}</span>
        </div>

        <div v-if="executeParams.length > 0" class="form-item">
          <label class="form-label">输入参数：</label>
          <div class="params-container">
            <div v-for="param in executeParams" :key="param.name" class="param-item">
              <label class="param-label">
                {{ param.name }}
                <span v-if="param.required" class="required">*</span>
              </label>
              <el-input
                v-model="param.value"
                :placeholder="param.description || `请输入${param.name}`"
                :type="param.type === 'number' ? 'number' : 'text'"
                clearable
              />
            </div>
          </div>
        </div>

        <div v-else class="form-item">
          <label class="form-label">输入参数：</label>
          <span class="form-value">无需输入参数</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="executeVisible = false">取消</el-button>
          <el-button type="primary" :loading="executeLoading" @click="confirmExecute"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 执行记录抽屉 -->
    <el-drawer v-model="executionVisible" :title="executionTitle" size="60%">
      <div v-loading="executionLoading">
        <TitlecCom title="执行记录"></TitlecCom>
        <div class="attr-panel">
          <div class="tab-content">
            <MyTable
              :data="executionList"
              :total="executionList.length"
              :show-el-pagination="false"
            >
              <my-column label="状态">
                <template #default="{ row }">
                  <el-tag
                    :type="
                      row.status === 'SUCCESS'
                        ? 'success'
                        : row.status === 'FAILED'
                          ? 'danger'
                          : 'info'
                    "
                  >
                    {{ row.status }}
                  </el-tag>
                </template>
              </my-column>
              <my-column property="input_context" label="输入参数" :show-overflow-tooltip="false">
                <template #default="scope">
                  <div class="ip-cell">
                    <el-tooltip placement="top" popper-class="custom-tooltip">
                      <template #content>
                        <div style="white-space: pre-wrap; word-break: break-word">
                          {{ scope.row.input_context }}
                        </div>
                      </template>
                      <span class="ip-text">{{ scope.row.input_context }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </my-column>
              <my-column label="聚合数据" property="aggregated_data" :show-overflow-tooltip="false">
                <template #default="scope">
                  <div class="ip-cell">
                    <el-tooltip placement="top" popper-class="custom-tooltip">
                      <template #content>
                        <div style="white-space: pre-wrap; word-break: break-word">
                          {{ scope.row.aggregated_data }}
                        </div>
                      </template>
                      <span class="ip-text">{{ scope.row.aggregated_data || "-" }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </my-column>
              <my-column label="Prompt" property="final_prompt" :show-overflow-tooltip="false">
                <template #default="scope">
                  <div class="ip-cell">
                    <el-tooltip placement="top" popper-class="custom-tooltip">
                      <template #content>
                        <div style="white-space: pre-wrap; word-break: break-word">
                          {{ scope.row.final_prompt }}
                        </div>
                      </template>
                      <span class="ip-text">{{ scope.row.final_prompt || "-" }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </my-column>
              <my-column label="结果" property="result" :show-overflow-tooltip="false">
                <template #default="scope">
                  <div class="ip-cell">
                    <el-tooltip placement="top" popper-class="custom-tooltip">
                      <template #content>
                        <div style="white-space: pre-wrap; word-break: break-word">
                          {{ formatResult(scope.row.result) }}
                        </div>
                      </template>
                      <span class="ip-text">{{ scope.row.result || "-" }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </my-column>
              <my-column label="错误信息" property="error" :show-overflow-tooltip="false">
                <template #default="scope">
                  <div class="ip-cell">
                    <el-tooltip placement="top" popper-class="custom-tooltip">
                      <template #content>
                        <div style="white-space: pre-wrap; word-break: break-word">
                          {{ scope.row.error }}
                        </div>
                      </template>
                      <span class="ip-text">{{ scope.row.error || "-" }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </my-column>
              <my-column label="开始时间">
                <template #default="{ row }">
                  <span>{{ formatTime(row.started_at) }}</span>
                </template>
              </my-column>
              <my-column label="完成时间">
                <template #default="{ row }">
                  <span>{{ formatTime(row.finished_at) }}</span>
                </template>
              </my-column>
            </MyTable>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { formatTime } from "@/utils/dateStr";
import { reactive, ref, onMounted } from "vue";
import {
  getWorkFlows,
  deleteWorkflow,
  executeWorkflow,
  detailWorkflow,
  getWorkflowExecutions
} from "@/api/workflow/index";
import type { WorkflowItem, PageParams } from "@/api/workflow/types";
import MyTable from "@/components/table/my-table.vue";
import TitlecCom from "@/components/TitleCom/index.vue";
import WorkflowDetail from "./components/WorkflowDetail.vue";
import { ElMessage } from "element-plus";

// 响应式数据
const workflowList = ref<WorkflowItem[]>([]);
const total = ref(0);
const loading = ref(false);
const deleteVisible = ref(false);
const executeVisible = ref(false);
const deleteLoading = ref(false);
const executeLoading = ref(false);
const currentWorkflow = ref<WorkflowItem | null>(null);
const title = ref("");

// 详情相关
const detailVisible = ref(false);
const detailLoading = ref(false);
const workflowDetail = ref<any>(null);

// 执行记录相关
const executionVisible = ref(false);
const executionLoading = ref(false);
const executionList = ref<any[]>([]);
const executionTitle = ref("");

// 执行参数相关
const executeParams = ref<
  Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    value: string;
  }>
>([]);

const pageParams = reactive<PageParams>({
  page: 1,
  page_size: 10
});

// 加载数据
async function loadData() {
  loading.value = true;
  try {
    const response = await getWorkFlows(pageParams);
    if (response && response.data) {
      workflowList.value = response.data.items || [];
      total.value = response.data.total || response.data.items?.length || 0;
    } else {
      ElMessage.error("数据格式异常");
    }
  } catch (error) {
    ElMessage.error("获取工作流列表失败");
  } finally {
    loading.value = false;
  }
}

// 分页处理
function handleCurrentChange(page: number) {
  pageParams.page = page;
  loadData();
}

function handleSizeChange(size: number) {
  pageParams.page_size = size;
  loadData();
}

// 查看详情
async function showDetail(row: WorkflowItem) {
  title.value = "工作流详情 【" + row.name + "】";
  detailVisible.value = true;
  detailLoading.value = true;
  try {
    const response = await detailWorkflow(row.id);
    if (response && response.data) {
      workflowDetail.value = response.data;
    } else {
      ElMessage.error("获取详情失败");
    }
  } catch (error: any) {
    const errorMessage = error?.desc || "获取详情失败";
    ElMessage.error(errorMessage);
  } finally {
    detailLoading.value = false;
  }
}

// 查看执行记录
async function showExecutions(row: WorkflowItem) {
  executionTitle.value = `执行记录【${row.name}】`;
  executionVisible.value = true;
  executionLoading.value = true;
  try {
    const response = await getWorkflowExecutions(row.id);
    if (response && response.data) {
      executionList.value = response.data;
    } else {
      executionList.value = [];
    }
  } catch (error: any) {
    executionList.value = [];
    ElMessage.error(error?.desc || "获取执行记录失败");
  } finally {
    executionLoading.value = false;
  }
}

// 删除工作流
function handleDelete(row: WorkflowItem) {
  currentWorkflow.value = row;
  deleteVisible.value = true;
}

async function confirmDelete() {
  if (!currentWorkflow.value) return;

  deleteLoading.value = true;
  try {
    await deleteWorkflow(currentWorkflow.value.id);
    ElMessage.success("删除成功");
    deleteVisible.value = false;
    loadData();
  } catch (error: any) {
    const errorMessage = error?.desc || "删除失败";
    ElMessage.error(errorMessage);
  } finally {
    deleteLoading.value = false;
  }
}

// 执行工作流
function handleExecute(row: WorkflowItem) {
  currentWorkflow.value = row;

  // 初始化执行参数
  executeParams.value = [];
  if (row.graph?.inputs?.length) {
    executeParams.value = row.graph.inputs.map((input: any) => ({
      name: input.name,
      type: input.type,
      description: input.description,
      required: input.required,
      value: ""
    }));
  }

  executeVisible.value = true;
}

async function confirmExecute() {
  if (!currentWorkflow.value) return;

  // 验证必填参数
  const requiredParams = executeParams.value.filter(param => param.required);
  const missingParams = requiredParams.filter(param => !param.value.trim());

  if (missingParams.length > 0) {
    ElMessage.error(`请填写必填参数: ${missingParams.map(p => p.name).join(", ")}`);
    return;
  }

  // 构建参数对象
  const params: Record<string, any> = {};
  executeParams.value.forEach(param => {
    if (param.value.trim()) {
      params[param.name] = param.type === "number" ? Number(param.value) : param.value;
    }
  });

  executeLoading.value = true;
  try {
    await executeWorkflow(currentWorkflow.value.id, params);
    ElMessage.success("执行成功");
    executeVisible.value = false;
    loadData();
  } catch (error: any) {
    const errorMessage = error?.desc || "执行失败";
    ElMessage.error(errorMessage);
  } finally {
    executeLoading.value = false;
  }
}

function formatResult(val: string) {
  if (!val) return "-";
  try {
    let str = val;
    if (str.startsWith('"') && str.endsWith('"')) {
      str = str.slice(1, -1);
    }
    // 还原转义字符
    str = str.replace(/\\n/g, "\n").replace(/\n/g, "\n").replace(/"/g, '"');
    return str;
  } catch {
    return val;
  }
}

// function goCreateWorkflow() {
//   router.push({ path: "/system/warnmanage?tab=analysis" }); // 替换为实际路由
// }

onMounted(() => {
  loadData();
});
</script>

<style scoped>
/* .workflow-container {
  padding: 20px;
} */

.page-header {
  margin-bottom: 20px;
}

.workflow-name {
  color: #0064c8;
  cursor: pointer;
}
.input-params {
  color: #666;
}

.dialog-footer {
  text-align: right;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
  font-style: italic;
}

.config-content {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
}

:deep(.el-drawer__header) {
  margin: 5px !important;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

:deep(.attr-panel) {
  margin: 0 !important;
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}

.detail-table tr td {
  border: 1px solid #ebeef5;
  word-break: break-all;
  padding: 15px;
}

.detail-table tr td.label {
  background: #f5f7fa;
  width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.detail-table thead tr td {
  background: #f5f7fa;
  font-weight: 500;
}

/* 执行记录表格样式 */
.content-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  max-width: 100%;
}

.workflow-tooltip {
  white-space: pre-wrap;
  word-break: break-all;
  max-width: 600px;
  font-size: 14px;
}

.error-text {
  color: #d93026;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-weight: 500 !important;
}

:deep(.el-table td) {
  padding: 12px 0;
}
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.custom-tooltip {
  max-width: 300px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}
.create-btn {
  min-width: 120px;
}

/* 执行表单样式 */
.execute-form {
  padding: 10px 0;
}

.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.form-value {
  color: #303133;
  font-weight: 500;
}

.params-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.param-item {
  margin-bottom: 16px;
}

.param-item:last-child {
  margin-bottom: 0;
}

.param-label {
  display: block;
  font-size: 14px;
  color: #606266;
  margin-bottom: 6px;
}

.required {
  color: #f56c6c;
  margin-left: 2px;
}
</style>
