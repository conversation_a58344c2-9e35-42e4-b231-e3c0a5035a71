<template>
  <div class="node-panel">
    <div class="node-list">
      <div
        v-for="(node, index) in nodeTypes"
        :key="index"
        class="node-item"
        @mousedown="handleNodeDrag(node)"
      >
        <div class="node-icon-wrapper">
          <el-icon class="node-icon">
            <component :is="node.icon" />
          </el-icon>
        </div>
        <span class="node-label">{{ node.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps({
  lf: Object
});

const emit = defineEmits(["add-node"]);

// 节点配置
const nodeTypes = ref([
  {
    type: "rect",
    nodeType: "datasource",
    label: "数据源",
    icon: "DataAnalysis"
  },
  {
    type: "circle",
    nodeType: "llm",
    label: "AI分析",
    icon: "Connection"
  }
]);

// 处理节点拖拽
const handleNodeDrag = (node: any) => {
  if (!props.lf) {
    return;
  }

  if (!props.lf.dnd) {
    return;
  }

  const nodeId = `node_${Date.now()}`;

  try {
    props.lf.dnd.startDrag({
      type: node.type,
      text: node.label,
      id: nodeId
    });
  } catch (error) {
    return;
  }

  // 只使用备用方案，不使用LogicFlow事件
  setTimeout(() => {
    const addedNode = props.lf?.getNodeModelById(nodeId);
    if (addedNode) {
      const emitObj = {
        ...node,
        id: nodeId,
        type: node.type,
        nodeType: node.nodeType,
        config: getDefaultConfig(node.nodeType),
        param_mappings: {},
        position: { x: addedNode.x, y: addedNode.y }
      };
      emit("add-node", emitObj);
    }
  }, 500);
};

// 获取默认配置
const getDefaultConfig = (type: string) => {
  if (type === "datasource") {
    return { source_id: "" };
  } else if (type === "llm") {
    return {
      system_template_name: "",
      user_template_name: "",
      adhoc_instruction: ""
    };
  }
  return {};
};
</script>

<style lang="scss" scoped>
.node-panel {
  position: absolute;
  left: 5px;
  top: 20px;
  width: 80px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 60vh;
  overflow-y: auto;
  user-select: none;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 5px;
  cursor: grab;
  transition: all 0.2s;
  position: relative;

  &:hover {
    background: rgba(64, 158, 255, 0.1);
    border-radius: 4px;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &::after {
      content: "";
      position: absolute;
      right: -8px;
      background: #409eff;
      width: 3px;
      height: 100%;
      border-radius: 2px;
    }

    .node-icon-wrapper {
      transform: scale(1.15);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }
  }

  &:active {
    cursor: grabbing;
  }

  .node-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    transition: all 0.3s;
  }

  .node-icon {
    font-size: 24px;
    color: #333;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  }

  .node-item:nth-child(1) .node-icon-wrapper {
    background: linear-gradient(135deg, #e8f5e8, #f0f9ff);
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
    border: 2px solid #67c23a;
  }

  .node-item:nth-child(2) .node-icon-wrapper {
    background: linear-gradient(135deg, #fff7e6, #fef0f0);
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
    border: 2px solid #e6a23c;
  }

  .node-label {
    font-size: 12px;
    color: #606266;
    text-align: center;
  }
}
</style>
