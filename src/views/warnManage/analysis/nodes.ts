import {
  RectNodeModel,
  CircleNodeModel,
  DiamondNodeModel,
  LogicFlow,
  RectNode,
  CircleNode,
  DiamondNode
} from "@logicflow/core";

// 开始节点
class StartNodeModel extends CircleNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#3a7bd5", stroke: "#2563eb", strokeWidth: 2 };
  }
}
class StartNodeView extends CircleNode {
  static extendKey = "StartNodeView";
}

// 结束节点
class EndNodeModel extends CircleNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#d1d5db", stroke: "#6b7280", strokeWidth: 2 };
  }
}
class EndNodeView extends CircleNode {
  static extendKey = "EndNodeView";
}

// 告警源
class AlertSourceModel extends RectNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#22c55e", stroke: "#16a34a", strokeWidth: 2, radius: 8 };
  }
}
class AlertSourceView extends RectNode {
  static extendKey = "AlertSourceView";
}

// 规则判断
class AlertRuleModel extends DiamondNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#fde68a", stroke: "#f59e42", strokeWidth: 2 };
  }
}
class AlertRuleView extends DiamondNode {
  static extendKey = "AlertRuleView";
}

// 人工审核
class ManualAuditModel extends RectNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#a5b4fc", stroke: "#6366f1", strokeWidth: 2, radius: 8 };
  }
}
class ManualAuditView extends RectNode {
  static extendKey = "ManualAuditView";
}

// 自动处理
class AutoActionModel extends RectNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#67e8f9", stroke: "#06b6d4", strokeWidth: 2, radius: 8 };
  }
}
class AutoActionView extends RectNode {
  static extendKey = "AutoActionView";
}

// 通知
class AlertNotificationModel extends RectNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#fdba74", stroke: "#ea580c", strokeWidth: 2, radius: 8 };
  }
}
class AlertNotificationView extends RectNode {
  static extendKey = "AlertNotificationView";
}

// 归档/忽略
class ArchiveModel extends RectNodeModel {
  getNodeStyle() {
    const style = super.getNodeStyle();
    return { ...style, fill: "#e5e7eb", stroke: "#6b7280", strokeWidth: 2, radius: 8 };
  }
}
class ArchiveView extends RectNode {
  static extendKey = "ArchiveView";
}

export function registerNodes(lf: LogicFlow) {
  lf.register({ type: "start", view: StartNodeView, model: StartNodeModel });
  lf.register({ type: "end", view: EndNodeView, model: EndNodeModel });
  lf.register({ type: "alert-source", view: AlertSourceView, model: AlertSourceModel });
  lf.register({ type: "alert-rule", view: AlertRuleView, model: AlertRuleModel });
  lf.register({ type: "manual-audit", view: ManualAuditView, model: ManualAuditModel });
  lf.register({ type: "auto-action", view: AutoActionView, model: AutoActionModel });
  lf.register({
    type: "alert-notification",
    view: AlertNotificationView,
    model: AlertNotificationModel
  });
  lf.register({ type: "archive", view: ArchiveView, model: ArchiveModel });
}
