<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <div class="mb-10px top-container">
      <el-button @click="saveVisible = true" type="primary">保存工作流</el-button>
    </div>

    <div class="logic-flow-view" v-loading="loading">
      <!-- 工具栏 -->
      <Control v-if="lf" class="demo-control" :lf="lf"></Control>

      <!-- 左侧节点面板 -->
      <NodePanel v-if="lf" :lf="lf" @add-node="addNode"></NodePanel>

      <!-- 画布 -->
      <div id="workflow-container" style="width: 100%; height: 100%"></div>

      <!-- 右侧属性面板 -->
      <PropertyPanel
        v-if="selectedNode"
        :node="selectedNode"
        :data-sources="dataSources"
        :prompt-templates="promptTemplates"
        @update-node="updateNode"
        @close="selectedNode = null"
      />
    </div>

    <!-- 保存对话框 -->
    <el-dialog
      :align-center="true"
      v-model="saveVisible"
      title="保存工作流"
      width="800"
      :close-on-click-modal="false"
    >
      <el-form :model="workflowForm" label-width="120px">
        <el-form-item label="工作流名称" required>
          <el-input v-model="workflowForm.name" placeholder="请输入工作流名称"></el-input>
        </el-form-item>
        <el-form-item label="工作流类型">
          <el-input v-model="workflowForm.workflow_type" placeholder="请选择工作流类型" disabled>
          </el-input>
        </el-form-item>
        <el-form-item label="工作流描述">
          <el-input
            type="textarea"
            v-model="workflowForm.description"
            :rows="3"
            placeholder="请输入工作流描述"
          ></el-input>
        </el-form-item>

        <!-- 输入参数管理 -->
        <el-form-item label="输入参数">
          <div class="input-params-section">
            <div
              v-for="(input, index) in workflowForm.graph.inputs"
              :key="index"
              class="input-param-item"
            >
              <el-form :model="input" label-position="top" size="small">
                <div class="input-header">
                  <span>输入参数 {{ index + 1 }}</span>
                  <el-button type="danger" size="small" @click="removeInput(index)">删除</el-button>
                </div>
                <el-form-item label="参数名称">
                  <el-input v-model="input.name" placeholder="参数名称"></el-input>
                </el-form-item>
                <el-form-item label="参数类型">
                  <el-select v-model="input.type">
                    <el-option label="字符串" value="string"></el-option>
                    <el-option label="数字" value="number"></el-option>
                    <el-option label="布尔值" value="boolean"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="参数描述">
                  <el-input v-model="input.description" placeholder="参数描述"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-checkbox v-model="input.required">必填参数</el-checkbox>
                </el-form-item>
              </el-form>
            </div>
            <el-button type="primary" @click="addInput" size="small">添加输入参数</el-button>
          </div>
        </el-form-item>

        <!-- 输出源配置 -->
        <el-form-item label="输出源表达式">
          <el-input
            v-model="workflowForm.graph.output_source"
            placeholder="例如: nodes.analyst_llm.output"
          ></el-input>
        </el-form-item>

        <!-- 测试区域 -->
        <!-- <el-form-item label="测试功能">
          <div class="test-section">
            <el-button @click="createExampleWorkflow" type="info" size="small"
              >创建示例工作流</el-button
            >
            <el-button @click="testDataSources" type="warning" size="small"
              >测试数据源接口</el-button
            >
            <el-button @click="testPromptTemplates" type="success" size="small"
              >测试提示模板接口</el-button
            >
          </div>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveVisible = false">取消</el-button>
          <el-button type="primary" @click="saveWorkflow" :loading="saving">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import LogicFlow from "@logicflow/core";
import { Menu, Snapshot, MiniMap, SelectionSelect, DndPanel } from "@logicflow/extension";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import Control from "./components/Control.vue";
import NodePanel from "./components/NodePanel.vue";
import PropertyPanel from "./components/PropertyPanel.vue";
import { createWorkflow, getDataSources, getPromptTemplates } from "@/api/workflow";

export default {
  name: "WorkflowAnalysis",
  components: {
    Control,
    NodePanel,
    PropertyPanel
  },
  setup() {
    const loading = ref(false);
    const saving = ref(false);
    const saveVisible = ref(false);
    const previewVisible = ref(false);
    const selectedNode = ref(null);
    let lf = ref(null);

    // 数据源和提示模板列表
    const dataSources = ref([]);
    const promptTemplates = ref([]);

    // 工作流表单
    const workflowForm = reactive({
      name: "",
      description: "",
      workflow_type: "service_analysis",
      graph: {
        inputs: [],
        nodes: [],
        edges: [],
        output_source: "",
        workflow_type: "workflow"
      }
    });

    // 获取数据源列表
    const fetchDataSources = async () => {
      try {
        const response = await getDataSources();
        if (response.success) {
          dataSources.value = response.data.items;
        }
      } catch (error) {
        ElMessage.error("获取数据源列表失败");
      }
    };

    // 获取提示模板列表
    const fetchPromptTemplates = async () => {
      try {
        const response = await getPromptTemplates();
        if (response.success) {
          promptTemplates.value = response.data;
        }
      } catch (error) {
        ElMessage.error("获取提示模板列表失败");
      }
    };

    // 初始化LogicFlow
    const initLogicFlow = () => {
      loading.value = true;

      // 使用更长的延迟确保DOM完全渲染
      setTimeout(() => {
        try {
          const container = document.querySelector("#workflow-container");
          if (!container) {
            loading.value = false;
            return;
          }

          const myLf = new LogicFlow({
            container: container,
            grid: {
              size: 10,
              visible: true,
              type: "dot",
              config: {
                color: "#a0a0a0",
                thickness: 1
              }
            },
            background: {
              backgroundColor: "#fafafa"
            },
            keyboard: {
              enabled: true
            },
            nodeTextEdit: true,
            edgeTextEdit: true,
            plugins: [Menu, MiniMap, Snapshot, SelectionSelect, DndPanel]
          });

          lf.value = myLf;

          // 注册自定义节点
          registerCustomNodes(myLf);

          // 绑定事件
          bindEvents(myLf);

          // 渲染初始数据
          renderWorkflow(myLf);

          loading.value = false;
        } catch (error) {
          loading.value = false;
        }
      }, 100);
    };

    // 设置节点样式
    const registerCustomNodes = lf => {
      lf.setTheme({
        rect: {
          radius: 8,
          fill: "#f0f9ff",
          stroke: "#67c23a",
          strokeWidth: 2
        },
        circle: {
          fill: "#fff7e6",
          stroke: "#e6a23c",
          strokeWidth: 2
        }
      });
    };

    // 绑定事件
    const bindEvents = lf => {
      if (!lf || !lf.on) return;

      // 节点点击事件
      lf.on("node:click", ({ data }) => {
        // 尝试多种方式获取业务数据
        let bizData = data.properties?.biz;
        if (!bizData) {
          // 如果 properties.biz 不存在，尝试从 workflowForm 中查找
          const nodeInWorkflow = workflowForm.graph.nodes.find(n => n.id === data.id);
          if (nodeInWorkflow) {
            bizData = nodeInWorkflow;
          }
        }

        selectedNode.value = bizData;
      });

      // 画布点击事件
      lf.on("blank:click", () => {
        selectedNode.value = null;
      });

      // 节点删除事件
      lf.on("node:delete", ({ data }) => {
        const index = workflowForm.graph.nodes.findIndex(n => n.id === data.id);
        if (index > -1) {
          workflowForm.graph.nodes.splice(index, 1);
        }
      });

      // 边添加事件
      lf.on("edge:add", ({ data }) => {
        const edge = {
          source_node_id: data.sourceNodeId,
          source_output: "data",
          target_node_id: data.targetNodeId,
          target_input: "input"
        };
        workflowForm.graph.edges.push(edge);
      });
    };

    // 渲染工作流
    const renderWorkflow = lf => {
      if (!lf || !lf.render) return;
      const flowData = {
        nodes: workflowForm.graph.nodes.map(node => {
          const nodeObj = {
            id: node.id,
            type: node.type === "datasource" ? "rect" : node.type === "llm" ? "circle" : node.type,
            x: node.position?.x || 100,
            y: node.position?.y || 100,
            text: node.type === "datasource" ? "数据源" : "AI分析",
            properties: {
              width: 100,
              height: 80,
              biz: { ...node }
            }
          };
          return nodeObj;
        }),
        edges: workflowForm.graph.edges.map(edge => ({
          id: `${edge.source_node_id}-${edge.target_node_id}`,
          sourceNodeId: edge.source_node_id,
          targetNodeId: edge.target_node_id,
          type: "polyline"
        }))
      };
      lf.render(flowData);
    };

    // 创建示例工作流
    const createExampleWorkflow = () => {
      // 添加示例输入参数
      workflowForm.graph.inputs = [
        {
          name: "service_name",
          type: "string",
          description: "服务名称",
          required: true
        },
        {
          name: "hostname",
          type: "string",
          description: "服务器主机名",
          required: true
        }
      ];

      // 添加示例节点
      const datasourceNode = {
        id: "get_metrics",
        type: "datasource",
        config: {
          source_id: "host_monitor.metrics"
        },
        param_mappings: {
          hostname: "workflow.input.hostname"
        },
        position: {
          x: 100,
          y: 100
        }
      };

      const llmNode = {
        id: "analyst_llm",
        type: "llm",
        config: {
          system_template_name: "system_analysis",
          user_template_name: "",
          adhoc_instruction: ""
        },
        param_mappings: {
          host_data: "nodes.get_metrics.data",
          service_name: "workflow.input.service_name"
        },
        position: {
          x: 400,
          y: 100
        }
      };

      workflowForm.graph.nodes = [datasourceNode, llmNode];
      workflowForm.graph.edges = [
        {
          source_node_id: "get_metrics",
          source_output: "data",
          target_node_id: "analyst_llm",
          target_input: "host_data"
        }
      ];
      workflowForm.graph.output_source = "nodes.analyst_llm.output";
    };

    // 添加节点
    const addNode = nodeData => {
      if (!nodeData || !nodeData.type) {
        return;
      }

      // 如果节点已经有ID，说明是从拖拽添加的
      const nodeId = nodeData.id || `node_${Date.now()}`;

      // 检查节点是否已经存在
      const existingNode = workflowForm.graph.nodes.find(n => n.id === nodeId);
      if (existingNode) {
        return;
      }
      const newNode = {
        id: nodeId,
        type: nodeData.nodeType,
        nodeType: nodeData.nodeType,
        config: nodeData.config || getDefaultConfig(nodeData.nodeType),
        param_mappings: nodeData.param_mappings || {},
        position: nodeData.position || {
          x: Math.random() * 400 + 100,
          y: Math.random() * 300 + 100
        }
      };

      // 检查节点是否已存在
      const existingIndex = workflowForm.graph.nodes.findIndex(n => n.id === nodeId);
      if (existingIndex > -1) {
        // 更新现有节点
        workflowForm.graph.nodes[existingIndex] = newNode;
      } else {
        // 添加新节点
        workflowForm.graph.nodes.push(newNode);
      }

      // 只更新已存在节点的属性，不重复添加到画布
      if (lf && lf.value.setProperties) {
        try {
          // 设置节点的业务数据
          lf.value.setProperties(nodeId, {
            biz: { ...newNode }
          });

          // 验证设置是否成功
          setTimeout(() => {
            const nodeModel = lf.value.getNodeModelById(nodeId);
            if (nodeModel) {
              // 节点设置成功
            }
          }, 100);
        } catch (error) {
          // 忽略设置错误
        }
      } else {
        // 逻辑流未初始化
      }
    };

    // 更新节点
    const updateNode = nodeData => {
      if (!nodeData || !nodeData.id) return;

      const index = workflowForm.graph.nodes.findIndex(n => n.id === nodeData.id);
      if (index > -1) {
        const oldNode = workflowForm.graph.nodes[index];
        workflowForm.graph.nodes[index] = { ...nodeData };

        // 如果是数据源节点且source_id发生变化，自动添加required_params到输入参数
        if (
          nodeData.type === "datasource" &&
          nodeData.config?.source_id &&
          oldNode.config?.source_id !== nodeData.config.source_id
        ) {
          addRequiredParamsToInputs(nodeData.config.source_id);
        }

        // 更新画布中的节点
        if (lf && lf.value.setProperties) {
          lf.value.setProperties(nodeData.id, {
            biz: { ...nodeData }
          });
        }
      }
    };

    // 根据数据源类型添加必需参数到输入参数
    const addRequiredParamsToInputs = sourceId => {
      // 查找对应的数据源类型
      for (const source of dataSources.value) {
        for (const dataType of source.data_types) {
          if (dataType.id === sourceId) {
            // 为每个必需参数添加输入参数
            dataType.required_params.forEach(paramName => {
              // 检查是否已存在相同的输入参数
              const existingInput = workflowForm.graph.inputs.find(
                input => input.name === paramName
              );
              if (!existingInput) {
                workflowForm.graph.inputs.push({
                  name: paramName,
                  type: "string",
                  description: `${paramName} (来自数据源 ${sourceId})`,
                  required: true
                });
              }
            });
            return;
          }
        }
      }
    };

    // 获取默认配置
    const getDefaultConfig = type => {
      if (type === "datasource") {
        return { source_id: "" };
      } else if (type === "llm") {
        return {
          system_template_name: "",
          user_template_name: "",
          adhoc_instruction: ""
        };
      }
      return {};
    };

    // 重置工作流
    const resetWorkflow = () => {
      ElMessageBox.confirm("确定要重置工作流吗？这将清除所有配置。", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          workflowForm.graph.nodes = [];
          workflowForm.graph.edges = [];
          workflowForm.graph.inputs = [];
          workflowForm.graph.output_source = "";
          selectedNode.value = null;

          if (lf && lf.value.clearData) {
            lf.value.clearData();
          }
        })
        .catch(() => {
          // 用户取消
        });
    };

    // 保存工作流
    const saveWorkflow = async () => {
      if (!workflowForm.name.trim()) {
        ElMessage.error("请输入工作流名称");
        return;
      }

      if (workflowForm.graph.nodes.length === 0) {
        ElMessage.error("请至少添加一个节点");
        return;
      }

      // 如果没有设置输出源，自动设置第一个LLM节点为输出源
      if (!workflowForm.graph.output_source) {
        const llmNode = workflowForm.graph.nodes.find(n => n.type === "llm");
        if (llmNode) {
          workflowForm.graph.output_source = `nodes.${llmNode.id}.output`;
        } else {
          ElMessage.error("请至少添加一个AI分析节点，或手动设置输出源表达式");
          return;
        }
      }

      // 验证节点配置
      for (const node of workflowForm.graph.nodes) {
        if (node.type === "datasource" && !node.config.source_id) {
          ElMessage.error(`数据源节点 ${node.id} 缺少 source_id 配置`);
          return;
        }
        if (
          node.type === "llm" &&
          !node.config.system_template_name &&
          !node.config.adhoc_instruction
        ) {
          ElMessage.error(`LLM节点 ${node.id} 缺少配置，请设置系统模板名称或临时指令`);
          return;
        }
      }

      // 验证边配置
      for (const edge of workflowForm.graph.edges) {
        const sourceNode = workflowForm.graph.nodes.find(n => n.id === edge.source_node_id);
        const targetNode = workflowForm.graph.nodes.find(n => n.id === edge.target_node_id);

        if (!sourceNode) {
          ElMessage.error(`边配置错误：找不到源节点 ${edge.source_node_id}`);
          return;
        }
        if (!targetNode) {
          ElMessage.error(`边配置错误：找不到目标节点 ${edge.target_node_id}`);
          return;
        }
      }

      saving.value = true;
      try {
        // 清理和格式化数据，确保符合后端要求
        const cleanGraph = {
          inputs: workflowForm.graph.inputs,
          nodes: workflowForm.graph.nodes.map(node => {
            const nodeData = {
              id: node.id,
              type: node.type, // datasource 或 llm
              param_mappings: node.param_mappings || {},
              position: {
                x: Math.round(node.position?.x || 0),
                y: Math.round(node.position?.y || 0)
              }
            };

            // 根据节点类型处理配置
            if (node.type === "datasource") {
              nodeData.config = {
                source_id: node.config?.source_id || ""
              };

              // 为数据源节点生成正确的参数映射
              const sourceId = node.config?.source_id;
              if (sourceId) {
                // 查找对应的数据源类型
                for (const source of dataSources.value) {
                  for (const dataType of source.data_types) {
                    if (dataType.id === sourceId) {
                      // 为每个必需参数生成正确的映射
                      const correctMappings = {};
                      dataType.required_params.forEach(paramName => {
                        correctMappings[paramName] = `workflow.input.${paramName}`;
                      });
                      nodeData.param_mappings = correctMappings;
                      break;
                    }
                  }
                }
              }
            } else if (node.type === "llm") {
              // LLM节点配置，将系统模版名称和用户模版名称放在config中
              nodeData.config = {
                system_template_name: node.config?.system_template_name || "",
                user_template_name: node.config?.user_template_name || "",
                adhoc_instruction: node.config?.adhoc_instruction || ""
              };

              // 为LLM节点生成正确的参数映射
              const datasourceNodes = workflowForm.graph.nodes.filter(n => n.type === "datasource");
              if (datasourceNodes.length > 0) {
                nodeData.param_mappings = {
                  input: `nodes.${datasourceNodes[0].id}.data`
                };
              }
            }

            return nodeData;
          }),
          edges: workflowForm.graph.edges.map(edge => ({
            source_node_id: edge.source_node_id,
            source_output: edge.source_output || "data",
            target_node_id: edge.target_node_id,
            target_input: edge.target_input || "input"
          })),
          output_source: workflowForm.graph.output_source,
          workflow_type: "service_analysis" // 改为service_analysis
        };

        // 使用真实数据，但添加详细调试
        const requestData = {
          name: workflowForm.name,
          dify_app_id: "test-app-id",
          graph: cleanGraph
        };

        const response = await createWorkflow(requestData);

        if (response.success) {
          ElMessage.success("工作流保存成功");
          saveVisible.value = false;
        } else {
          ElMessage.error(response.message || "保存失败");
        }
      } catch (error) {
        if (error.response?.data?.message) {
          ElMessage.error("保存失败: " + error.response.data.message);
        } else {
          ElMessage.error("保存失败: " + error.message);
        }
      } finally {
        saving.value = false;
      }
    };

    // 添加输入参数
    const addInput = () => {
      workflowForm.graph.inputs.push({
        name: "",
        type: "string",
        description: "",
        required: true
      });
    };

    // 删除输入参数
    const removeInput = index => {
      workflowForm.graph.inputs.splice(index, 1);
    };

    // 预览工作流
    const previewWorkflow = () => {
      previewVisible.value = true;
    };

    // 测试数据源接口
    const testDataSources = async () => {
      try {
        ElMessage.info("正在测试数据源接口...");
        const response = await getDataSources();
        if (response.success) {
          ElMessage.success(`数据源接口测试成功，获取到 ${response.data.length} 个数据源`);
        } else {
          ElMessage.error("数据源接口测试失败: " + response.message);
        }
      } catch (error) {
        ElMessage.error("数据源接口测试失败: " + error.message);
      }
    };

    // 测试提示模板接口
    const testPromptTemplates = async () => {
      try {
        ElMessage.info("正在测试提示模板接口...");
        const response = await getPromptTemplates();
        if (response.success) {
          ElMessage.success(`提示模板接口测试成功，获取到 ${response.data.length} 个模板`);
        } else {
          ElMessage.error("提示模板接口测试失败: " + response.message);
        }
      } catch (error) {
        ElMessage.error("提示模板接口测试失败: " + error.message);
      }
    };

    onMounted(() => {
      initLogicFlow();
      fetchDataSources();
      fetchPromptTemplates();
    });

    return {
      loading,
      saving,
      saveVisible,
      previewVisible,
      selectedNode,
      workflowForm,
      lf,
      dataSources,
      promptTemplates,
      addNode,
      updateNode,
      addInput,
      removeInput,
      createExampleWorkflow,
      resetWorkflow,
      saveWorkflow,
      previewWorkflow,
      testDataSources,
      testPromptTemplates
    };
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .top-container {
    padding: 10px 20px;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    gap: 10px;
  }

  .logic-flow-view {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  .preview-content {
    .preview-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 16px;
      }
    }
  }

  .input-params-section {
    .input-param-item {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 15px;

      .input-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        font-weight: 500;
      }
    }
  }

  .test-section {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
}

// 自定义节点样式
:deep(.custom-node) {
  width: 200px;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .node-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-weight: 500;

    i {
      font-size: 16px;
    }
  }

  .node-content {
    .node-info {
      font-size: 12px;
      color: #909399;
      margin-bottom: 10px;
    }

    .node-ports {
      display: flex;
      justify-content: space-between;
      gap: 10px;

      .port {
        flex: 1;
        height: 30px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }

        &.input-port {
          background-color: #f0f9ff;
        }

        &.output-port {
          background-color: #fff7e6;
        }
      }
    }
  }
}

:deep(.datasource-node) {
  border: 2px solid #67c23a;
}

:deep(.llm-node) {
  border: 2px solid #e6a23c;
}
</style>
