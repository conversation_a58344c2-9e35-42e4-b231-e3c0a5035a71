<template>
  <div class="tb-header"><el-button type="primary" @click="addRecordFun">新增</el-button></div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="moduleLabel" label="告警模块" />
    <my-column property="metricsLabel" label="告警规则" />
    <my-column property="level" label="告警级别">
      <template #default="scope">
        <el-tag
          :type="scope.row.level === 0 ? 'danger' : scope.row.level === 1 ? 'warning' : 'success'"
        >
          {{ scope.row.level === 0 ? "紧急告警" : scope.row.level === 1 ? "严重告警" : "一般告警" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="noticeWayDesc" label="通知方式" />
    <my-column property="noticeTemplate" label="通知模板" />
    <my-column property="enable" label="是否启用">
      <template #default="scope">
        <el-switch
          v-model="scope.row.enable"
          @change="enableChange($event, scope.row)"
          :active-value="1"
          :inactive-value="0"
          inline-prompt
        ></el-switch>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
      <template #default="scope">
        <span class="operate" @click="edit(scope.row)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="deleteRule(scope.row)">删除</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="DeleteAlarmRrule"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 新增编辑抽屉-->
  <div>
    <el-drawer v-model="drawer" :title="title" :close-on-click-modal="false" size="40%">
      <el-form :model="form" ref="formRef" label-width="auto" style="padding: 15px">
        <el-form-item label="告警模块：">
          <el-cascader
            v-model="form.moduleIdList"
            :options="options"
            clearable
            placeholder="请选择告警模块"
            :props="{ value: 'id', label: 'label' }"
            @change="handleModuleChange"
          />
        </el-form-item>
        <el-col>
          <el-form-item label="告警指标：">
            <el-col :span="8">
              <el-select placeholder="请选择告警指标" v-model="form.metricsId">
                <el-option
                  :label="item.label"
                  :value="item.id"
                  v-for="(item, index) in metricsSearchOptions"
                  :key="item.id"
                />
              </el-select>
            </el-col>
            <el-col :span="5" class="ml-3">
              <el-select placeholder="" v-model="form.operator">
                <el-option label=">" :value="0" />
                <el-option label="<" :value="1" />
                <el-option label="=" :value="2" />
              </el-select>
            </el-col>
            <el-col :span="8" class="ml-3">
              <el-input-number
                v-model="form.threshold"
                :controls="false"
                :min="1"
                placeholder="请输入阈值"
              />
            </el-col>
          </el-form-item>
        </el-col>
        <el-form-item label="告警级别：">
          <el-select
            v-model="form.level"
            clearable
            style="width: 200px"
            placeholder="请选择告警级别"
          >
            <el-option label="紧急告警" :value="0" />
            <el-option label="严重告警" :value="1" />
            <el-option label="一般告警" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知模版：">
          <el-input
            v-model="form.noticeTemplate"
            type="textarea"
            :rows="5"
            style="width: 350px"
            :autosize="{ minRows: 3, maxRows: 5 }"
            placeholder="请输入通知模版"
          />
          <div
            class="environment-variables"
            v-show="environmentVariables && environmentVariables.length > 0"
          >
            <div>支持的环境变量：</div>
            <ul>
              <li v-for="(variable, index) in environmentVariables" :key="index">
                {{ variable }}
              </li>
            </ul>
          </div>
        </el-form-item>
        <el-form-item label="通知方式：">
          <el-checkbox-group
            v-model="form.noticeWayIdList"
            clearable
            style="width: 200px"
            placeholder="请选择通知方式"
          >
            <el-checkbox label="短信" value="0" />
            <el-checkbox label="邮件" value="1" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="通知对象：">
          <el-select
            multiple
            v-model="form.noticeGroupIdList"
            clearable
            style="width: 200px"
            placeholder="请选择通知对象"
          >
            <el-option
              v-for="item in notifierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="demo-drawer__footer">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="formCommit"> 确定 </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  moduleList,
  metricsList,
  notifierGroup,
  ruleList,
  ruleAdd,
  ruleDetail,
  ruleEdit,
  ruleChange,
  ruleDelete
} from "@/api/warnManage/strategy";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appId = useApplicationStore.appId;
//新增编辑抽屉标题
const title = ref("");
//表单数据
const form = reactive({
  delId: null as any,
  id: null,
  moduleIdList: [],
  moduleId: "",
  metricsId: "",
  operator: 0,
  threshold: null,
  level: null,
  noticeTemplate: "",
  noticeWay: "",
  noticeWayIdList: [],
  noticeGroup: "",
  noticeGroupIdList: []
});
//清空表单
const initRecord = reactive({
  id: null,
  moduleIdList: [],
  moduleId: "",
  metricsId: "",
  operator: 0,
  threshold: null,
  level: null,
  noticeTemplate: "",
  noticeWay: "",
  noticeWayIdList: [],
  noticeGroup: "",
  noticeGroupIdList: []
});
//列表参数
const pageParams = reactive({
  appid: "",
  page: 1,
  rows: 10
});
// 存储通知对象的选项
const notifierOptions = ref([]);
//环境变量
const environmentVariables = ref([]);
//删除弹窗是否显示
const deleteVisible = ref(false);
//新增编辑抽屉是否显示
const drawer = ref(false);
//级联数据
const options = ref([]);
//loading动画
const tableLoading = ref(false);
const formRef = ref();
const submitType = ref(1);

//点击新增按钮
function addRecordFun() {
  drawer.value = true;
  environmentVariables.value = [];
  Object.assign(form, initRecord);
  title.value = "新增告警策略";
  submitType.value = 1;
}
//点击编辑
function edit(row: any) {
  submitType.value = 2;
  drawer.value = true;
  environmentVariables.value = [];
  Object.assign(form, initRecord);
  title.value = "编辑告警策略";
  ruleDetail(row.id).then(res => {
    if (res.code === 0) {
      const entity = res.entity;
      form.id = entity.id;
      form.moduleId = entity.moduleId;
      form.metricsId = entity.metricsId;
      form.operator = entity.operator;
      form.threshold = entity.threshold;
      form.level = entity.level;
      form.noticeWay = entity.noticeWay;
      form.noticeWayIdList = entity.noticeWay.split(",");
      form.noticeGroup = entity.noticeGroup;
      form.noticeGroupIdList = entity.noticeGroup.split(",").map(Number);
      form.noticeTemplate = entity.noticeTemplate;
      form.moduleIdList = [];
      metricsSearchOptions.value = metricsOptions.value.filter(
        (item: any) => item.moduleId === entity.moduleId
      );
      for (let i = 0; i < moduleOptions.value.length; i++) {
        const item: any = moduleOptions.value[i];
        if (item.id === entity.moduleId) {
          form.moduleIdList.push(item.pid, entity.moduleId);
          environmentVariables.value = item.variable.split(",");
        }
      }
    }
  });
}
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const list = reactive({
  records: [],
  total: 0
});
//获取列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = appId;
  ruleList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//获取所有告警模块
const moduleOptions = ref([]);
function getmoduleList() {
  moduleList()
    .then(response => {
      if (response.code === 0) {
        const records = response.records;
        moduleOptions.value = response.records;
        let idMap = new Map();
        records.forEach((record: any) => {
          idMap.set(record.id, record);
        });
        options.value = [];
        records.forEach((record: any) => {
          if (record.pid === 0) {
            options.value.push(record);
          } else {
            let parentRecord = idMap.get(record.pid);
            if (parentRecord) {
              if (!parentRecord.children) {
                parentRecord.children = [];
              }
              parentRecord.children.push(record);
            }
          }
        });
      }
    })
    .catch(error => {
      console.log(error);
    });
}
//切换告警模块
function handleModuleChange(value: any) {
  form.metricsId = "";
  if (value && value.length > 0) {
    form.moduleId = value[1];
    const secondId = value[1]; //二级分类的id
    metricsSearchOptions.value = metricsOptions.value.filter(
      (item: any) => item.moduleId === secondId
    ); //过滤出二级分类对应的指标列表
    if (metricsSearchOptions.value && metricsSearchOptions.value.length > 0) {
      form.metricsId = metricsSearchOptions.value[0].id;
    }
    let data: any = moduleOptions.value.find((item: any) => item.id === secondId);
    if (data && data.variable && data.variable.length > 0) {
      environmentVariables.value = data.variable.split(",");
    }
  } else {
    environmentVariables.value = []; //清空环境变量列表
  }
}
//获取所有告警指标
const metricsOptions = ref([]);
const metricsSearchOptions = ref([]);
function getmetricsList() {
  metricsList()
    .then(response => {
      if (response.code === 0) {
        metricsOptions.value = response.records;
        metricsSearchOptions.value = response.records;
      }
    })
    .catch(error => {
      console.log(error);
    });
}
//查询应用下的所有告警对象组键值对
function getnotifierGroup() {
  notifierGroup({ appid: appId }).then(response => {
    if (response.code === 0) {
      notifierOptions.value = response.records;
    }
  });
}
//新增编辑数据
function formCommit() {
  if (form.noticeWayIdList && form.noticeWayIdList.length > 0) {
    form.noticeWay = form.noticeWayIdList.join(",");
  }
  if (form.noticeGroupIdList && form.noticeGroupIdList.length > 0) {
    form.noticeGroup = form.noticeGroupIdList.join(",");
  }
  if (submitType.value === 1) {
    ruleAdd({ ...form, appid: appId }).then(response => {
      if (response.code === 0) {
        ElMessage.success("新增成功");
        drawer.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  } else {
    ruleEdit({ ...form, appid: appId }).then(response => {
      if (response.code === 0) {
        ElMessage.success("编辑成功");
        drawer.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  }
}
//修改告警规则状态
function enableChange(status: any, row: any) {
  if (!row.id) {
    return;
  }
  ruleChange(row.id, status).then(response => {
    if (response.code === 0) {
      let str = status === 1 ? "启用成功" : "禁用成功";
      ElMessage.success(str);
      loadData();
    } else {
      ElMessage.error(response.desc);
    }
  });
}
//删除告警规则
function deleteRule(row: any) {
  form.delId = row.id;
  deleteVisible.value = true;
}
function DeleteAlarmRrule() {
  deleteVisible.value = false;
  ruleDelete(form.delId).then(response => {
    if (response.code === 0) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(response.desc);
    }
  });
}
onMounted(() => {
  getmoduleList();
  getmetricsList();
  getnotifierGroup();
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.environment-variables {
  margin-top: 10px;
}

.environment-variables ul {
  list-style-type: disc;
  list-style-position: inside;
  border: 1px solid #dcdcdc;
  border-radius: 5px;
  padding-left: 20px;
}

.environment-variables li {
  color: #666;
  width: 450px;
  word-wrap: break-word;
  padding: 5px 10px;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
</style>
