<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="snmp设置" name="config">
      <config v-if="activeName === 'config'"></config>
    </el-tab-pane>
    <el-tab-pane label="snmp客户端设置" name="collect">
      <collect v-if="activeName === 'collect'"></collect>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import config from "./config/index.vue";
import collect from "./collect/index.vue";
import type { TabsPaneContext } from "element-plus";

const route = useRoute();
const router = useRouter();

const activeName = ref(route.query.tab ? String(route.query.tab) : "config");

watch(
  () => route.query.tab,
  newTab => {
    if (newTab) {
      activeName.value = String(newTab);
    }
  }
);

watch(activeName, newActiveName => {
  router.replace({ query: { tab: newActiveName } });
});

const handleClick = (tab: TabsPaneContext) => {
  console.log(tab);
};
</script>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
