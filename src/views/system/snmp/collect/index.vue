<template>
  <div class="title">
    <div class="mark"></div>
    <div>snmp客户端设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主机名称"
        clearable
        filterable
        v-model="pageParams.hostName"
      ></el-input>
      <el-button type="primary" class="ml-15px" :icon="Search" @click="search">搜索</el-button>
    </div>
    <div>
      <el-button type="primary" @click="openAddDialog">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="id" label="ID" />
    <my-column property="hostIp" label="主机IP" />
    <my-column property="hostName" label="主机名称" />
    <my-column property="createDt" label="创建时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createDt) }}</span>
      </template>
    </my-column>
    <my-column property="updateDt" label="更新时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.updateDt) }}</span>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="handleEdit(scope.row)">编辑 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="promptDelete(scope.row.id)">删除 </span>
        <!-- <span class="divider"> / </span>
        <span class="operate" @click="query(scope.row)">详情 </span> -->
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="主机IP：">
        <el-input v-model="state.form.hostIp" placeholder="请输入主机IP" clearable></el-input>
      </el-form-item>
      <el-form-item label="主机名称：">
        <el-input v-model="state.form.hostName" placeholder="请输入主机名称" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确定要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <div>
    <el-drawer v-model="queryVisible" :title="queryTitle" size="60%">
      <MyTable
        :data="lists.records"
        :total="lists.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handlesizeChange"
        @currentChange="handlecurrentChange"
      >
        <my-column property="id" label="ID" />
        <my-column property="hostIp" label="主机IP" />
        <my-column property="hostName" label="主机名称" />
      </MyTable>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import {
  snmpClientList,
  oidList,
  collectList,
  getAllApp,
  addSnmpCollect,
  editSnmp,
  deleteSnmpCollect,
  editSnmpCollect,
  detailSnmpCollect
} from "@/api/log/snmp";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/dateStr";
const tableLoading = ref(false);
const dialogFormVisible = ref(false); //新增编辑弹窗是否显示
const deleteVisible = ref(false); //删除弹窗是否显示
const queryVisible = ref(false);
const appOptions = ref<{ appid: string; name: string }[]>([]); //应用名称
const title = ref("");
const queryTitle = ref("");
const oidOptions = ref<{ oid: string; name: string }[]>([]); // oid列表选项
const collectOptions = ref<{ id: string; hostName: string }[]>([]); // collectId选项
const formatOids = (ipString: string) => {
  return ipString.split(",").join("<br>");
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const pageParams = reactive({
  hostName: "",
  page: 1,
  rows: 10
});
const list = reactive({
  records: [],
  total: 0
});
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
function loadData() {
  tableLoading.value = true;
  pageParams.hostName = pageParams.hostName.trim();
  snmpClientList(pageParams)
    .then(res => {
      if (res.code === 0) {
        list.records = res.records;
        list.total = Number(res.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      tableLoading.value = false;
    });
}
//获取应用名称
const getApp = async () => {
  try {
    const response = await getAllApp();
    if (response.code === 0) {
      appOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
//获取oid列表
const getOid = async () => {
  try {
    const response = await oidList();
    if (response.code === 0) {
      oidOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
//查询Collect所在主机信息
const getCollect = async () => {
  try {
    const response = await collectList();
    if (response.code === 0) {
      collectOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
const state = reactive({
  form: {
    id: "",
    appId: "",
    hostIp: "",
    hostName: "",
    hostPort: "",
    version: "v1",
    community: "public",
    userName: "",
    authProto: "",
    authPass: "",
    privProto: "",
    privPass: "",
    oids: "" as string | string[],
    collectId: ""
  }
});
const handleEdit = (val: any) => {
  dialogUse.value = false;
  title.value = "编辑snmp客户端设置";
  dialogFormVisible.value = true;
  state.form = JSON.parse(JSON.stringify(val));
  // try {
  //   const res = await editSnmpCollect(row.id);
  //   if (res.code === 0) {
  //     state.form = res.entity;
  //     // if (typeof state.form.oids === "string") {
  //     //   state.form.oids = state.form.oids.split(",");
  //     // }
  //   }
  // } catch (error) {
  //   console.error(error);
  // }
};

const submitForm = async () => {
  try {
    const { id, ...formData } = state.form;
    const res = await (dialogUse.value
      ? addSnmpCollect(formData)
      : editSnmpCollect({ id, ...formData }));
    if (res.code === 0) {
      ElMessage.success(id ? "编辑成功" : "新增成功");
      dialogFormVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error(error);
  }
};

const handleDelete = async (id: string) => {
  try {
    const response = await deleteSnmpCollect({ id });
    if (response.code === 0) {
      ElMessage({ message: "删除成功", type: "success" });
      loadData();
    }
  } catch (error) {
    console.log(error);
  }
};

const dialogUse = ref(false);
const openAddDialog = () => {
  dialogUse.value = true;
  Object.assign(state.form, {
    appId: "",
    hostIp: "",
    hostName: "",
    hostPort: "",
    version: "v1",
    community: "public",
    userName: "",
    authProto: "",
    authPass: "",
    privProto: "",
    privPass: "",
    oids: "",
    collectId: ""
  });
  title.value = "新增snmp客户端设置";
  dialogFormVisible.value = true;
};

const confirmDelete = () => {
  handleDelete(currentDeleteId);
  deleteVisible.value = false;
};

let currentDeleteId = "";

const promptDelete = (id: string) => {
  currentDeleteId = id;
  deleteVisible.value = true;
};
//详情参数
const dataToSend = reactive({
  hostIp: "",
  hostName: "",
  hostPort: "",
  appId: "",
  version: "",
  community: "",
  oid: "",
  collectId: "",
  page: 1,
  rows: 10
});
const query = (row: any) => {
  queryVisible.value = true;
  queryTitle.value = "查询结果 【" + row.hostName + "】";
  detailSnmpCollect({ id: row.id })
    .then(response => {
      if (response.code === 0) {
        lists.records = response.entity;
        lists.total = Number(response.total);
      }
    })
    .catch(error => {
      console.log(error);
    });
};
const lists = reactive({
  records: [],
  total: 0
});
//修改每页条数
const handlesizeChange = (val: number) => {
  dataToSend.rows = val;
};
//详情
const handlecurrentChange = (val: number) => {
  dataToSend.page = val;
};

onMounted(() => {
  loadData();
  getApp();
  getOid();
  getCollect();
});
</script>
<style lang="scss" scoped>
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ip-tooltip.el-tooltip__popper {
  max-width: 300px;
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
</style>
