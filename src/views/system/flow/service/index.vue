<template>
  <div class="title">
    <div class="mark"></div>
    <div>服务映射</div>
  </div>
  <div class="tb-header">
    <div>
      <el-select
        style="width: 240px"
        placeholder="请选择所属单位"
        v-model="pageParams.unitid"
        clearable
        filterable
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.appid"
        clearable
        filterable
        placeholder="请选择应用"
      >
        <el-option v-for="app in apps" :key="app.appid" :label="app.name" :value="app.appid" />
      </el-select>
      <el-input
        style="width: 240px; margin-left: 15px"
        placeholder="请输入服务名"
        v-model="pageParams.serviceName"
        clearable
        filterable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="serviceName" label="服务名" />
    <my-column property="startPort" label="开始端口" />
    <my-column property="endPort" label="结束端口" />
    <my-column property="protocol" label="协议" />
    <my-column property="ipAddresses" label="包含IP" width="380" />
    <my-column property="description" label="描述" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="handleEdit(scope)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="handleDelete(scope)">删除</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 新增编辑弹窗-->
  <el-dialog
    v-model="dialogFormVisible"
    :align-center="true"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="所属单位：">
        <el-select v-model="state.form.unitid" clearable filterable placeholder="请选择所属单位">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用：">
        <el-select v-model="state.form.appid" clearable filterable placeholder="请选择应用">
          <el-option v-for="app in apps" :key="app.appid" :label="app.name" :value="app.appid">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务名：">
        <el-input v-model="state.form.serviceName" placeholder="请输入服务名" clearable></el-input>
      </el-form-item>
      <el-form-item label="开始端口：">
        <el-input v-model="state.form.startPort" placeholder="请输入开始端口" clearable></el-input>
      </el-form-item>
      <el-form-item label="结束端口：">
        <el-input v-model="state.form.endPort" placeholder="请输入结束端口" clearable></el-input>
      </el-form-item>
      <el-form-item label="协议：">
        <el-select v-model="state.form.protocol" placeholder="请选择协议" clearable filterable>
          <el-option label="ANY" value="ANY" />
          <el-option label="TCP" value="TCP" />
          <el-option label="UDP" value="UDP" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述：">
        <el-input
          v-model="state.form.description"
          placeholder="请输入描述"
          clearable
          type="textarea"
          :rows="3"
        ></el-input>
      </el-form-item>
      <el-form-item label="IP列表：">
        <el-input
          v-model="state.form.ipAddresses"
          placeholder="请输入IP列表"
          clearable
          type="textarea"
          :rows="3"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import {
  serviceMapping,
  appName,
  getServiceDetail,
  deleteService,
  addServiceMapping,
  editServiceMapping
} from "@/api/system/flow";
import { getOrganEntries } from "@/api/system/app";
import { applicationStore } from "@/store/modules/application";
import { ElMessage } from "element-plus";
const useApplicationStore = applicationStore();
const tableLoading = ref(false);
const deleteVisible = ref(false);
const dialogFormVisible = ref(false);
const title = ref("");
const options = ref([] as any[]);
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.page_size = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const list = reactive({
  records: [] as Array<{ appid: string; ruleId: string; [key: string]: any }>,
  total: 0
});
function search() {
  pageParams.page = 1;
  loadData();
}
const pageParams = reactive({
  page: 1,
  page_size: 10,
  unitid: useApplicationStore.organId,
  appid: "",
  serviceName: ""
});
function loadData() {
  tableLoading.value = true;
  serviceMapping(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//获取单位条目
const OrganEntries = async () => {
  try {
    const res = await getOrganEntries({});
    options.value = res.records;
  } catch (err) {
    console.log(err);
  }
};
//获取应用
const apps = ref<{ appid: string; name: string }[]>([]);
const fetchApps = async () => {
  try {
    const response = await appName();
    if (response.code === 0) {
      apps.value = response.records.map((app: any) => ({
        appid: app.appid,
        name: app.name
      }));
    }
  } catch (error) {
    console.error("获取应用数据异常:", error);
  }
};
const state = reactive({
  form: {
    unitid: "",
    appid: "",
    serviceName: "",
    startPort: "",
    endPort: "",
    protocol: "",
    description: "",
    ipAddresses: "",
    ruleid: ""
  },
  delAppid: "",
  delRuleId: ""
});

onMounted(() => {
  loadData();
  OrganEntries();
  fetchApps();
});

// 新增或编辑服务映射
function saveServiceMapping(formData: any) {
  const apiCall = state.form.ruleid ? editServiceMapping : addServiceMapping;
  const successMessage = state.form.ruleid ? "编辑成功" : "新增成功";
  apiCall(formData)
    .then(response => {
      if (response.code === 0) {
        ElMessage.success(successMessage);
        dialogFormVisible.value = false;
        loadData();
      }
    })
    .catch(error => {
      console.error("操作失败:", error);
    });
}

// 在表单提交时调用saveServiceMapping
function handleSubmit() {
  // 将ipAddresses从字符串转换为数组
  const ipArray = state.form.ipAddresses.split(",").map((ip: string) => ip.trim());
  const formData = { ...state.form, ipAddresses: ipArray };
  saveServiceMapping(formData);
}

// 获取服务详情
async function fetchServiceDetail(appid: string, ruleId: string) {
  try {
    const response = await getServiceDetail(appid, ruleId);
    if (response.code === 0) {
      const data = response.entity;
      state.form = {
        unitid: data.unitid,
        appid: data.appid,
        serviceName: data.serviceName,
        startPort: data.startPort.toString(),
        endPort: data.endPort.toString(),
        protocol: data.protocol,
        description: data.description,
        ipAddresses: data.ipAddresses.join(", "),
        ruleid: data.ruleid
      };
      dialogFormVisible.value = true;
    }
  } catch (error) {
    console.error("获取服务详情异常:", error);
  }
}

// 删除服务
async function removeService(appid: string, ruleId: string) {
  try {
    const response = await deleteService(appid, ruleId);
    if (response.code === 0) {
      console.log("删除成功");
      loadData();
    }
  } catch (error) {
    console.error("删除服务异常:", error);
  }
}

// 在表格操作中调用编辑和删除
function handleEdit(scope: any) {
  const { appid, ruleid } = scope.row;
  if (appid && ruleid) {
    fetchServiceDetail(appid, ruleid).then(() => {
      title.value = "编辑服务映射";
      dialogFormVisible.value = true;
    });
  } else {
    console.error("缺少appid或ruleId");
  }
}

function handleDelete(scope: any) {
  const { appid, ruleid } = scope.row;
  if (appid && ruleid) {
    state.delAppid = appid;
    state.delRuleId = ruleid;
    deleteVisible.value = true;
  } else {
    console.error("缺少appid或ruleId");
  }
}

// 确认删除
function confirmDelete() {
  removeService(state.delAppid, state.delRuleId).then(() => {
    ElMessage.success("删除成功");
    deleteVisible.value = false;
  });
}

function handleAdd() {
  title.value = "新增服务映射";
  dialogFormVisible.value = true;
  state.form = {
    unitid: "",
    appid: "",
    serviceName: "",
    startPort: "",
    endPort: "",
    protocol: "",
    description: "",
    ipAddresses: "",
    ruleid: ""
  };
}
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
