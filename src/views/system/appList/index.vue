<template>
  <TitlecCom :title="`应用管理`" />
  <div class="tb-header">
    <div>
      <el-cascader
        placeholder="请选择所属单位"
        :props="props1"
        :options="treesData"
        v-model="pageParams.organId"
        filterable
        clearable
        style="width: 200px; margin-right: 15px"
      />
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="pageParams.name"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="addApply">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="appid" label="应用ID" width="120" />
    <my-column property="name" label="应用名称" width="300" />
    <my-column property="organName" label="单位名称" width="250" />
    <my-column property="contact" label="联系人" />
    <my-column property="mobile" label="联系人手机" />
    <my-column property="email" label="联系人邮箱" />
    <my-column property="remark" label="备注" />
    <my-column property="createdAt" label="添加时间" width="180" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="180">
      <template #default="scope">
        <span class="operate" @click="edit(scope.row)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="showDel(scope.row.id)">删除</span>
        <!-- <span class="divider"> / </span>
        <span class="operate" @click="bindUserFun(scope.row)">绑定用户</span> -->
      </template>
    </my-column>
  </MyTable>
  <!-- 新增编辑弹窗-->
  <el-dialog
    v-model="dialogFormVisible"
    :align-center="true"
    :title="title"
    width="700"
    :close-on-click-modal="false"
    v-if="dialogFormVisible"
  >
    <el-form :model="state.form" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="应用ID：">
            <el-input v-model="state.form.appid" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用名称：">
            <el-input v-model="state.form.name" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系人：">
            <el-input v-model="state.form.contact" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人手机：">
            <el-input v-model="state.form.mobile" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系人邮箱：">
            <el-input v-model="state.form.email" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属单位：">
            <el-cascader
              placeholder="请选择所属单位"
              :props="props1"
              :options="treesData"
              v-model="state.form.organId"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input v-model="state.form.remark" type="textarea" clearable :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="formCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="删除应用"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--已绑定用户弹窗-->
  <el-dialog
    :align-center="true"
    draggable
    v-model="dialogVisible"
    :title="bindUserTitle"
    :close-on-click-modal="false"
    width="1100"
    v-if="dialogVisible"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px"
          placeholder="请输入关键字"
          v-model="dataToSend.name"
          clearable
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchFor"
          >搜索</el-button
        >
      </div>
      <div>
        <el-button type="primary" @click="dialogTableVisible = true">添加绑定</el-button>
      </div>
    </div>
    <MyTable
      height="300"
      :data="userData"
      :total="userDataSize"
      style="width: 100%"
      @sizeChange="sizeChange"
      @currentChange="currentChange"
    >
      <MyColumn prop="id" label="用户ID" />
      <MyColumn prop="name" label="用户姓名" />
      <MyColumn prop="mobile" label="手机号" />
      <MyColumn label="锁定状态" width="150">
        <template #default="scope">
          <el-tag :type="scope.row.lockStatus === 0 ? 'success' : 'danger'">
            {{ scope.row.lockStatus === 0 ? "未锁定" : "已锁定" }}
          </el-tag>
        </template>
      </MyColumn>
      <MyColumn prop="createdAt" label="创建时间" />
      <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
        <template #default="scope">
          <span class="operate" @click="unbind(scope)">解绑</span>
        </template>
      </my-column>
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--绑定用户弹窗-->
  <el-dialog
    draggable
    :align-center="true"
    v-model="dialogTableVisible"
    title="请选择用户"
    :close-on-click-modal="false"
    width="900"
    v-if="dialogTableVisible"
  >
    <div class="tb-header">
      <div>
        <el-input
          style="width: 240px; margin-left: 15px"
          placeholder="请输入关键字"
          v-model="pageParam.name"
          clearable
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="searchUser"
          >搜索</el-button
        >
      </div>
    </div>
    <MyTable
      height="300"
      :data="tableData"
      :total="totalSize"
      style="width: 100%"
      @sizeChange="SizeChange"
      @currentChange="CurrentChange"
      @selection-change="handleSelectionChange"
    >
      <my-column type="selection" />
      <my-column property="name" label="用户姓名" />
      <my-column property="mobile" label="手机号" />
      <MyColumn label="锁定状态" width="150">
        <template #default="scope">
          <el-tag :type="scope.row.lockStatus === 0 ? 'success' : 'danger'">
            {{ scope.row.lockStatus === 0 ? "未锁定" : "已锁定" }}
          </el-tag>
        </template>
      </MyColumn>
      <!-- <MyColumn label="用户类型">
        <template #default="scope">
          <span>{{ scope.row.type === 0 ? "管理员" : "普通用户" }}</span>
        </template>
      </MyColumn> -->
      <my-column property="createdAt" label="创建时间" />
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" @click="submit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!--解绑弹窗-->
  <el-dialog
    :align-center="true"
    v-model="unbindVisible"
    title="解绑应用"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要解除绑定吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="unbindVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBind"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { getOrganList } from "@/api/system/organ";
import TitlecCom from "@/components/TitleCom/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import {
  getOrganEntries,
  listApp,
  getApp,
  updateApp,
  insertApp,
  delApp,
  bindUser,
  user,
  unBindUser
} from "@/api/system/app";
import { userList } from "@/api/system/index";

const ids: any = ref([]);
const bindId: any = ref("");
const unbindId: any = ref("");
const props1 = {
  checkStrictly: true
};
const handleSelectionChange = (val: any) => {
  let arr: any = [];
  val.forEach((element: any) => {
    arr.push(element.id);
  });
  ids.value = arr;
};
const submit = () => {
  if (ids.value.length == 0) {
    ElMessage.error("请选择要绑定的用户");
  } else {
    let userIds = ids.value.join(",");
    bindUser(bindId.value, { userIds }).then(response => {
      if (response.code === 0) {
        userlist();
        ElMessage.success("绑定成功");
        dialogTableVisible.value = false;
      } else {
        ElMessage.error(response.desc);
      }
    });
  }
};
// 定义用户数据类型
interface User {
  id?: number;
  type?: number;
  name?: string;
  mobile?: string;
  email: string;
  remark?: string;
  createdAt?: string;
  lockStatus?: number;
  password?: string;
  confirmPassword?: string;
}
const pageParam = reactive({
  page: 1,
  rows: 10,
  name: "",
  type: ""
});
//修改每页条数
const SizeChange = (val: number) => {
  pageParam.rows = val;
  fetchUserList();
};
//分页
const CurrentChange = (val: number) => {
  pageParam.page = val;
  fetchUserList();
};
const tableData = ref<User[]>([]);
const totalSize = ref(0);
function searchUser() {
  pageParam.page = 1;
  fetchUserList();
}
function fetchUserList() {
  userList(pageParam).then(response => {
    if (response.code === 0) {
      tableData.value = response.records;
      totalSize.value = Number(response.total);
    }
  });
}
const dataToSend = reactive({
  page: 1,
  rows: 10,
  appId: "",
  name: "",
  type: ""
});

const bindUserFun = (row: any) => {
  dialogVisible.value = true;
  bindUserTitle.value = "【" + row.name + "】已绑定的用户";
  bindId.value = row.id;
  dataToSend.appId = row.id;
  userlist();
};
//修改每页条数
const sizeChange = (val: number) => {
  dataToSend.rows = val;
  userlist();
};
//分页
const currentChange = (val: number) => {
  dataToSend.page = val;
  userlist();
};
const userData = ref([]);
const userDataSize = ref(0);
function searchFor() {
  dataToSend.page = 1;
  userlist();
}
function userlist() {
  user(dataToSend).then(response => {
    if (response.code === 0) {
      userData.value = response.records;
      userDataSize.value = Number(response.total);
    }
  });
}
//解绑用户
const unbind = (scope: any) => {
  unbindVisible.value = true;
  unbindId.value = scope.row.id;
};

const submitBind = () => {
  unBindUser(unbindId.value, bindId.value).then(response => {
    if (response.code === 0) {
      userlist();
      ElMessage.success("解绑成功");
      unbindVisible.value = false;
    } else {
      ElMessage.error(response.desc);
    }
  });
};

//loading动画
const tableLoading = ref(true);
//新增编辑弹窗是否显示
const dialogFormVisible = ref(false);
//删除弹窗是否显示
const deleteVisible = ref(false);
//绑定用户弹窗是否显示
const dialogTableVisible = ref(false);
//已绑定用户弹窗是否显示
const dialogVisible = ref(false);
//新增编辑弹窗标题
const title = ref("");
const bindUserTitle = ref("");
const unbindVisible = ref(false);
//所属单位
const options = ref([] as any[]);
//新增编辑参数
const state = reactive({
  delId: null as any,
  form: {
    appid: "",
    name: "",
    contact: "",
    mobile: "",
    email: "",
    organId: "",
    remark: ""
    // moduleConfig: [] as any[]
  }
} as any);
//列表参数参数
const pageParams = reactive({
  organId: "",
  name: "",
  page: 1,
  rows: 10
});

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

//搜索
function search() {
  pageParams.page = 1;
  loadData();
}

//获取单位条目
const OrganEntries = async () => {
  try {
    const res = await getOrganEntries({});
    options.value = res.records;
  } catch (err) {
    console.log(err);
  }
};

// 新增数据
const addApply = () => {
  dialogFormVisible.value = true;

  // moduleConfig.value = [];
  title.value = "新增应用";
  state.form = {
    appid: (state.form.appid = Math.floor(Math.random() * 100000)
      .toString()
      .padStart(6, "0")),
    name: "",
    contact: "",
    mobile: "",
    email: "",
    organId: "",
    remark: ""
  };
};

//编辑数据
const edit = (row: any) => {
  dialogFormVisible.value = true;
  title.value = "编辑应用";
  getApp(row.id).then(res => {
    state.form = res.entity;
  });
};

//删除(获取删除id，以及弹窗显示)
function showDel(id: any) {
  state.delId = id;
  deleteVisible.value = true;
}
//删除数据
function delCommit() {
  deleteVisible.value = false;
  delApp(state.delId).then(response => {
    if (response.code === 0) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(response.desc);
    }
  });
}
//新增编辑数据
function formCommit() {
  const params = {
    ...state.form,
    organId: Array.isArray(state.form.organId)
      ? state.form.organId.slice(-1)[0]
      : state.form.organId
  };
  if (state.form.id) {
    updateApp(params).then(response => {
      if (response.code === 0) {
        ElMessage.success("修改成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  } else {
    insertApp({
      ...state.form,
      organId: Array.isArray(state.form.organId)
        ? state.form.organId.slice(-1)[0]
        : state.form.organId
    }).then(response => {
      if (response.code === 0) {
        ElMessage.success("新增成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  }
}

const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.name = pageParams.name.trim();
  const params = {
    ...pageParams,
    organId: pageParams.organId
      ? Array.isArray(pageParams.organId)
        ? pageParams.organId.slice(-1)[0]
        : pageParams.organId
      : ""
  };
  listApp(params)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
        pageParams.organId = "";
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
// 树形数据
const treesData = ref([]);
interface Tree {
  label: string;
  children?: Tree[];
}
const organId = ref("");
const handleNodeClick = (data: Tree) => {
  organId.value = data.id;
  loadData();
};

// 获取组织结构
const getOrganTree = async () => {
  const params = {
    name: "",
    status: 1
  };
  try {
    const res = await getOrganList(params);
    if (res.code === 0) {
      const processTreeData = (data: any[]): Tree[] => {
        return data.map(item => {
          const node: Tree = {
            label: item.name,
            value: item.id,
            id: item.id,
            children: item.children ? processTreeData(item.children) : []
          };
          return node;
        });
      };

      treesData.value = processTreeData(res.records || []);
    }
  } catch (error) {
    console.error("获取组织结构 ===>>>", error);
  }
};
const defaultProps = {
  children: "children",
  label: "label"
};
onMounted(() => {
  OrganEntries();
  loadData();
  fetchUserList();
  getOrganTree();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.show-container {
  display: flex;
  justify-content: space-between;
}
.left-container {
  width: 19%;
}
.right-container {
  width: 80%;
}
</style>
