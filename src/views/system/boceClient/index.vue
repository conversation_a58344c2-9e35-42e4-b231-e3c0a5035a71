<template>
  <div class="title">
    <div class="mark"></div>
    <div>拨测客户端设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主机名称"
        v-model="pageParams.name"
        clearable
      ></el-input>
      <el-select
        style="width: 200px; margin-left: 15px"
        placeholder="请选择任务状态"
        v-model="pageParams.taskStatus"
        clearable
      >
        <el-option
          v-for="item in statusMap"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="addApply">新增</el-button>
    </div>
  </div>
  <MyTable
    v-loading="tableLoading"
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="clientId" label="客户端id" />
    <my-column property="ipAddress" label="主机IP" />
    <my-column property="hostName" label="主机名称" />
    <my-column property="area" label="省份" />
    <my-column property="city" label="城市" />
    <my-column property="operator" label="运营商" />
    <my-column property="status" label="状态">
      <template #default="scope">
        <el-tag :type="scope.row.status ? 'success' : 'danger'" effect="light">{{
          scope.row.status ? "已启动" : "未启动"
        }}</el-tag>
      </template>
    </my-column>
    <my-column property="updateDt" label="更新时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.updateDt) }}</span>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
      <template #default="scope">
        <span class="operate" @click="control(scope.row, 'edit')">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="control(scope.row, 'delete')">删除</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 新增编辑弹窗-->
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="700"
    :close-on-click-modal="false"
    v-if="dialogFormVisible"
  >
    <div>
      <el-form :model="state.form" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="主机IP：">
              <el-input v-model="state.form.ipAddress" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主机名称：">
              <el-input v-model="state.form.hostName" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="省份：">
              <el-input v-model="state.form.area" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市：">
              <el-input v-model="state.form.city" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="运营商：">
              <el-input v-model="state.form.operator" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12" />
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="formCommit(submmitFormCommit)">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 删除弹窗-->
  <el-dialog
    v-model="deleteVisible"
    :title="delTitle"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatTime } from "@/utils/dateStr";
import { showAllClients, delClient, addClient, editClient } from "@/api/boce/index";
//loading动画
const tableLoading = ref(true);
//删除弹窗是否显示
const deleteVisible = ref(false);
//新增编辑弹窗是否显示
const dialogFormVisible = ref(false);
//新增编辑弹窗标题
const title = ref("");
//列表参数参数
const pageParams = reactive({
  name: "",
  page: 1,
  rows: 10,
  taskStatus: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  getBoceMes();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  getBoceMes();
};
//搜索
function search() {
  pageParams.page = 1;
  getBoceMes();
}
const list = reactive({
  records: [],
  total: 0
});

const state = reactive({
  delId: null as any,
  ipAddress: "",
  hostName: "",
  area: "",
  city: "",
  operator: ""
} as any);

//删除
function showDel(id: any) {
  state.delId = id;
  deleteVisible.value = true;
}
//删除数据
function delCommit() {
  deleteVisible.value = false;
  delClient({ id: state.delId })
    .then(res => {
      if (res.code === 0) {
        ElMessage.success("删除成功");
        tableLoading.value = true;
      }
    })
    .catch(() => {
      ElMessage.error("删除失败");
    });
}
// 清空数据
const initForm = () => ({
  ipAddress: "",
  hostName: "",
  area: "",
  city: "",
  operator: ""
});
// 新增数据
const addApply = () => {
  // getBoceNodes();
  submmitFormCommit.value = true;
  title.value = "新增拨测任务";
  state.form = initForm();
  dialogFormVisible.value = true;
};
//新增编辑数据
async function formCommit(val: boolean) {
  if (val) {
    const formData = {
      ...state.form
    };
    try {
      const response = await addClient(formData);
      if (response.code === 0) {
        ElMessage.success("新增成功");
        dialogFormVisible.value = false;
        getBoceMes();
      } else {
        ElMessage.error(response.desc || "新增失败");
      }
    } catch (error) {
      ElMessage.error("新增失败");
    }
  } else {
    const formData = {
      ...state.form
    };
    try {
      const response = await editClient(formData);
      if (response.code === 0) {
        ElMessage.success("编辑成功");
        dialogFormVisible.value = false;
        getBoceMes();
      } else {
        ElMessage.error(response.desc || "编辑失败");
      }
    } catch (error) {
      ElMessage.error("编辑失败");
    }
  }
}

const delTitle = ref("");
const submmitFormCommit = ref(false);
// 控制列表
const control = (row: any, val: string | null = null) => {
  // const status = row.status;
  if (val === "delete") {
    delTitle.value = `删除【${row.name}】任务`;
    showDel(row.id);
  } else if (val === "edit") {
    submmitFormCommit.value = false;
    title.value = "编辑拨测客户端";
    state.form = row;
    dialogFormVisible.value = true;
  }
};

// 获取状态码
const statusMap = [
  { label: "未启动", value: 0 },
  { label: "已启动", value: 1 }
];

// 查询拨测列表
const getBoceMes = async () => {
  tableLoading.value = true;
  const params = {
    hostName: pageParams.name,
    page: pageParams.page,
    rows: pageParams.rows,
    status: pageParams.taskStatus
  };
  try {
    const res = await showAllClients(params);
    console.log(res);
    if (res.code === 0 && res.records.length > 0) {
      list.records = res.records;
      list.total = Number(res.total);
    }
    tableLoading.value = false;
  } catch (e) {
    tableLoading.value = false;
    ElMessage.error("获取拨测客户端数据失败");
  }
};

onMounted(() => {
  getBoceMes();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
