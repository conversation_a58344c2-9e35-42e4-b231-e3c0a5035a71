<template>
  <div class="title">
    <div class="mark"></div>
    <div>拨测设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="pageParams.name"
        clearable
      ></el-input>
      <el-select
        style="width: 200px; margin-left: 15px"
        placeholder="请选择任务状态"
        v-model="pageParams.taskStatus"
        clearable
      >
        <el-option
          v-for="item in statusMap"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="addApply">新增</el-button>
    </div>
  </div>
  <MyTable
    v-loading="tableLoading"
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="name" label="任务名称" width="300" />
    <!-- <my-column property="task_type" label="拨测地址" /> -->
    <my-column property="is_enabled" label="是否可用">
      <template #default="scope">
        <el-tag :type="scope.row.is_enabled ? 'success' : 'danger'" effect="light">{{
          scope.row.is_enabled ? "运行中" : "已暂停"
        }}</el-tag>
      </template>
    </my-column>
    <my-column property="run_enable" label="是否运行一次">
      <template #default="scope">
        <el-tag :type="scope.row.run_enable ? 'success' : 'danger'" effect="light">{{
          scope.row.run_enable ? "是" : "否"
        }}</el-tag>
      </template>
    </my-column>
    <!-- <my-column property="scheduling_status" label="调度状态" /> -->
    <my-column property="scheduling_status" label="调度状态">
      <template #default="scope">
        <span>{{ formatTaskType(scope.row.scheduling_status) }}</span>
      </template>
    </my-column>
    <my-column property="frequency_cron" label="调度表达式" />
    <my-column property="next_run_at" label="下次调度时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.next_run_at) }}</span>
      </template>
    </my-column>
    <my-column property="updated_at" label="更新时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.updated_at) }}</span>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
      <template #default="scope">
        <span class="operate" v-if="scope.row.is_enabled" @click="control(scope.row, 'pause')"
          ><span class="divider"> </span>暂停</span
        >
        <span
          class="operate"
          v-else-if="!scope.row.is_enabled"
          @click="control(scope.row, 'restore')"
          ><span class="divider"></span>恢复</span
        >
        <span class="divider"> / </span>
        <span class="operate" @click="control(scope.row, 'delete')">删除</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 新增编辑弹窗-->
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="700"
    :close-on-click-modal="false"
    v-if="dialogFormVisible"
  >
    <div v-loading="nodesLoading">
      <el-form :model="state.form" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称：">
              <el-input v-model="state.form.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务描述：">
              <el-input v-model="state.form.description" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="任务类型：">
              <el-input v-model="state.form.taskType" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交超时时间：">
              <el-input type="number" v-model="state.form.executionTimeouts" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="拨测url：">
              <el-input v-model="state.form.url" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求类型：">
              <el-input v-model="state.form.method" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户端id：">
              <el-input v-model="state.form.clientIds" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="cron表达式：">
              <el-input v-model="state.form.frequencyCron" clearable />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="formCommit">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 删除弹窗-->
  <el-dialog
    v-model="deleteVisible"
    :title="delTitle"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatTime } from "@/utils/dateStr";
import {
  getStatusTag,
  formatNodeIpType,
  formatTaskType,
  TaskTypeMap,
  ipType,
  netType,
  formatCreateFormData
} from "../components/index";
// import { organList, getOrgan, insertOrgan, updateOrgan, delOrgan } from "@/api/system/organ";
import {
  createBatch,
  deleteTask,
  detailTask,
  listTasks,
  resumeTasks,
  pauseTasks,
  updateProperties,
  showAllTasks
} from "@/api/boce/index";
//loading动画
const tableLoading = ref(true);
//删除弹窗是否显示
const deleteVisible = ref(false);
//新增编辑弹窗是否显示
const dialogFormVisible = ref(false);
//新增编辑弹窗标题
const title = ref("");
//列表参数参数
const pageParams = reactive({
  name: "",
  page: 1,
  rows: 10,
  taskStatus: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  getBoceMes();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  getBoceMes();
};
//搜索
function search() {
  pageParams.page = 1;
  getBoceMes();
}
const list = reactive({
  records: [],
  total: 0
});

const state = reactive({
  delId: null as any,
  name: "",
  description: "",
  frequencyCron: "*/1 * * * *",
  executionTimeouts: "",
  url: "",
  method: "",
  clientIds: "",
  taskType: "HTTP"
  // targetAddress: "",
  // taskType: 1,
  // interval: 5,
  // nodesStr: "",
  // tagKey: "",
  // tagValue: "",
  // ipType: 0,
  // netIcmpOn: 0,
  // netIcmpActivex: 0,
  // netIcmpDataCut: 0,
  // netDnsServer: 0,
  // nodes: []
} as any);

//删除
function showDel(id: any) {
  state.delId = id;
  deleteVisible.value = true;
}
//删除数据
function delCommit() {
  deleteVisible.value = false;
  deleteTask({ taskId: state.delId })
    .then(res => {
      if (res.code === 0) {
        ElMessage.success("删除成功");
        tableLoading.value = true;
        setTimeout(() => {
          getBoceMes();
        }, 2000);
      }
    })
    .catch(() => {
      ElMessage.error("删除失败");
    });
}
// 清空数据
const initForm = () => ({
  name: "",
  description: "",
  frequencyCron: "*/1 * * * *",
  executionTimeouts: "",
  url: "",
  taskType: "HTTP",
  method: "",
  clientIds: ""
});
// 新增数据
const addApply = () => {
  // getBoceNodes();
  title.value = "新增拨测任务";
  state.form = initForm();
  dialogFormVisible.value = true;
};
//编辑数据
const edit = (row: any) => {
  dialogFormVisible.value = true;
  title.value = "编辑单位";
  getOrgan(row.id).then(res => {
    state.form = res.entity;
  });
};
//新增编辑数据
// 改成异步请求
async function formCommit() {
  // const formData = { ...state.form };
  const formData = {
    ...state.form,
    executionTimeouts: Number(state.form.executionTimeouts)
  };
  // const formData = {
  //   name: "test062003",
  //   description: "test https://httpbin.org/get",
  //   taskType: "HTTP",
  //   frequencyCron: "*/1 * * * *",
  //   executionTimeouts: 30,
  //   url: "https://httpbin.org/get",
  //   method: "GET",
  //   clientIds: "my-dialer-client-01,my-dialer-client-02"
  // };
  try {
    const response = await createBatch(formData);
    if (response.code === 0) {
      ElMessage.success("新增成功");
      dialogFormVisible.value = false;
      getBoceMes();
    } else {
      ElMessage.error(response.desc || "新增失败");
    }
  } catch (error) {
    ElMessage.error("新增失败");
  }
}

const delTitle = ref("");
// 控制列表
const control = (row: any, val: string | null = null) => {
  // const status = row.status;
  if (row.is_enabled && val === "pause") {
    // 暂停
    pauseTasks({ taskId: row.id })
      .then(res => {
        if (res.code === 0) {
          ElMessage.success("暂停成功");
          tableLoading.value = true;
          setTimeout(() => {
            getBoceMes();
          }, 2000);
        }
      })
      .catch(() => {
        ElMessage.error("暂停失败");
      });
  } else if (!row.is_enabled && val === "restore") {
    // 恢复
    resumeTasks({ taskId: row.id })
      .then(res => {
        if (res.code === 0) {
          ElMessage.success("恢复成功");
          tableLoading.value = true;
          setTimeout(() => {
            getBoceMes();
          }, 2000);
        }
      })
      .catch(() => {
        ElMessage.error("恢复失败");
      });
  } else if (val === "delete") {
    delTitle.value = `删除【${row.name}】任务`;
    showDel(row.id);
  }
};

// 获取状态码
const statusMap = [
  { label: "运行中", value: true },
  // { label: "运行异常", value: 3 },
  { label: "任务暂停", value: false }
];

// 查询拨测列表
const getBoceMes = async () => {
  tableLoading.value = true;
  const params = {
    // ...pageParams,
    // taskType: state.form.taskType,
    pageNum: pageParams.page,
    pageSize: pageParams.rows,
    taskIds: pageParams.name,
    isEnable: pageParams.taskStatus
  };
  try {
    const res = await showAllTasks(params);
    console.log(res);
    if (res.code === 0 && res.entity.data.length > 0) {
      list.records = res.entity.data;
      list.total = Number(res.entity.total);
    }
    tableLoading.value = false;
  } catch (e) {
    tableLoading.value = false;
    ElMessage.error("获取拨测数据失败");
  }
};

// 查询拨测节点列表
const nodesList = ref([]);
const getBoceNodes = async () => {
  try {
    const res = await nodesTask({});
    if (res.code === 0 && res.entity.list.length > 0) {
      nodesList.value = res.entity.list.map((item: any) => {
        return {
          label: item.content,
          value: item.id
        };
      });
    }
  } catch (e) {}
};
let intervalId: number;
onMounted(() => {
  getBoceMes();
  intervalId = setInterval(() => {
    getBoceMes();
  }, 60000);
});
onUnmounted(() => {
  clearInterval(intervalId);
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
