<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="拨测设置" name="dialing">
      <dialing v-if="activeName === 'dialing'"></dialing>
    </el-tab-pane>
    <el-tab-pane label="拨测客户端设置" name="boceClient">
      <boceClient v-if="activeName === 'boceClient'"></boceClient>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import dialing from "./dialing/index.vue";
import boceClient from "../boceClient/index.vue";
import type { TabsPaneContext } from "element-plus";

const route = useRoute();
const router = useRouter();

const activeName = ref(route.query.tab ? String(route.query.tab) : "dialing");

watch(
  () => route.query.tab,
  newTab => {
    if (newTab) {
      activeName.value = String(newTab);
    }
  }
);

watch(activeName, newActiveName => {
  router.replace({ query: { tab: newActiveName } });
});

const handleClick = (tab: TabsPaneContext) => {
  console.log(tab);
};
</script>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
