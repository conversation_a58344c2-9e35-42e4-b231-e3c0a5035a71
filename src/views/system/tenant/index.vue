<template>
  <TitlecCom :title="`租户管理`" />
  <div class="top-container">
    <div class="search-container">
      <el-select
        style="width: 180px; margin-right: 15px"
        placeholder="请选择租户类型"
        v-model="userId"
        clearable
      >
        <el-option
          v-for="label in tenantTypes"
          :key="label.value"
          :label="label.label"
          :value="label.value"
        />
      </el-select>
      <el-input
        style="width: 180px; margin-right: 15px"
        placeholder="请输入编码"
        v-model="pageParams.code"
        clearable
        aria-label="Search input"
      ></el-input>
      <el-input
        style="width: 180px; margin-right: 15px"
        placeholder="请输入名称"
        v-model="pageParams.name"
        clearable
        aria-label="Search input"
      ></el-input>
      <el-button type="primary" @click="performSearch" :icon="Search" aria-label="Search button">
        搜索
      </el-button>
    </div>
    <el-button type="primary" class="topBtn" @click="addDialog"> 新增 </el-button>
  </div>
  <MyTable
    :data="tenantData"
    :total="totalSize"
    style="width: 100%"
    v-loading="loading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <MyColumn prop="code" label="编码" width="100" />
    <MyColumn prop="name" label="名称" />
    <MyColumn prop="legalPersonName" label="法人" />
    <MyColumn prop="type" label="类型" width="100">
      <template #default="scope">
        {{ scope.row.type === 0 ? "企业" : "其他" }}
      </template>
    </MyColumn>
    <MyColumn prop="status" label="状态" width="100">
      <template #default="scope">
        <el-tag :type="scope.row.status ? 'success' : 'info'" disable-transitions>
          {{ scope.row.status ? "启用" : "未启用" }}
        </el-tag>
      </template>
    </MyColumn>
    <MyColumn prop="alias" label="别名" />
    <MyColumn prop="email" label="邮箱" />
    <MyColumn prop="contactPerson" label="联系人" />
    <MyColumn prop="contactPhone" label="联系方式" />
    <MyColumn prop="industry" label="行业" />
    <MyColumn prop="description" label="描述" />
    <MyColumn prop="createdTime" label="创建时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createdTime) }}</span>
      </template>
    </MyColumn>
    <MyColumn label="操作" width="300" fixed="right" align="center" header-align="center">
      <template #default="scope">
        <span class="action-link" @click="editDialog(scope.row)">编辑</span>
        <span> / </span>
        <span class="action-link" @click="handleDelete(scope.row)">删除</span>
        <span> / </span>
        <span class="action-link" @click="handleInitial(scope.row)">初始化</span>
      </template>
    </MyColumn>
  </MyTable>

  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle ? '新增租户' : '编辑租户'"
    width="800px"
    @closed="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      label-position="right"
      :rules="{
        code: [{ required: true, message: '租户编码不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '租户名称不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '租户类型不能为空', trigger: 'change' }],
        contactPerson: [{ required: true, message: '联系人不能为空', trigger: 'blur' }],
        contactPhone: [{ required: true, message: '联系方式不能为空', trigger: 'blur' }]
      }"
    >
      <el-row :gutter="20">
        <!-- 第一列 -->
        <el-col :span="12">
          <el-form-item label="租户编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入统一社会信用代码"
              clearable
              :disabled="!dialogTitle"
            />
          </el-form-item>

          <el-form-item label="租户名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入企业全称" clearable />
          </el-form-item>

          <el-form-item label="企业简称" prop="alias">
            <el-input v-model="formData.alias" placeholder="请输入企业简称" clearable />
          </el-form-item>

          <el-form-item label="租户类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择租户类型" clearable>
              <el-option
                v-for="item in tenantTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属行业" prop="industry">
            <el-input v-model="formData.industry" placeholder="请输入所属行业" clearable />
          </el-form-item>
        </el-col>

        <!-- 第二列 -->
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="formData.contactPerson" placeholder="请输入联系人姓名" clearable />
          </el-form-item>

          <el-form-item label="联系电话" prop="contactPhone">
            <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" clearable />
          </el-form-item>

          <el-form-item label="电子邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入企业邮箱" clearable />
          </el-form-item>

          <el-form-item label="法人代表" prop="legalPersonName">
            <el-input v-model="formData.legalPersonName" placeholder="请输入法人姓名" clearable />
          </el-form-item>

          <el-form-item label="企业状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="企业logo" prop="logo">
        <el-input v-model="formData.logo" placeholder="请输入logo地址" clearable />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入详细地址" clearable />
      </el-form-item>

      <el-form-item label="企业描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入企业简介"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit(dialogTitle)" :loading="submitting">
        提交
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { ref, reactive, onMounted } from "vue";
import {
  getTenantList,
  createTenant,
  deleteTenant,
  getAvailableTenantIds,
  updateTenant,
  initialTenant
} from "@/api/system/tenant";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatTime } from "@/utils/dateStr";
// 定义用户数据类型
interface FormData {
  code: string;
  name: string;
  type: number;
  status: boolean;
  alias: string;
  logo: string;
  email: string;
  contactPerson: string;
  contactPhone: string;
  industry: string;
  provinceId: number;
  cityId: number;
  districtId: number;
  address: string;
  creditCode: string;
  legalPersonName: string;
  description: string;
}

const formData = reactive<FormData>({
  code: "",
  name: "",
  type: 1,
  status: true,
  alias: "",
  logo: "",
  email: "",
  contactPerson: "",
  contactPhone: "",
  industry: "",
  provinceId: 0,
  cityId: 0,
  districtId: 0,
  address: "",
  creditCode: "",
  legalPersonName: "",
  description: ""
});
const tenantTypes = [
  { label: "企业", value: 0 },
  { label: "其他", value: 1 }
];
const dialogVisible = ref(false);
const totalSize = ref(0);
const userId = ref(null);
const searchKeywords = ref("");
const dialogTitle = ref(true); // true: 新增, false: 编辑
const formRef = ref<FormInstance>();
const pageParams = reactive({
  page: 1,
  rows: 10,
  name: "",
  code: ""
  // type: 0,
  // status: 0,
  // industry: 0,
});

// 搜索用户
const performSearch = () => {
  pageParams.page = 1;
  fetchTenantList();
};

// 分页大小改变时处理
const handleSizeChange = (size: number) => {
  pageParams.rows = size;
  fetchTenantList();
};

// 当前页码改变时处理
const handleCurrentChange = (page: number) => {
  pageParams.page = page;
  fetchTenantList();
};

const tenantData = ref([]);
const loading = ref(false); // 表格加载状态
const submitting = ref(false); // 提交状态

// 弹窗关闭
const handleClose = () => {
  formRef.value?.resetFields();
  // selectedRegion.value = [];
  Object.assign(formData, {
    code: "",
    name: "",
    type: 1,
    status: true,
    alias: "",
    logo: "",
    email: "",
    contactPerson: "",
    contactPhone: "",
    industry: "",
    provinceId: 0,
    cityId: 0,
    districtId: 0,
    address: "",
    creditCode: "",
    legalPersonName: "",
    description: ""
  });
};

// 新增租户
const handleSubmit = async (val: boolean) => {
  if (val) {
    // 新增租户
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const res = await createTenant(formData);
      if (res.code === 0) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        fetchTenantList();
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  } else {
    // 编辑租户
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const res = await updateTenant(formData);
      if (res.code === 0) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        fetchTenantList();
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  }
};

// 获取租户列表
const fetchTenantList = async () => {
  loading.value = true;

  const params = {
    page: pageParams.page,
    rows: pageParams.rows,
    name: pageParams.name || "",
    code: pageParams.code || "",
    // sort: "id",
    // order: 0
    // code: "",
    type: userId.value
    // status: 0,
    // industry: 0,
    // provinceId: 0,
    // cityId: 0,
    // districtId: 0
  };

  try {
    const res = await getTenantList(params);
    if (res.code === 0) {
      tenantData.value = res.records || [];
      totalSize.value = Number(res.total);
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    loading.value = false;
  }
};
const addDialog = () => {
  dialogTitle.value = true;
  dialogVisible.value = true;
};
const editDialog = (row: any) => {
  dialogTitle.value = false;
  dialogVisible.value = true;
  Object.assign(formData, row);
};

// 删除租户
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除租户「${row.name}」吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    const res = await deleteTenant(row);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      fetchTenantList();
    } else {
    }
  } catch {
    ElMessage.info("已取消删除");
  }
};

// 初始化租户

const handleInitial = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要初始化租户「${row.name}」吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    const res = await initialTenant(row);
    if (res.code === 0) {
      ElMessage.success("初始化成功");
      fetchTenantList();
    } else {
    }
  } catch {}
};
onMounted(() => {
  fetchTenantList();
});
</script>

<style scoped>
.top-container {
  display: flex;
  justify-content: space-between;
}
.search-container {
  display: flex;
  margin-bottom: 15px;
}
.topBtn {
  margin-left: 10px;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}
</style>
