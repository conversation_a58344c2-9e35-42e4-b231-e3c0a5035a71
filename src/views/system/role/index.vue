<template>
  <TitlecCom :title="`角色管理`" />
  <div class="top-container">
    <div class="search-container">
      <el-input
        style="width: 240px; margin-right: 15px"
        placeholder="请输入名称"
        v-model="pageParams.name"
        clearable
        aria-label="Search input"
      ></el-input>
      <el-button type="primary" @click="performSearch" :icon="Search" aria-label="Search button">
        搜索
      </el-button>
    </div>
    <el-button type="primary" class="topBtn" @click="addDialog"> 新增 </el-button>
  </div>
  <MyTable
    :data="roleData"
    :total="totalSize"
    style="width: 100%"
    v-loading="loading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <MyColumn prop="id" label="角色ID" width="150" />
    <MyColumn prop="name" label="角色名称" />
    <MyColumn prop="code" label="角色编码" />
    <MyColumn prop="readonly" label="内置角色" width="100">
      <template #default="scope">
        {{ scope.row.readonly ? "是" : "否" }}
      </template>
    </MyColumn>
    <MyColumn prop="status" label="角色状态" width="100">
      <template #default="scope">
        <el-tag :type="scope.row.status ? 'success' : 'info'" disable-transitions>
          {{ scope.row.status ? "启用" : "未启用" }}
        </el-tag>
      </template>
    </MyColumn>
    <MyColumn prop="description" label="描述" />
    <MyColumn prop="createdTime" label="创建时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createdTime) }}</span>
      </template>
    </MyColumn>
    <MyColumn label="操作" width="300" fixed="right" align="center" header-align="center">
      <template #default="scope">
        <span class="action-link" @click="editDialog(scope.row)">编辑</span>
        <span> / </span>
        <span class="action-link" @click="handleDelete(scope.row)">删除</span>
        <span> / </span>
        <span class="action-link" @click="handleAssignUser(scope.row)">分配用户</span>
        <span> / </span>
        <span class="action-link" @click="handleAssignPermission(scope.row)">分配资源</span>
      </template>
    </MyColumn>
  </MyTable>

  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle ? '新增角色' : '编辑角色'"
    width="800px"
    @closed="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" label-width="120px" label-position="right">
      <el-row :gutter="20">
        <!-- 第一列 -->
        <el-col :span="12">
          <el-form-item label="角色编码" prop="code">
            <el-input v-model="formData.code" placeholder="请输入角色编码" clearable />
          </el-form-item>

          <el-form-item label="数据权限类型" prop="type">
            <el-select v-model="formData.scopeType" placeholder="请选择数据权限类型" clearable>
              <el-option
                v-for="item in scopeTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 第二列 -->
        <el-col :span="12">
          <el-form-item label="角色名称" prop="code">
            <el-input v-model="formData.name" placeholder="请输入角色名称" clearable />
          </el-form-item>
          <el-form-item label="角色状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit(dialogTitle)" :loading="submitting">
        提交
      </el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="assignDialogVisible"
    :title="assignDialogTitle ? '分配用户' : '分配资源'"
    :width="assignDialogTitle ? '700px' : '60%'"
    @closed="handleAssignClose"
    :close-on-click-modal="false"
  >
    <el-transfer
      v-if="assignDialogTitle"
      v-model="selectedItems"
      :data="transferData"
      :titles="['未分配', '已分配']"
      :props="{ key: 'id', label: 'name' }"
      filterable
      filter-placeholder="请输入关键词"
      style="margin-left: 55px"
    ></el-transfer>
    <div v-else style="display: flex; justify-content: space-between">
      <div style="margin-left: 10px" class="tree-container">
        <el-input
          v-model="filterText"
          class="w-60 mb-2"
          style="width: 200px"
          placeholder="请输入关键词"
          clearable
        />
        <div class="tree-wrapper">
          <el-tree
            ref="treeRef"
            class="filter-tree"
            :data="menuTypeData"
            node-key="value"
            :highlight-current="true"
            :props="defaultProps"
            show-checkbox
            accordion
            :default-checked-keys="pageParams1.checkedIds"
            @node-click="handleNodeClick"
            @check-change="handleCheckChange"
            :filter-node-method="filterNode"
            check-strictly
          />
        </div>
      </div>
      <div style="width: 600px">
        <MyTable
          :data="list.records"
          :total="list.total"
          ref="tableRef"
          style="width: 95%"
          row-key="id"
          v-loading="tableLoading"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
          :showSelect="true"
          @select="handleSelect"
          @select-all="handleSelectAll"
        >
          <MyColumn prop="title" label="名称" width="100" />
          <MyColumn prop="permission" label="资源编码" />
          <MyColumn prop="description" label="描述" />
        </MyTable>
      </div>
    </div>
    <template #footer>
      <el-button @click="assignDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleAssignSubmit" :loading="assignSubmitting">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { ref, reactive, onMounted } from "vue";
import {
  getRoleList,
  updateRole,
  createRole,
  deleteRole,
  getRoleUser,
  getRoleResource,
  assignRole,
  initialRole,
  getRoleResourceList
} from "@/api/system/role";
import { getResourceList, getResourceMenu } from "@/api/system/resources";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatTime } from "@/utils/dateStr";
import type { TreeInstance } from "element-plus";
import {
  scopeTypeMap,
  scopeTypeOptions,
  buildParentMap,
  getAllParentIds
} from "./components/index";
// 定义用户数据类型
interface FormData {
  code: string;
  name: string;
  type: number;
  status: boolean;
  alias: string;
  logo: string;
  email: string;
  contactPerson: string;
  contactPhone: string;
  industry: string;
  provinceId: number;
  cityId: number;
  districtId: number;
  address: string;
  creditCode: string;
  legalPersonName: string;
  description: string;
  readonly: boolean;
  scopeType: string;
}
const formData = reactive<FormData>({
  code: "",
  name: "",
  type: 1,
  status: true,
  alias: "",
  logo: "",
  email: "",
  contactPerson: "",
  contactPhone: "",
  industry: "",
  provinceId: 0,
  cityId: 0,
  districtId: 0,
  address: "",
  creditCode: "",
  legalPersonName: "",
  description: "",
  readonly: true,
  scopeType: ""
});

const dialogVisible = ref(false);
const assignDialogVisible = ref(false);
const totalSize = ref(0);
const userId = ref(null);
const dialogTitle = ref(true); // true: 新增, false: 编辑
const formRef = ref<FormInstance>();
const pageParams = reactive({
  page: 1,
  rows: 10,
  name: "",
  code: ""
});

// 搜索用户
const performSearch = () => {
  pageParams.page = 1;
  fetchRoleList();
};

// 分页大小改变时处理
const handleSizeChange = (size: number) => {
  pageParams.rows = size;
  fetchRoleList();
};

// 当前页码改变时处理
const handleCurrentChange = (page: number) => {
  pageParams.page = page;
  fetchRoleList();
};

// 处理单行选择
const handleSelect = <T extends { id: any }>(_selection: T[], row: T) => {
  const rowId = row.id;

  pageParams1.listIds = pageParams1.listIds.includes(rowId)
    ? pageParams1.listIds.filter(id => id !== rowId)
    : [...pageParams1.listIds, rowId];
};

// 处理全选
const handleSelectAll = () => {
  const allIds = list.records.map(item => item.id);
  const hasSelectedAll = allIds.every(id => pageParams1.listIds.includes(id));

  if (hasSelectedAll) {
    pageParams1.listIds = pageParams1.listIds.filter(id => !allIds.includes(id));
  } else {
    const newSet = new Set([...pageParams1.listIds, ...allIds]);
    pageParams1.listIds = Array.from(newSet);
  }
};

// 树形选中
const handleCheckChange = () => {
  const checkedKeys = treeRef.value?.getCheckedNodes() || [];
  let appIdsArr = [];
  appIdsArr = checkedKeys.map((item: any) => item.value);
  pageParams1.checkedIds = appIdsArr;
};
const roleData = ref([]);
const loading = ref(false); // 表格加载状态
const submitting = ref(false); // 提交状态

// 弹窗关闭
const handleClose = () => {
  formRef.value?.resetFields();
  // selectedRegion.value = [];
  Object.assign(formData, {
    code: "",
    name: "",
    type: 1,
    status: true,
    alias: "",
    logo: "",
    email: "",
    contactPerson: "",
    contactPhone: "",
    industry: "",
    provinceId: 0,
    cityId: 0,
    districtId: 0,
    address: "",
    creditCode: "",
    legalPersonName: "",
    description: "",
    readonly: true,
    scopeType: ""
  });
};

// 新增角色
const handleSubmit = async (val: boolean) => {
  if (val) {
    // 新增角色
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const res = await createRole(formData);
      if (res.code === 0) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        fetchRoleList();
      } else {
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  } else {
    // 编辑角色
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const res = await updateRole(formData);
      if (res.code === 0) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        fetchRoleList();
      } else {
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  }
};

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true;

  const params = {
    page: pageParams.page,
    rows: pageParams.rows,
    name: pageParams.name || ""
  };

  try {
    const res = await getRoleList(params);
    if (res.code === 0) {
      roleData.value = res.records || [];
      totalSize.value = Number(res.total);
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    loading.value = false;
  }
};
const addDialog = () => {
  dialogTitle.value = true;
  dialogVisible.value = true;
};
const editDialog = (row: any) => {
  dialogTitle.value = false;
  dialogVisible.value = true;
  Object.assign(formData, row);
};

// 删除角色
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色「${row.name}」吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    const res = await deleteRole(row);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      fetchRoleList();
    }
  } catch {
    ElMessage.info("已取消删除");
  }
};
const assignDialogTitle = ref(false);

// 查看可分配用户
const handleAssignUser = async (row: any) => {
  console.log(row);
  assignDialogTitle.value = true;
  userId.value = row.id;
  assignDialogVisible.value = true;
  const res = await getRoleUser(row);
  if (res.code === 0) {
    transferData.value = res.entity.userRoleDetails || [];
    selectedItems.value = res.entity.originTargetKeys;
  }
};

// 查看已分配资源
const handleViewResource = async () => {
  try {
    const res = await getRoleResource(userId.value);
    if (res.code === 0) {
      pageParams1.listIds = res.entity.buttonIdList || [];

      pageParams1.checkedIds = res.entity.menuIdList || [];
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  }
};

// 分配资源
const handleAssignPermission = async (row: any) => {
  assignDialogTitle.value = false;
  userId.value = row.id;
  handleViewResource();

  await fetchmenuList();
  assignDialogVisible.value = true;
};

const handleAssignClose = () => {
  assignDialogVisible.value = false;
  selectedItems.value = [];
  pageParams1.checkedIds = [];
  pageParams1.listIds = [];
  tableRef.value?.clearSelection();
};
const tableRef = ref();
const tableselect = ref([]);

const selectedItems = ref([]);
const transferData = ref([]);
const assignSubmitting = ref(false);
const handleAssignSubmit = async () => {
  if (assignDialogTitle.value) {
    // 分配用户

    const params = {
      userIdList: selectedItems.value,
      roleId: userId.value
    };
    try {
      assignSubmitting.value = true;
      const res = await initialRole(params);
      if (res.code === 0) {
        ElMessage.success("分配用户成功");
        assignDialogVisible.value = false;
        fetchRoleList();
      }
    } catch (error) {
      console.error("API 请求异常:", error);
    } finally {
      assignSubmitting.value = false;
    }
    return;
  } else {
    // 分配资源;
    const parentMap = buildParentMap(menuTypeData.value);
    const rawIds = [...(pageParams1.checkedIds || []), ...(pageParams1.listIds || [])];
    const allIds = getAllParentIds(rawIds, parentMap);
    const params = {
      resIdList: allIds,
      roleId: userId.value
    };
    try {
      assignSubmitting.value = true;
      const res = await assignRole(params);
      if (res.code === 0) {
        ElMessage.success("分配资源成功");
        handleViewResource();
        fetchRoleList();
      }
    } catch (error) {
      console.error("API 请求异常:", error);
    } finally {
      assignSubmitting.value = false;
    }
  }
};

const filterText = ref("");
const treeRef = ref<TreeInstance>();
const defaultProps = {
  children: "children",
  label: "label",
  value: "value"
};
watch(filterText, val => {
  treeRef.value!.filter(val);
});
interface Tree {
  [key: string]: any;
}
const filterNode = (value: string, menuTypeData: Tree) => {
  if (!value) return true;
  return menuTypeData.label.includes(value);
};

const menuTypeData = ref<{ value: string; label: string; children?: any[] }[]>([]);
// 树形图
const fetchmenuList = async () => {
  loading.value = true;

  const params = {
    page: pageParams.page,
    rows: pageParams.rows,
    status: true
  };

  try {
    const res = await getResourceMenu(params);
    if (res.code === 0) {
      menuTypeData.value = res.records?.map((item: any) => {
        return {
          value: item.id,
          label: item.title,
          children: item.children
            ? item.children.map((child: any) => ({
                value: child.id,
                label: child.title,
                children: child.children
                  ? child.children.map((subChild: any) => ({
                      value: subChild.id,
                      label: subChild.title,
                      children: subChild.children
                    }))
                  : []
              }))
            : []
        };
      });
      pageParams1.parentId = menuTypeData.value[0].value;
      fetchResourceList();
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    loading.value = false;
  }
};
const handleNodeClick = (data: any) => {
  pageParams1.parentId = data.value;
  fetchResourceList();
};

const pageParams1 = reactive<{
  page: number;
  rows: number;
  parentId: string;
  type: string;
  id: string;
  checkedIds: string[];
  listIds: string[];
}>({
  page: 1,
  rows: 10,
  parentId: "",
  type: "button",
  id: "",
  checkedIds: [],
  listIds: []
  // industry: 0,
});
const list = reactive({
  records: [],
  total: 0
});
const tableLoading = ref(false);
const fetchResourceList = async () => {
  tableLoading.value = true;
  const params = {
    page: pageParams1.page,
    rows: pageParams1.rows,
    parentId: pageParams1.parentId,
    type: "button",
    status: true
  };
  try {
    const res = await getResourceList(params);
    if (res.code === 0) {
      // roleData.value = res.records || [];
      list.records = res.records;
      list.total = Number(res.total);
      nextTick(() => {
        pageParams1.listIds.forEach(id => {
          const row = list.records.find(item => item.id === id);
          if (row) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    tableLoading.value = false;
  }
};

onMounted(() => {
  fetchRoleList();
});
</script>

<style scoped>
.top-container {
  display: flex;
  justify-content: space-between;
}
.search-container {
  display: flex;
  margin-bottom: 15px;
}
.topBtn {
  margin-left: 10px;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}

.dialog-footer {
  text-align: right;
}

.tree-container {
  height: 600px;
  width: 200px;
  display: flex;
  flex-direction: column;
}

.tree-wrapper {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 优化滚动条样式 */
.tree-wrapper::-webkit-scrollbar {
  width: 6px;
}
.tree-wrapper::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}
.tree-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.filter-tree {
  min-width: 100%;
  display: inline-block;
}
</style>
