export const scopeTypeOptions = [
  { label: "全部", value: 50 },
  { label: "本级以及子级", value: 40 },
  { label: "本级", value: 30 },
  { label: "自定义", value: 20 },
  { label: "个人", value: 10 },
  { label: "跟随系统上下文", value: 0 }
];

export const scopeTypeMap = scopeTypeOptions.reduce(
  (acc, item) => {
    acc[item.value] = item.label;
    return acc;
  },
  {} as Record<number, string>
);
// Step 1: 构建 parentMap
export function buildParentMap(tree: any[], parentId: string | null = null, map = new Map()) {
  tree.forEach(node => {
    map.set(node.value, parentId);
    if (node.children && node.children.length) {
      buildParentMap(node.children, node.value, map);
    }
  });
  return map;
}

// Step 2: 向上查找所有父级 ID
export function getAllParentIds(checkedIds: string[], parentMap: Map<any, any>) {
  const allIds = new Set(checkedIds);

  for (const id of checkedIds) {
    let currentId = id;
    while (parentMap.get(currentId)) {
      const parentId = parentMap.get(currentId);
      if (!allIds.has(parentId)) {
        allIds.add(parentId);
        currentId = parentId;
      } else {
        break;
      }
    }
  }

  return Array.from(allIds);
}
