<template>
  <div class="title">
    <div class="mark"></div>
    <div>AI提示词</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入提示词名称"
        clearable
        filterable
        v-model="pageParams.name"
      ></el-input>
      <el-select
        style="width: 240px; margin-left: 15px"
        placeholder="请输入提示词分类"
        clearable
        filterable
        v-model="pageParams.category"
      >
        <el-option v-for="item in categories" :key="item" :label="item" :value="item" />
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="name" label="名称" />
    <my-column property="category" label="分类" />
    <my-column property="content" label="内容" width="300" :show-overflow-tooltip="false">
      <template #default="scope">
        <div class="ip-cell">
          <el-tooltip placement="bottom" popper-class="custom-tooltip">
            <template #content>
              <div style="white-space: pre-wrap; word-break: break-word">
                {{ scope.row.content }}
              </div>
            </template>
            <span class="ip-text">{{ scope.row.content }}</span>
          </el-tooltip>
        </div>
      </template>
    </my-column>
    <my-column property="description" label="描述" />
    <my-column property="version" label="版本" />
    <my-column property="status" label="状态">
      <template #default="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? "启用" : "停用" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="createdTime" label="创建时间">
      <template #default="{ row }">
        {{ formatTime(row.createdTime) }}
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
      <template #default="{ row }">
        <span class="operate" @click="handleEdit(row)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="handleDelete(row)">删除</span>
        <span class="divider"> / </span>
        <span class="operate" @click="handleCopy(row)">复制</span>
        <span class="divider"> / </span>
        <span class="operate" @click="viewDetail(row)">详情</span>
      </template>
    </my-column>
  </MyTable>

  <!-- 新增弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑AI提示词模板' : '新增AI提示词模板'"
    width="800px"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" label-width="100px">
      <el-form-item label="提示词名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入提示词名称" clearable />
      </el-form-item>
      <el-form-item label="提示词分类" prop="category">
        <el-input v-model="formData.category" placeholder="请输入提示词分类" clearable />
      </el-form-item>
      <el-form-item label="提示词内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="6"
          placeholder="请输入提示词内容"
          clearable
        />
      </el-form-item>
      <el-form-item label="提示词描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入提示词描述"
          clearable
        />
      </el-form-item>
      <el-form-item label="提示词版本" prop="version">
        <el-input v-model="formData.version" placeholder="请输入提示词版本" clearable />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 删除确认弹窗 -->
  <el-dialog
    v-model="deleteDialogVisible"
    title="温馨提示"
    width="500px"
    :close-on-click-modal="false"
  >
    <span>确认要删除提示词「{{ currentDeleteItem?.name }}」吗？</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete" :loading="deleteLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 复制弹窗 -->
  <el-dialog
    v-model="copyDialogVisible"
    title="复制提示词"
    width="400px"
    :close-on-click-modal="false"
  >
    <el-form :model="copyForm" label-width="80px">
      <el-form-item label="新名称">
        <el-input v-model="copyForm.newName" placeholder="请输入新的提示词名称" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="copyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCopy" :loading="copyLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <div>
    <el-drawer v-model="detailVisiable" :title="detailTitle" size="50%">
      <div class="attr-panel">
        <div class="tab-content">
          <table class="detail-table">
            <tbody>
              <tr>
                <td class="label">名称</td>
                <td width="35%">{{ detailList.name }}</td>
                <td class="label">分类</td>
                <td width="35%">{{ detailList.category }}</td>
              </tr>
              <tr>
                <td class="label">标签</td>
                <td width="35%">{{ detailList.tags }}</td>
                <td class="label">版本</td>
                <td width="35%">{{ detailList.version }}</td>
              </tr>
              <tr>
                <td class="label">创建时间</td>
                <td width="35%">{{ formatTime(detailList.createdTime) }}</td>
                <td class="label">状态</td>
                <td width="35%">
                  <el-tag :type="detailList.status === 1 ? 'success' : 'danger'">
                    {{ detailList.status === 1 ? "启用" : "停用" }}
                  </el-tag>
                </td>
              </tr>
              <tr>
                <td class="label">描述</td>
                <td colspan="3">{{ detailList.description }}</td>
              </tr>
              <tr>
                <td class="label">内容</td>
                <td colspan="3">
                  <div style="white-space: pre-wrap; word-break: break-word">
                    {{ detailList.content }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { formatTime } from "@/utils/dateStr";
import {
  aiPromptList,
  aiPromptCategories,
  aiPromptAdd,
  aiPromptDetail,
  aiPromptEdit,
  aiPromptDelete,
  aiPromptCopy
} from "@/api/system/aiPrompt";
const tableLoading = ref(false); //列表loading动画
//列表传参
const pageParams = reactive({
  name: "",
  category: "",
  page: 1,
  rows: 10
});
//列表搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//分类变量
const categories = ref<string[]>([]);

// 新增相关变量
const dialogVisible = ref(false); //新增编辑弹窗是否可见
const submitLoading = ref(false); //新增编辑确定按钮loading动画
const formRef = ref();
//新增编辑传参
const formData = reactive({
  id: "",
  name: "",
  category: "",
  content: "",
  description: "",
  version: "",
  status: 1
});

// 编辑相关变量
const isEdit = ref(false);
const deleteDialogVisible = ref(false); //删除弹窗是否可见
const currentDeleteItem = ref<any>(null); //删除传参
const deleteLoading = ref(false); //删除确定按钮loading动画

// 复制相关变量
const copyDialogVisible = ref(false); //复制弹窗是否可见
const copyLoading = ref(false); //复制确定按钮loading动画
const currentCopyItem = ref<any>(null);
const copyForm = reactive({
  newName: ""
});
//每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//获取列表数据
function loadData() {
  tableLoading.value = true;
  aiPromptList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      tableLoading.value = false;
    });
}
//获取分类数据
const getCategories = async () => {
  try {
    const res = await aiPromptCategories();
    if (res.code === 0) {
      categories.value = res.records;
    }
  } catch (error) {
    console.log(error);
  }
};

// 新增相关方法
const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  // 重置表单数据
  Object.assign(formData, {
    id: "",
    name: "",
    category: "",
    content: "",
    description: "",
    version: "",
    status: 1
  });
};
//点击新增编辑确定按钮
const handleSubmit = async () => {
  try {
    submitLoading.value = true;

    if (isEdit.value) {
      // 编辑
      const response = await aiPromptEdit({
        id: formData.id,
        name: formData.name,
        category: formData.category,
        content: formData.content,
        description: formData.description,
        version: formData.version,
        status: formData.status
      });

      if (response.code === 0) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response?.message || "编辑失败");
      }
    } else {
      // 新增
      const response = await aiPromptAdd({
        name: formData.name,
        category: formData.category,
        content: formData.content,
        description: formData.description,
        version: formData.version,
        status: formData.status
      });

      if (response.code === 0) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response?.message || "新增失败");
      }
    }
  } catch (error) {
    console.error(isEdit.value ? "编辑失败:" : "新增失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

// 编辑方法
const handleEdit = async (row: any) => {
  try {
    isEdit.value = true;
    dialogVisible.value = true;

    // 获取详情数据
    const response = await aiPromptDetail(row.id);
    if (response.code === 0) {
      Object.assign(formData, response.entity);
    } else {
      // ElMessage.error("获取详情失败");
    }
  } catch (error) {
    console.error("获取详情失败:", error);
    // ElMessage.error("获取详情失败");
  }
};

// 删除方法
const handleDelete = (row: any) => {
  currentDeleteItem.value = row;
  deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  if (!currentDeleteItem.value) return;

  try {
    deleteLoading.value = true;
    await aiPromptDelete(currentDeleteItem.value.id);

    ElMessage.success("删除成功");
    deleteDialogVisible.value = false;
    currentDeleteItem.value = null;
    loadData();
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  } finally {
    deleteLoading.value = false;
  }
};
//点击复制按钮
const handleCopy = (row: any) => {
  currentCopyItem.value = row;
  copyForm.newName = "";
  copyDialogVisible.value = true;
};
//点击复制确定按钮
const confirmCopy = async () => {
  if (!currentCopyItem.value || !copyForm.newName.trim()) {
    ElMessage.warning("请输入新名称");
    return;
  }

  try {
    copyLoading.value = true;
    await aiPromptCopy(currentCopyItem.value.id, copyForm.newName);

    ElMessage.success("复制成功");
    copyDialogVisible.value = false;
    currentCopyItem.value = null;
    copyForm.newName = "";
    loadData();
  } catch (error) {
    console.error("复制失败:", error);
    ElMessage.error("复制失败");
  } finally {
    copyLoading.value = false;
  }
};
const detailVisiable = ref(false); //详情弹窗是否可见
const detailTitle = ref(""); //详情弹窗标题
const detailList = ref({
  category: "",
  content: "",
  createdTime: "",
  description: "",
  name: "",
  status: 1,
  tags: "",
  updatedTime: "",
  version: ""
});
//点击详情按钮
const viewDetail = async (row: any) => {
  detailVisiable.value = true;
  detailTitle.value = "提示词详情 【" + row.name + "】 ";
  detailList.value = row;
};

onMounted(() => {
  loadData();
  getCategories();
});
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.custom-tooltip {
  max-width: 300px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}
.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
