<template>
  <div class="card-grid">
    <el-card
      v-for="(item, index) in cards"
      :key="index"
      class="card"
      shadow="hover"
      style="position: relative"
    >
      <div class="card-content">
        <div class="probe-img">
          <img :src="item.img" alt="探针图片" v-if="item.img" />
        </div>
        <div
          style="
            width: calc(100% - 100px);
            display: flex;
            flex-direction: column;
            justify-content: center;
          "
        >
          <h3>{{ item.title }}</h3>
          <hr :style="`width: 80%; margin: 18px 0 8px 20px`" />
          <br />
          <p class="desc-text">{{ item.desc }}</p>
          <div style="position: absolute; bottom: 15px; right: 15px">
            <el-button type="primary" @click="openDocument(item)">查看文档</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElCard } from "element-plus";
import { cards } from "./index.ts";

const openDocument = item => {
  // window.open(`/${item.title}_Sdk接入说明文档.pdf`, "_blank");
  // window.open(`${import.meta.env.BASE_URL}${item.title}.pdf`, "_blank");
  window.open(`/pdf-viewer.html?file=/${item.title}.pdf`, "_blank");
};
</script>

<style scoped>
.card-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px;
  user-select: none;
  cursor: default;
}
.card {
  border-radius: 8px;
  min-height: 230px;
  max-width: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid #d4d4d4;
}
.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.probe-img {
  margin-bottom: 12px;
  min-width: 70px;
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: #666 solid 2px;
  border-radius: 8px;
  margin-left: 8px;
}
.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

.probe-img img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}
.card-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}
.card-content p {
  color: #666;
  font-size: 14px;
}
.desc-text {
  width: 90%;
  margin-left: 10px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
</style>
