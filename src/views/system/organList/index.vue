<template>
  <TitlecCom :title="`单位管理`" />
  <MyTable
    :data="list.records"
    style="width: 100%"
    row-key="id"
    default-expand-all
    height="790px"
    :showElPagination="false"
    :header-cell-style="{ background: '#f9f9f9', color: '#333', fontWeight: 'normal' }"
  >
    <MyColumn prop="name" label="单位名称" />
    <!-- <MyColumn prop="id" label="id" /> -->
    <MyColumn prop="alias" label="别名" />
    <MyColumn prop="contact" label="联系人" />
    <MyColumn prop="mobile" label="联系人号码" />
    <MyColumn prop="email" label="联系人邮箱" />
    <MyColumn prop="status" label="状态" width="100">
      <template #default="scope">
        <el-tag :type="scope.row.status ? 'success' : 'info'" disable-transitions>
          {{ scope.row.status ? "启用" : "未启用" }}
        </el-tag>
      </template>
    </MyColumn>
    <MyColumn prop="remark" label="备注" />
    <MyColumn label="操作" width="340" fixed="right" align="center" header-align="center">
      <template #default="scope">
        <span class="action-link" @click="addDialog(scope.row)">新增下级</span>
        <span> / </span>
        <span class="action-link" @click="showAppListDialog(scope.row)">应用列表</span>
        <span> / </span>
        <span class="action-link" @click="showUserListDialog(scope.row)">用户列表</span>
        <template v-if="scope.row.parentId != 0">
          <span> / </span>
          <span class="action-link" @click="editDialog(scope.row)">编辑</span>
          <span> / </span>
          <span class="action-link" @click="handleDelete(scope.row)">删除</span>
        </template>
      </template>
    </MyColumn>
  </MyTable>

  <!-- 新增编辑弹窗-->
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle ? '新增单位' : '编辑单位'"
    width="800px"
    @closed="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" label-width="120px" label-position="right">
      <el-row :gutter="20">
        <!-- 第一列 -->
        <el-col :span="12">
          <el-form-item label="上级单位名称">
            <el-input v-model="formData.parentName" clearable disabled />
          </el-form-item>

          <el-form-item label="单位简称">
            <el-input v-model="formData.alias" placeholder="请输入单位简称" clearable />
          </el-form-item>

          <el-form-item label="联系人电话">
            <el-input v-model="formData.mobile" placeholder="请输入联系人电话" clearable />
          </el-form-item>
          <el-form-item label="单位状态">
            <el-radio-group v-model="formData.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <!-- 第二列 -->
        <el-col :span="12">
          <el-form-item label="单位名称">
            <el-input v-model="formData.name" placeholder="请输入单位名称" clearable />
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="formData.contact" placeholder="请输入联系人姓名" clearable />
          </el-form-item>

          <el-form-item label="联系人邮箱">
            <el-input v-model="formData.email" placeholder="请输入联系人邮箱" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="企业描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入企业描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit(dialogTitle)" :loading="submitting">
        提交
      </el-button>
    </template>
  </el-dialog>

  <!-- 应用列表弹窗 -->
  <el-dialog
    v-model="appListDialogVisible"
    :title="`应用列表【${currentOrganName}】`"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div v-loading="appListLoading">
      <div style="margin-bottom: 16px; display: flex; align-items: center">
        <el-input
          v-model="appListPage.keyword"
          placeholder="请输入关键字"
          clearable
          style="width: 240px; margin-right: 12px"
        />
        <el-button type="primary" @click="onAppSearch" :icon="Search">搜索</el-button>
      </div>
      <MyTable
        :data="appList.records"
        :total="appList.total"
        style="width: 100%"
        :showElPagination="true"
        :rowKey="'id'"
        @currentChange="onAppPageChange"
        @sizeChange="onAppPageSizeChange"
      >
        <MyColumn prop="appid" label="AppID" />
        <MyColumn prop="name" label="应用名称" />
        <MyColumn prop="contact" label="联系人" />
        <MyColumn prop="mobile" label="联系人号码" />
        <MyColumn prop="organName" label="所属单位" />
      </MyTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="appListDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="appListDialogVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 用户列表弹窗 -->
  <el-dialog
    v-model="userListDialogVisible"
    :title="`用户列表【${currentUserOrganName}】`"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div v-loading="userListLoading">
      <div style="margin-bottom: 16px; display: flex; align-items: center">
        <el-input
          v-model="userListPage.keyword"
          placeholder="请输入关键字"
          clearable
          style="width: 240px; margin-right: 12px"
        />
        <el-button type="primary" @click="onUserSearch" :icon="Search">搜索</el-button>
      </div>
      <MyTable
        :data="userList.records"
        :total="userList.total"
        style="width: 100%"
        :showElPagination="true"
        :rowKey="'id'"
        @currentChange="onUserPageChange"
        @sizeChange="onUserPageSizeChange"
      >
        <MyColumn prop="name" label="用户姓名" />
        <MyColumn prop="mobile" label="手机号" />
        <MyColumn prop="email" label="邮箱" />
        <MyColumn prop="organName" label="所属单位" />
      </MyTable>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="userListDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="userListDialogVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import TitlecCom from "@/components/TitleCom/index.vue";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import {
  getOrganList,
  createOrganList,
  editOrganList,
  delOrganList,
  getOrganAppList,
  getOrganUserList
} from "@/api/system/organ";

//列表参数参数
const submitting = ref(false); // 提交状态
const pageParams = reactive({
  name: ""
});
const formData = reactive({
  id: "",
  name: "",
  contact: "",
  mobile: "",
  alias: "",
  email: "",
  remark: "",
  parentId: "",
  parentName: "",
  status: true,
  description: ""
});
interface RecordItem {
  id: string;
  name: string;
  parentId: string;
  [key: string]: any;
}

const list = reactive<{
  records: RecordItem[];
  total: number;
}>({
  records: [],
  total: 0
});

const dialogTitle = ref(false);
const dialogVisible = ref(false);
const formRef = ref<FormInstance>();

const appListDialogVisible = ref(false);
const appList = reactive<{ records: any[]; total: number }>({ records: [], total: 0 });
const appListPage = reactive({ page: 1, rows: 10, organId: "", keyword: "" });
let currentOrganId = "";
const currentOrganName = ref("");
const appListLoading = ref(false);

// 用户列表相关
const userListDialogVisible = ref(false);
const userList = reactive<{ records: any[]; total: number }>({ records: [], total: 0 });
const userListPage = reactive({ page: 1, rows: 10, organId: "", keyword: "" });
let currentUserOrganId = "";
const currentUserOrganName = ref("");
const userListLoading = ref(false);

// 弹窗关闭
const handleClose = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    id: "",
    name: "",
    contact: "",
    mobile: "",
    alias: "",
    email: "",
    remark: "",
    parentId: "",
    parentName: "",
    status: true
  });
};
const handleSubmit = async (typ: any) => {
  try {
    await formRef.value?.validate();
    if (typ) {
      // 新增
      const res = await createOrganList(formData);
      if (res.code === 0) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        getOrganListData();
      }
    } else {
      // 编辑
      const res = await editOrganList(formData);
      if (res.code === 0) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        getOrganListData();
      }
    }
  } catch (error) {
    console.log(error);
  }
};
const addDialog = (row: any) => {
  dialogTitle.value = true;
  dialogVisible.value = true;
  formData.parentId = row.id;
  formData.parentName = row.name;
};
const editDialog = (row: any) => {
  dialogTitle.value = false;
  dialogVisible.value = true;
  Object.assign(formData, row);
  formData.parentName = list.records.find(item => item.id === row.parentId)?.name || "";
};

const fetchAppList = async (organId: number, page = 1, rows = 10, keyword = "") => {
  appListLoading.value = true;
  const params: any = { page, rows };
  if (keyword) params.keyword = keyword;
  try {
    const res = await getOrganAppList(organId, params);
    if (res.code === 0) {
      appList.records = res.records || [];
      appList.total = Number(res.total) || 0;
    } else {
      appList.records = [];
      appList.total = 0;
      appListLoading.value = false;
    }
  } finally {
    appListLoading.value = false;
  }
};

const showAppListDialog = async (row: any) => {
  appListDialogVisible.value = true;
  currentOrganId = row.id;
  currentOrganName.value = row.name;
  appListPage.page = 1;
  appListPage.rows = 10;
  appListPage.keyword = "";
  await fetchAppList(
    Number(currentOrganId),
    appListPage.page,
    appListPage.rows,
    appListPage.keyword
  );
};

const onAppPageChange = (page: number) => {
  appListPage.page = page;
  fetchAppList(Number(currentOrganId), appListPage.page, appListPage.rows, appListPage.keyword);
};
const onAppPageSizeChange = (size: number) => {
  appListPage.rows = size;
  appListPage.page = 1;
  fetchAppList(Number(currentOrganId), appListPage.page, appListPage.rows, appListPage.keyword);
};
const onAppSearch = () => {
  appListPage.page = 1;
  const keyword = appListPage.keyword.trim();
  fetchAppList(Number(currentOrganId), appListPage.page, appListPage.rows, keyword);
};

// 用户列表相关方法
const fetchUserList = async (organId: number, page = 1, rows = 10, keyword = "") => {
  userListLoading.value = true;
  const params: any = { page, rows };
  if (keyword) params.keyword = keyword;
  try {
    const res = await getOrganUserList(organId, params);
    if (res.code === 0) {
      userList.records = res.records || [];
      userList.total = Number(res.total) || 0;
    } else {
      userList.records = [];
      userList.total = 0;
      userListLoading.value = false;
    }
  } finally {
    userListLoading.value = false;
  }
};

const showUserListDialog = async (row: any) => {
  userListDialogVisible.value = true;
  currentUserOrganId = row.id;
  currentUserOrganName.value = row.name;
  userListPage.page = 1;
  userListPage.rows = 10;
  userListPage.keyword = "";
  await fetchUserList(
    Number(currentUserOrganId),
    userListPage.page,
    userListPage.rows,
    userListPage.keyword
  );
};
const onUserPageChange = (page: number) => {
  userListPage.page = page;
  fetchUserList(
    Number(currentUserOrganId),
    userListPage.page,
    userListPage.rows,
    userListPage.keyword
  );
};
const onUserPageSizeChange = (size: number) => {
  userListPage.rows = size;
  userListPage.page = 1;
  fetchUserList(
    Number(currentUserOrganId),
    userListPage.page,
    userListPage.rows,
    userListPage.keyword
  );
};
const onUserSearch = () => {
  userListPage.page = 1;
  const keyword = userListPage.keyword.trim();
  fetchUserList(Number(currentUserOrganId), userListPage.page, userListPage.rows, keyword);
};

// 组织结构
const getOrganListData = async () => {
  const params = {
    name: pageParams.name || "",
    status: 1
  };
  try {
    const res = await getOrganList(params);
    if (res.code === 0) {
      list.records = res.records;
      // list.total = res.records.length;
    }
  } catch {}
};

// 删除单位
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除单位「${row.name}」吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    const res = await delOrganList(row);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      getOrganListData();
    }
  } catch {
    ElMessage.info("已取消删除");
  }
};

// 搜索用户
const performSearch = () => {
  getOrganListData();
};

onMounted(() => {
  getOrganListData();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}

.ellipsis {
  display: inline-block;
  max-width: 90px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
