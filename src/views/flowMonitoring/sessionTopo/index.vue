<template>
  <div v-if="topodata.length" class="relative">
    <!-- <el-button class="absolute top-8 left-8 z-12" type="primary" @click="handleClick"
      >拓扑前50</el-button
    >
    <el-button
      class="absolute top-8 left-32 z-12"
      type="primary"
      style="background-color;: #4b9fd5"
      @click="handleClick100"
      >拓扑前100</el-button
    > -->
    <button
      :class="[
        'absolute top-8 left-8 z-12',
        topSelect === 'top50' ? 'btn-select' : 'btn-no-select'
      ]"
      @click="handleClick"
    >
      top50
    </button>
    <button
      :class="[
        'absolute top-8 left-32 z-12',
        topSelect === 'top100' ? 'btn-select' : 'btn-no-select'
      ]"
      @click="handleClick100"
    >
      top100
    </button>
  </div>

  <div id="chart-container" ref="chartRef" style="width: 100%; height: 100%"></div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { getSessionTopo } from "@/api/flow/overview";
import { useRouter } from "vue-router";
import { serviceNameStore } from "@/store/modules/service";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useServiceNameStore = serviceNameStore();
const usebreadcrumbStore = breadcrumbStore();
const router = useRouter();
// 定义类型
interface Link {
  source: string;
  target: string;
  lineStyle?: {
    color?: string;
    width?: number;
    opacity?: number;
  };
  emphasis?: {
    lineStyle: {
      width?: number;
      opacity?: number;
    };
  };
  inFlow?: number;
  outFlow?: number;
}
const topSelect = ref("");
const handleClick = async (tab: any) => {
  topSelect.value = "top50";
  usebreadcrumbStore.setTopSelect("top50");
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);

    const data = await fetchData();
    if (data) {
      option.value.series[0].data = data.nodes;
      option.value.series[0].links = data.links;
      chart.setOption(option.value);
    }
    chart.on("click", (params: any) => {
      if (params.dataType === "node" || params.dataType === "edge") {
        // 获取被点击的元素的 ip（节点的 name）
        const ip = params.data.target || params.data.name || "";

        // 跳转到另一个页面，并带上查询参数 label
        router.push({ path: "/flowMonitoring/list/IpList", query: { label: ip } });
      }
    });
  }
};
const handleClick100 = async (tab: any) => {
  topSelect.value = "top100";
  usebreadcrumbStore.setTopSelect("top100");
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);

    const data = await fetch100Data();
    if (data) {
      option.value.series[0].data = data.nodes;
      option.value.series[0].links = data.links;
      chart.setOption(option.value);
    }
    chart.on("click", (params: any) => {
      if (params.dataType === "node" || params.dataType === "edge") {
        // 获取被点击的元素的 ip（节点的 name）
        const ip = params.data.target || params.data.name || "";
        // 跳转到另一个页面，并带上查询参数 label
        router.push({ path: "/flowMonitoring/list/IpList", query: { label: ip } });
      }
    });
  }
};

const chartRef = ref<HTMLDivElement | null>(null);
let chart: echarts.ECharts | null = null;
const topodata: any = ref([]);

const fetchData = async () => {
  const ip = useServiceNameStore.serviceName;
  try {
    const res = await getSessionTopo(ip);
    if (res.code === 0) {
      // 取了前50个
      const data = res.records.splice(0, 50) || [];
      topodata.value = data;
      return processData(data);
    }
  } catch (error) {
    console.log(error, "error:::");
  }
};
const fetch100Data = async () => {
  const ip = useServiceNameStore.serviceName;
  try {
    const res = await getSessionTopo(ip);
    if (res.code === 0) {
      // 取了前50个
      const data = res.records || [];
      topodata.value = data;
      return processData(data);
    }
  } catch (error) {
    console.log(error, "error:::");
  }
};

const processData = (data: any[]) => {
  const nodes = new Set<string>();
  const links: Link[] = [];
  const ip = useServiceNameStore.serviceName;
  nodes.add(ip);

  data.forEach(item => {
    nodes.add(item.related_ip);
    links.push({
      source: ip,
      target: item.related_ip,
      lineStyle: {
        color: "#4b9fd5",
        width: 2,
        opacity: 0.6
      },
      emphasis: {
        lineStyle: {
          width: 9,
          opacity: 1
        }
      },
      inFlow: item.in_bytes,
      outFlow: item.out_bytes
    });
  });

  const nodeList = Array.from(nodes).map(ip => {
    return {
      name: ip,
      // value: isLocalIP ? 85 : 1,
      // category: isLocalIP ? 0 : isPrivateIP ? 1 : 2,
      itemStyle: {
        borderColor: "#fff",
        borderWidth: 2

        // shadowBlur: 10,
        // shadowColor: isLocalIP
        //   ? "rgba(255, 107, 107, 0.5)"
        //   : isPrivateIP
        //     ? "rgba(75, 159, 213, 0.5)"
        //     : "rgba(255, 215, 0, 0.5)"
      }
    };
  });

  return {
    nodes: nodeList,
    links: links
  };
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const option = ref({
  autoFit: true,
  backgroundColor: "#001122",
  color: "black",
  // title: {
  //   text: "IP连接关系图",
  //   textStyle: {
  //     color: "#fff",
  //     fontSize: 20
  //   },
  //   left: 20,
  //   top: 20
  // },
  tooltip: {
    trigger: "item",
    formatter: (params: any) => {
      if (params.dataType === "edge") {
        return (
          `入站流量: ${formatBytes(params.data.inFlow)}<br/>` +
          `出站流量: ${formatBytes(params.data.outFlow)}<br/>` +
          `总流量: ${formatBytes(params.data.inFlow + params.data.outFlow)}`
        );
      }
      return params.name;
    },
    backgroundColor: "rgba(50,50,50,0.7)",
    borderColor: "#333",
    textStyle: {
      color: "#fff"
    }
  },
  animation: false,
  lineStyle: {
    color: "black",
    width: 1,
    opacity: 0.6,
    curveness: 0.3,
    type: "solid"
  },
  series: [
    {
      type: "graph",
      layout: "force",
      force: {
        repulsion: 600,
        gravity: 0.1,
        edgeLength: 100,
        layoutAnimation: false
      },

      animation: false,
      symbolSize: (value: number, params: any) => {
        const ip = useServiceNameStore.serviceName;
        return params.name === ip ? 20 : 10;
      },
      roam: true,
      label: {
        show: true,
        position: "right",
        fontSize: 12,
        color: "#fff",
        backgroundColor: "rgba(0,0,0,0.3)",
        padding: [4, 8],
        borderRadius: 3
      },
      edgeSymbol: ["none", "none"],
      edgeSymbolSize: [0, 8],
      lineStyle: {
        color: "black",
        width: 1,
        opacity: 0.6,
        curveness: 0.3,
        type: "solid"
      },
      itemStyle: {
        color: (params: any) => {
          const ip = useServiceNameStore.serviceName;
          return params.name === ip ? "#235894" : "#4b9fd5";
        },
        // borderColor: "#fff",
        borderWidth: 1
        // shadowColor: "rgba(0, 0, 0, 0.3)",
        // shadowBlur: 5
      },

      emphasis: {
        // focus: "adjacency",
        lineStyle: {
          width: 4,
          color: "#FF5733"
          // opacity: 1
        },
        // label: {
        //   // fontSize: 14
        //   // backgroundColor: "rgba(255,255,255,0.1)"
        // },
        // itemStyle: {
        //   // shadowBlur: 10,
        //   // shadowColor: "rgba(255, 255, 255, 0.5)"
        // },
        // blurScope: "coordinateSystem",
        scale: 1.3
      },
      blur: {
        itemStyle: {
          opacity: 0.7
        },
        lineStyle: {
          opacity: 0.3
        }
      }
    }
  ]
});

onMounted(async () => {
  topSelect.value = usebreadcrumbStore.topSelect || "top50";
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    window.addEventListener("resize", () => {
      chart?.resize();
    });
    let data: any = {};
    if (topSelect.value === "top50") {
      data = await fetchData();
    } else {
      data = await fetch100Data();
    }
    if (data) {
      option.value.series[0].data = data.nodes;
      option.value.series[0].links = data.links;
      chart.setOption(option.value);
    }
    chart.on("click", (params: any) => {
      if (params.dataType === "node" || params.dataType === "edge") {
        // 获取被点击的元素的 ip（节点的 name）
        const ip = params.data.target || params.data.name || "";
        // console.log(params.data, "params.data:::");

        // 跳转到另一个页面，并带上查询参数 label
        router.push({ path: "/flowMonitoring/list/IpList", query: { label: ip } });
      }
    });
  }
});
</script>

<style lang="scss" scoped>
button {
  padding: 0.8em 1.6em;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 2.5px;
  font-weight: 500;
  color: #000;
  background-color: #fff;
  border: none;
  border-radius: 4px;
  box-shadow: #4b9fd5;
  transition: all 0.3s ease 0s;
  cursor: pointer;
  outline: none;
}

button:hover {
  // background-color: #4b9fd5;
  // box-shadow: #4b9fd5;
  // color: #fff;
  // transform: translateY(-7px);
}

.btn-select {
  background-color: #4b9fd5;
  color: #fff;
}
.btn-no-select {
  background-color: #fff;
}

// background-color: #4b9fd5;
// box-shadow: #4b9fd5;

button:active {
  // transform: translateY(-1px);
}
</style>
