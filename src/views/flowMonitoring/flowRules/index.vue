<template>
  <div class="rule-management">
    <!-- 搜索栏 -->
    <div class="search-container">
      <div class="search-bar">
        <div class="search-input">
          <!-- <el-input class="mr-20px" v-model="searchParams.mac" placeholder="B0:4F:A6:4C:48:75">
            <template #prepend>MAC 地址</template>
          </el-input> -->
          <el-input v-model="searchParams.targetIp" placeholder="***********">
            <template #prepend>目标 IP</template>
          </el-input>
        </div>

        <div class="search-btn">
          <el-button type="primary" :icon="Search" @click="searchRules">搜索</el-button>
          <el-button @click="clearSearch">清除</el-button>
        </div>
      </div>
      <!-- <el-button type="primary" class="header-addbtn" @click="addRule">添加规则</el-button> -->
    </div>

    <!-- 卡片展示 -->
    <div class="card-container" v-loading="cardLoading">
      <el-row :gutter="20">
        <el-col v-for="(rule, index) in ruleList" :key="index" :xs="24" :sm="12" :lg="8" :xl="6">
          <div class="rule-card">
            <div class="rule-header">
              <div>
                <span
                  class="tag"
                  :class="{ vxlan: rule.type === 'VXLAN', gre: rule.type === 'GRE' }"
                >
                  {{ rule.type }}
                </span>
                <!-- <el-switch
                  class="ml-10px"
                  v-model="rule.status"
                  @change="stwichChange(rule.status, rule.id, index)"
                /> -->
              </div>
              <div>
                <el-tag v-if="rule.status" effect="dark" round class="mr-10px" size="small">
                  已开启
                </el-tag>
                <el-tag v-else effect="dark" type="info" round class="mr-10px" size="small">
                  已关闭
                </el-tag>
                <span class="priority">优先级: {{ rule.priority }}</span>
              </div>
            </div>
            <div class="rule-content">
              <p class="mb-10px">
                <strong>{{ rule.description }}</strong>
              </p>

              <!-- <p class="mb-10px"><span>IP/网段:</span> {{ rule.mac }}</p> -->
              <p class="mb-10px" v-if="rule.type === 'VXLAN'"><span>VNI:</span> {{ rule.vni }}</p>
              <p class="mb-10px" v-if="rule.type === 'VXLAN'">
                <span>远程 VTEP:</span> {{ rule.remoteVtep }}
              </p>
              <p class="mb-10px" v-if="rule.type === 'GRE'">
                <span>远程端点:</span> {{ rule.remoteEndpoint }}
              </p>
              <p class="mb-10px" v-if="rule.type === 'GRE'">
                <span>GRE 密钥:</span> {{ rule.key }}
              </p>
            </div>
            <div
              ref="chartRefs"
              :data-index="index"
              :style="{ width: '100%', height: '100px' }"
              class="mt-20px"
            ></div>
            <!-- <el-button type="danger" @click="deleteRule(rule.id)"> 删除规则 </el-button> -->
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
  <el-dialog
    title="添加规则"
    v-model="dialogVisible"
    width="500px"
    :before-close="handleClose"
    class="add-rule-dialog"
    :close-on-click-modal="false"
  >
    <div class="form-container">
      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-style="{ textAlign: 'left' }"
        label-width="100px"
      >
        <!-- MAC 地址 -->
        <!-- <el-form-item label="MAC 地址" prop="mac">
          <el-input v-model="form.mac" :disabled="true" />
        </el-form-item> -->

        <!-- 优先级 -->
        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="form.priority"
            placeholder="(-100 到 100)"
            :min="1"
            :max="5"
            :step="1"
          />
        </el-form-item>

        <!-- 隧道类型 -->
        <el-form-item label="协议类型" prop="tunnelType">
          <el-select v-model="form.tunnelType" placeholder="请选择">
            <el-option label="VXLAN" value="VXLAN" />
            <el-option label="GRE" value="GRE" />
          </el-select>
        </el-form-item>

        <!-- VNI -->
        <el-form-item v-if="form.tunnelType === 'VXLAN'" label="VNI" prop="vni">
          <el-input
            v-model="form.vni"
            placeholder="请输入 VNI（只能是数字）"
            clearable
            type="number"
          />
        </el-form-item>
        <!-- 远程 VTEP -->
        <el-form-item v-if="form.tunnelType === 'VXLAN'" label="远程 VTEP" prop="remoteVtep">
          <el-input v-model="form.remoteVtep" placeholder="192.168.1.2:9990" clearable />
        </el-form-item>

        <!-- 远程 VTEP -->
        <el-form-item v-if="form.tunnelType === 'GRE'" label="远程端点" prop="remoteEndpoint">
          <el-input v-model="form.remoteEndpoint" placeholder="192.168.1.2" clearable />
        </el-form-item>

        <!-- VNI -->
        <el-form-item v-if="form.tunnelType === 'GRE'" label="GRE KEY" prop="key">
          <el-input
            v-model="form.key"
            placeholder="请输入 KEY（只能是数字）"
            clearable
            type="number"
          />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input
            v-model="form.desc"
            placeholder="输入描述（最多40个字）"
            :maxlength="40"
            clearable
          />
        </el-form-item>
      </el-form>
    </div>

    <template v-slot:footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">添加规则</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="删除规则"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import { getFlowRulesInfo, creatRules, delRules, editStatus } from "@/api/flow/detail";
import * as echarts from "echarts";
import { Search, Filter } from "@element-plus/icons-vue";
import { debounce } from "lodash-es";
const serviceName = sessionStorage.getItem("serviceName");
const serviceObject = serviceName ? JSON.parse(serviceName) : {};
const service = reactive(serviceObject);
interface Rule {
  id: number;
  description: string;
  priority: number;
  status: boolean;
  type: "VXLAN" | "GRE";
  vni: number;
  remoteVtep: string;
  remoteEndpoint: string;
  key: number;
  sseData: number[];
}

interface StatsItem {
  id: number;
  name: string;
  rule_count: number;
  stats: [number, number][];
}

const deleteVisible = ref(false);
const cardLoading = ref(false);
const searchParams = reactive({
  mac: "",
  targetIp: ""
});
const ruleList = ref<Rule[]>([]);
const totalNum = ref();

const stwichChange = (stwichValue: boolean, id: number, index: number) => {
  const params = {
    status: stwichValue ? 1 : 0
  };
  try {
    editStatus(id, params).then(res => {
      getFlowRulesData();
    });
  } catch (error) {
    console.log(error);
  }
};

const addRule = () => {
  dialogVisible.value = !dialogVisible.value;
};

const clearSearch = debounce(async () => {
  searchParams.mac = "";
  searchParams.targetIp = "";
  clearCharts(); // 清理旧的图表
  await getFlowRulesData();
  initCharts();
}, 1000);

const ruleId = ref(0);
const deleteRule = (id: number) => {
  deleteVisible.value = !deleteVisible.value;
  ruleId.value = id;
};

const dialogVisible = ref(false);

// 表单数据
const form = reactive({
  mac: "",
  priority: 1,
  tunnelType: "VXLAN",
  vni: "",
  remoteVtep: "",
  remoteEndpoint: "",
  key: "",
  desc: ""
});

// 表单校验规则
const rules = reactive({
  // mac: [
  //   {
  //     required: true,
  //     message: "请输入 MAC 地址",
  //     trigger: "blur"
  //   },
  //   {
  //     pattern: /^([0-9A-Fa-f]{2}([ :])){5}[0-9A-Fa-f]{2}$/,
  //     message: "MAC地址格式错误，请使用空格或冒号分隔",
  //     trigger: "blur"
  //   }
  // ],
  priority: [
    {
      required: true,
      message: "请输入优先级",
      trigger: "blur"
    },
    {
      type: "number",
      min: 1,
      max: 5,
      message: "优先级范围为 1 到 5",
      trigger: "blur"
    }
  ],
  tunnelType: [
    {
      required: true,
      message: "请选择隧道类型",
      trigger: "change"
    }
  ],
  vni: [
    {
      required: true,
      message: "请输入正确的VNI",
      trigger: "blur"
    }
  ],
  key: [
    {
      required: true,
      message: "请输入正确的KEY",
      trigger: "blur"
    }
  ],
  remoteVtep: [
    {
      required: true,
      message: "请输入远程 VTEP IP 地址",
      trigger: "blur"
    }
  ],
  desc: [
    {
      required: true,
      message: "请输入描述",
      trigger: "blur"
    }
  ],
  remoteEndpoint: [
    {
      required: true,
      message: "请输入远程端点地址",
      trigger: "blur"
    },
    {
      message: "IP 地址格式不正确",
      trigger: "blur"
    }
  ]
});
import { FormInstance } from "element-plus";

// 表单引用
const formRef = ref<FormInstance>();

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 取消按钮点击事件
const handleCancel = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};
const handleMacFormat = () => {
  // 将空格或冒号统一替换为冒号
  form.mac = form.mac.replace(/[ :]/g, "");
};

function formatMacAddress(mac: string) {
  // 检查是否为有效的12位十六进制字符串
  if (!/^[0-9a-fA-F]{12}$/.test(mac)) {
    return "请输入正确的MAC地址";
  }
  // 插入冒号并转为大写
  return mac.toUpperCase().replace(/([0-9A-F]{2})(?=[0-9A-F])/g, "$1:");
}
// 提交按钮点击事件
const handleSubmit = () => {
  if (handlePriority()) {
    return;
  }

  formRef.value?.validate(valid => {
    if (valid) {
      const mac = form.mac || "";
      handleMacFormat();
      const params = handleParams();
      creatRules(params)
        .then(async res => {
          if (res.code === 0) {
            ElMessage.success("添加成功");

            ElMessage.error("添加失败");
          }
          formRef.value?.resetFields();
          dialogVisible.value = false;
          await getFlowRulesData();
          restartSSE();
          initCharts();
        })
        .catch(error => {
          form.mac = mac;
          ElMessage.success("添加失败");
          console.log(error);
        });
    } else {
      console.log("表单校验失败");
    }
  });
};
function handleParams() {
  if (form.key === "" || form.remoteEndpoint === "") {
    const params = [
      {
        mac: form.mac,
        target: {
          tunnel: {
            vxlan: {
              remote_vtep: form.remoteVtep,
              vni: Number(form.vni)
            }
          }
        },
        priority: Number(form.priority),
        description: form.desc
      }
    ];
    return params;
  }
  if (form.remoteVtep === "" || form.vni === "") {
    const params = [
      {
        mac: form.mac,
        target: {
          tunnel: {
            gre: {
              remote_endpoint: form.remoteEndpoint,
              key: Number(form.key)
            }
          }
        },
        priority: Number(form.priority),
        description: form.desc
      }
    ];
    return params;
  }
}
const delCommit = () => {
  // 将 deleteVisible 的值设置为 false
  // 这通常用于控制某个组件或视图的可见性
  try {
    delRules("default", ruleId.value).then(res => {
      getFlowRulesData();
      deleteVisible.value = false;
    });
  } catch (error) {
    console.log(error);
  }
};
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
interface PageParams {
  appid: string;
  mac?: string;
  target?: string;
}

const pageParams = reactive<PageParams>({
  appid: useApplicationStore.appId,
  mac: "",
  target: ""
});
const priority = ref<number[]>([]);
const chartInstances = ref<echarts.ECharts[]>([]);

function isValidMacAddress(mac: string): boolean {
  // 正则表达式检测 MAC 地址格式
  const macRegex = /^([0-9A-Fa-f]{2}([-:])){5}([0-9A-Fa-f]{2})$/;

  // 检查格式是否符合
  if (!macRegex.test(mac)) {
    return false;
  }

  // 检查分隔符是否一致
  const separator = mac.includes(":") ? ":" : "-";
  const parts = mac.split(separator);

  return parts.length === 6 && parts.every(part => part.length === 2);
}
function isValidIp(ip: string): boolean {
  // 定义IPv4地址的正则表达式
  const ipv4Regex =
    /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

  // 使用正则表达式测试IP地址
  return ipv4Regex.test(ip);
}
const getFlowRulesData = async () => {
  try {
    if (searchParams.mac !== "") {
      const result = isValidMacAddress(searchParams.mac);
      if (!result) {
        ElMessage.error("请输入正确的MAC地址");
        return;
      }
      pageParams.mac = searchParams.mac.replace(/[ :]/g, "");
    }
    if (searchParams.mac === "") {
      pageParams.mac = undefined;
    }
    if (searchParams.targetIp !== "") {
      const result = isValidIp(searchParams.targetIp);
      if (!result) {
        ElMessage.error("请输入正确的IP地址");
        return;
      }
      pageParams.target = searchParams.targetIp;
    }
    if (searchParams.targetIp === "") {
      pageParams.target = undefined;
    }
    cardLoading.value = true;

    await getFlowRulesInfo("default", pageParams).then(res => {
      ruleList.value = [];
      if (res.records && Array.isArray(res.records)) {
        res.records.forEach((item: any) => {
          ruleList.value.push({
            id: item.id,
            description: item.description,
            priority: item.priority,
            status: item.status === 1,
            type: "VXLAN",
            vni: 0,
            remoteVtep: "",
            remoteEndpoint: "",
            key: 0,
            sseData: []
          });
          priority.value.push(item.priority);
        });
        totalNum.value = res.records.length || 0;
      }
    });
    cardLoading.value = false;
  } catch (error) {
    cardLoading.value = false;
    console.log(error);
  }
};
function handlePriority() {
  if (priority.value.includes(form.priority)) {
    ElMessage.error("优先级已存在，请调整优先级");
    return true;
  }
}
const searchRules = debounce(async () => {
  const result = isValidMacAddress(searchParams.mac);
  if (searchParams.mac && !result) {
    ElMessage.error("请输入正确的MAC地址");
    return;
  }
  const resultIp = isValidIp(searchParams.targetIp);
  if (searchParams.targetIp && !resultIp) {
    ElMessage.error("请输入正确的IP地址");
    return;
  }
  clearCharts(); // 清理旧的图表
  await getFlowRulesData();
  initCharts();
}, 1000);
const clearCharts = () => {
  chartInstances.value.forEach((chart, index) => {
    if (chart) {
      chart.dispose();
      chartInstances.value[index] = null as any;
    }
  });
};

const appId = useApplicationStore.appId; // 替换为你的 app_id
let eventSource: EventSource | null = null;
// 动态绑定的 refs 和实例
const chartRefs = ref([]); // 绑定每个卡片的 chart 容器
const initCharts = async () => {
  await nextTick(); // 等待 DOM 渲染完成
  ruleList.value.forEach((rule, index) => {
    // 获取对应的 chart 容器
    const container = chartRefs.value[index];
    if (container) {
      // 初始化图表
      const chartInstance = echarts.init(container);
      chartInstances.value[index] = chartInstance;
      let tipValue = "";
      // 配置图表选项
      const option = {
        grid: {
          left: 0, // 去掉左边距
          right: 0, // 去掉右边距
          top: 80
        },
        xAxis: {
          type: "category",
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false }
        },
        yAxis: {
          type: "value",
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false },
          splitLine: { show: false }
        },
        tooltip: {
          trigger: "item",
          formatter: function (params: any) {
            const value = params.value;
            tipValue = params.value;
            return `流量：${value} B`;
          }
        },
        series: [
          {
            data: rule.sseData || [],
            type: "line",
            smooth: true,
            showSymbol: false
          }
        ]
      };
      // 设置图表选项
      chartInstance.setOption(option);
      window.addEventListener("resize", () => {
        if (chartInstance) chartResize(chartInstance);
      });
    }
  });
};
const chartResize = (chartInstance: any) => {
  chartInstance && chartInstance.resize();
};
onBeforeUnmount(() => {
  window.removeEventListener("resize", chartResize);
});
// 初始化 SSE
const initializeSSE = () => {
  if (eventSource) {
    console.warn("SSE 已经在运行，不能重复启动");
    return;
  }

  const ruleIds = ruleList.value.map(rule => rule.id);
  if (ruleIds.length === 0) {
    console.warn("没有可用的规则ID");
    return;
  }

  // 构造 URL 和查询参数
  const url = `/api/netflow/flow-mirror/rules/stats/stream?appid=${appId}&ruleIds=${ruleIds.join(",")}`;

  // 创建 EventSource 实例
  eventSource = new EventSource(url);

  // 监听连接打开事件
  eventSource.onopen = () => {
    console.log("SSE 连接已建立");
  };

  // 监听消息事件
  eventSource.onmessage = event => {
    try {
      const data = JSON.parse(event.data);

      // 处理数据结构 [[id, [[time1,value1], [time2,value2], ...]]]
      if (Array.isArray(data) && data.length > 0) {
        data.forEach(item => {
          if (Array.isArray(item) && item.length === 2) {
            const id = item[0];
            const stats = item[1];

            const ruleItemIndex = ruleList.value.findIndex(rule => rule.id === id);

            if (ruleItemIndex !== -1 && Array.isArray(stats)) {
              const result = stats.map(stat => stat[1]);
              ruleList.value[ruleItemIndex].sseData = result;
              const chartInstance = chartInstances.value[ruleItemIndex];

              if (chartInstance) {
                const option = {
                  title: [
                    {
                      text: "实时流量统计",
                      left: "-1%",
                      top: "-4%",
                      textStyle: {
                        color: "black",
                        fontSize: 13,
                        fontWeight: "400"
                      }
                    },
                    {
                      text: `当前：${formatBytes(result[result.length - 1])}`,
                      left: "right",
                      top: "-4%",
                      textStyle: {
                        color: "black",
                        fontSize: 13,
                        fontWeight: "400"
                      }
                    }
                  ],
                  grid: {
                    left: 0,
                    right: 0,
                    top: 80
                  },
                  xAxis: {
                    type: "category",
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false }
                  },
                  yAxis: {
                    type: "value",
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false },
                    splitLine: { show: false }
                  },
                  tooltip: {
                    trigger: "item",
                    formatter: function (params: any) {
                      return `流量：${formatBytes(params.value)}`;
                    }
                  },
                  series: [
                    {
                      data: result,
                      type: "line",
                      smooth: true,
                      showSymbol: false
                    }
                  ]
                };
                chartInstance.setOption(option);
              }
            }
          }
        });
      }
    } catch (error) {
      console.error("处理 SSE 数据时出错:", error);
    }
  };

  // 监听错误事件
  eventSource.onerror = error => {
    console.error("SSE 连接出错:", error);
    // 关闭连接以防止重复错误
    if (eventSource) {
      eventSource.close();
      eventSource = null;
      reconnectSSE(url); // 3 秒后重新连接
    }
  };
};
const sseConnect = ref<number | null>(null);
const connectTimes = ref(0);
function reconnectSSE(url: string) {
  // 等待一段时间后重新连接
  if (connectTimes.value >= 5) {
    ElMessage.error("SSE连接失败，请刷新页面重试");
    return;
  }
  sseConnect.value = setTimeout(() => {
    console.log("尝试重新连接 SSE...");
    sseConnect.value = null;
    initializeSSE();
    connectTimes.value += 1;
  }, 3000); // 3秒后重新连接
}
const closeSSE = () => {
  if (eventSource) {
    eventSource.close();
    eventSource = null; // 清空实例
    console.log("SSE 连接已关闭");
  }
  if (sseConnect.value) {
    clearTimeout(sseConnect.value);
    sseConnect.value = null;
  }
};
const restartSSE = () => {
  console.log("重启 SSE...");
  closeSSE(); // 先关闭现有连接
  initializeSSE(); // 重新启动连接
};
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return "0 B";
  bytes = bytes * 8;
  const k = 1024;
  const sizes = ["bit/s", "Kbit/s", "Mbit/s", "Gbit/s", "Tbit/s", "Pbit/s"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  const formattedValue = parseFloat((bytes / Math.pow(k, i)).toFixed(decimals));

  // 返回带单位的字符串
  return `${formattedValue} ${sizes[i]}`;
}

onMounted(async () => {
  await getFlowRulesData();
  initializeSSE();
  initCharts();
});
onBeforeUnmount(() => {
  closeSSE();
});
</script>

<style scoped lang="scss">
.rule-management {
  padding: 20px;
  color: #fff;
  width: 100%;
  min-height: 780px;
}

.rule-management {
  padding: 20px;
  background-color: #f5f7fa; /* 整体背景颜色 */
}

/* 标题栏样式 */
.header-container {
  background-color: #ffffff; /* 标题栏背景色 */
  // padding: 16px;
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  margin-bottom: 20px;
  padding: 15px 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

/* 搜索栏样式 */
.search-container {
  background-color: #ffffff; /* 搜索栏背景色 */
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.search-bar {
  display: flex;
  gap: 10px; /* 输入框之间的间距 */
}

.search-input {
  display: flex;
}
.search-btn {
  display: flex;
  .el-button {
    margin-top: 0px !important;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: black;
  // border: 1px solid #2c3e50;
  // padding: 10px;
  &-title {
    width: 400px;
    font-size: 28px;
    font-weight: bold;
    color: #333;
  }
  &-addbtn {
    width: 80px !important;
    // padding: 12px 10px;
    font-size: 12px;
    border-radius: 6px;
    font-weight: bold;
    margin-top: 0px !important;
  }
}

.card-container {
  margin-top: 20px;
}

.rule-card {
  // background-color: #1e1e2f;

  background-color: #ffffff; /* 整体背景颜色 */
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: black;
  margin-bottom: 20px;
  .el-button {
    width: 100%;
    // margin-top: 4px;
    font-size: 14px;
    padding: 10px 20px;
    // padding: 10px;
  }
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rule-header .tag {
  font-size: 14px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 5px;
  text-transform: uppercase;
}

.rule-header .tag.vxlan {
  background-color: #3498db;
  color: #fff;
}

.rule-header .tag.gre {
  background-color: #9b59b6;
  color: #fff;
}

.rule-header .priority {
  font-size: 14px;
  color: #bbb;
}

.rule-content p {
  margin: 12px 0;
}
/* 对话框整体样式 */
.add-rule-dialog {
  background-color: #1e1e2e;
  color: #ffffff;
}

/* 表单容器样式 */
.form-container {
  padding: 20px;
  // background-color: #2a2a3d;
  border-radius: 8px;
}

/* 表单标签样式 */
.el-form-item__label {
  color: #ffffff;
}

/* 按钮组样式 */
.dialog-footer {
  text-align: right;
  padding: 10px 20px;
}
.el-pagination {
  justify-content: end;
}
// .rule-card::before {
//   content: "";
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background: rgba(0, 0, 0, 0.1); /* 灰色遮罩层，透明度可调整 */
//   z-index: 1; /* 确保遮罩层覆盖在卡片内容之上 */
//   pointer-events: none; /* 确保遮罩层不影响点击事件 */
// }
</style>
