<template>
  <div class="flex justify-between mb-15px indicator-wrapper" v-loading="loading">
    <Indicator :value="totalPackets" :unit="'总数据包'"></Indicator>
    <Indicator :value="inPackets" :unit="'进网'"></Indicator>
    <Indicator :value="outPackets" :unit="'出网'"></Indicator>
    <Indicator :value="totalBytes" :unit="'总流量'" :trafficType="true"></Indicator>
    <Indicator :value="inBytes" :unit="'进网'" :trafficType="true"></Indicator>
    <Indicator :value="outBytes" :unit="'出网'" :trafficType="true"></Indicator>
  </div>
  <div class="flex justify-between mt-10px indicator-wrapper" v-loading="chartLoading">
    <Ranking
      class="w-100%"
      :title="'TCP标志'"
      :rankingList="castTypeDataList"
      :showNumber="false"
    ></Ranking>
    <Ranking
      class="w-100%"
      :title="'IP版本'"
      :rankingList="ipVersionList"
      :showNumber="false"
    ></Ranking>
    <Ranking
      class="w-100%"
      :title="'协议'"
      :rankingList="protocolDataList"
      :showNumber="false"
    ></Ranking>
    <Ranking
      class="w-100%"
      :title="'数据包类型'"
      :rankingList="tcpFlagDataList"
      :showNumber="false"
    ></Ranking>
  </div>
  <div class="mt-16px flex justify-between w-100%" v-loading="chartLoading">
    <BaseEcharts :options="packetSizeDistributionOptions" height="280px" />
    <BaseEcharts :options="regionTrafficOptions" height="280px" class="ml-8px" />
  </div>
</template>
<script setup lang="ts">
import {
  getFlowOverview,
  getDataPacketType,
  getDataPacketRange,
  getOverviewRegion
} from "@/api/flow/overview";
import { applicationStore } from "@/store/modules/application";
import { convertBytes } from "@/utils/trafficStr";
import { formatNums } from "@/utils/formatStr";
const useApplicationStore = applicationStore();
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
export interface ICastType {
  unicast_packets: number;
  broadcast_packets: number;
  multicast_packets: number;
  total_packets: number;
}
export interface IIpVersion {
  ipv4_packets: number;
  ipv6_packets: number;
  ipv4_bytes: number;
  ipv6_bytes: number;
  total_packets: number;
  total_bytes: number;
}
export interface IProtocol {
  tcp_packets: number;
  udp_packets: number;
  tcp_bytes: number;
  udp_bytes: number;
  total_packets: number;
  total_bytes: number;
}

const totalPackets = ref("0");
const inPackets = ref("0");
const outPackets = ref("0");
const totalBytes = ref("0");
const inBytes = ref("0");
const outBytes = ref("0");
const packetSizeDistributionOptions = ref({});
const regionTrafficOptions = ref({});
const castTypeDataList = ref<IRankItem[]>([]);
const castTypeData = ref<Partial<ICastType>>({});
const ipVersionList = ref<IRankItem[]>([]);
const ipVersionData = ref<Partial<IIpVersion>>({});
const protocolDataList = ref<IRankItem[]>([]);
const protocolData = ref<Partial<IProtocol>>({});
const tcpFlagDataList = ref<IRankItem[]>([]);
const tcpFlagData = ref<Partial<ITcpFlags>>({});
export interface ITcpFlags {
  syn_packets: number;
  ack_packets: number;
  fin_packets: number;
  rst_packets: number;
  psh_packets: number;
  total_packets: number;
}
const loading = ref(false);
const chartLoading = ref(false);

const getFlowOverviewData = async () => {
  const params = {
    appid: useApplicationStore.appId
  };
  const res = await getFlowOverview(params);
  if (res.code === 0) {
    totalPackets.value = res.entity.total_packets;
    inPackets.value = res.entity.in_packets;
    outPackets.value = res.entity.out_packets;
    totalBytes.value = res.entity.total_bytes;
    inBytes.value = res.entity.in_bytes;
    outBytes.value = res.entity.out_bytes;
  }
};
const getDataPacket = async () => {
  const res = await getDataPacketType();
  if (res.code === 0) {
    castTypeData.value = res.entity.cast_type || {};
    castTypeDataList.value = [
      {
        name: "单播包数",
        proportion:
          ((castTypeData.value?.unicast_packets || 0) / (castTypeData.value?.total_packets || 0)) *
          100,
        totalScore: castTypeData.value?.unicast_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "广播包数",
        proportion:
          ((castTypeData.value?.broadcast_packets || 0) /
            (castTypeData.value?.total_packets || 0)) *
          100,
        totalScore: castTypeData.value?.broadcast_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "组播包数",
        proportion:
          ((castTypeData.value?.multicast_packets || 0) /
            (castTypeData.value?.total_packets || 0)) *
          100,
        totalScore: castTypeData.value?.multicast_packets || 0,
        color: "#445fde",
        unit: ""
      }
    ];
    ipVersionData.value = res.entity.ip_version || {};
    ipVersionList.value = [
      {
        name: "ipv4包数",
        proportion:
          ((ipVersionData.value?.ipv4_packets || 0) / (ipVersionData.value?.total_packets || 0)) *
          100,
        totalScore: ipVersionData.value?.ipv4_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "ipv6包数",
        proportion:
          ((ipVersionData.value?.ipv6_packets || 0) / (ipVersionData.value?.total_packets || 0)) *
          100,
        totalScore: ipVersionData.value?.ipv6_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "ipv4字节数",
        proportion:
          ((ipVersionData.value?.ipv4_bytes || 0) / (ipVersionData.value?.total_bytes || 0)) * 100,
        totalScore: convertBytes(ipVersionData.value?.ipv4_bytes || 0).fixValue,
        color: "#445fde",
        unit: convertBytes(ipVersionData.value?.ipv4_bytes || 0).unit
      },
      {
        name: "ipv6字节数",
        proportion:
          ((ipVersionData.value?.ipv6_bytes || 0) / (ipVersionData.value?.total_bytes || 0)) * 100,
        totalScore: convertBytes(ipVersionData.value?.ipv6_bytes || 0).fixValue,
        color: "#445fde",
        unit: convertBytes(ipVersionData.value?.ipv6_bytes || 0).unit
      }
    ];
    protocolData.value = res.entity.protocol || {};
    protocolDataList.value = [
      {
        name: "tcp包数",
        proportion:
          ((protocolData.value?.tcp_packets || 0) / (protocolData.value?.total_packets || 0)) * 100,
        totalScore: protocolData.value?.tcp_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "udp包数",
        proportion:
          ((protocolData.value?.udp_packets || 0) / (protocolData.value?.total_packets || 0)) * 100,
        totalScore: protocolData.value?.udp_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "tcp字节数",
        proportion:
          ((protocolData.value?.tcp_bytes || 0) / (protocolData.value?.total_bytes || 0)) * 100,
        totalScore: convertBytes(protocolData.value?.tcp_bytes || 0).fixValue,
        color: "#445fde",
        unit: convertBytes(protocolData.value?.tcp_bytes || 0).unit
      },
      {
        name: "udp字节数",
        proportion:
          ((protocolData.value?.udp_bytes || 0) / (protocolData.value?.total_bytes || 0)) * 100,
        totalScore: convertBytes(protocolData.value?.udp_bytes || 0).fixValue,
        color: "#445fde",
        unit: convertBytes(protocolData.value?.tcp_bytes || 0).unit
      }
    ];
    tcpFlagData.value = res.entity.tcp_flags || {};
    tcpFlagDataList.value = [
      {
        name: "SYN (Synchronize) 数据包",
        proportion:
          ((tcpFlagData.value?.syn_packets || 0) / (tcpFlagData.value?.total_packets || 0)) * 100,
        totalScore: tcpFlagData.value?.syn_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "ACK (Acknowledgment) 数据包",
        proportion:
          ((tcpFlagData.value?.ack_packets || 0) / (tcpFlagData.value?.total_packets || 0)) * 100,
        totalScore: tcpFlagData.value?.ack_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "FIN (Finish) 数据包",
        proportion:
          ((tcpFlagData.value?.fin_packets || 0) / (tcpFlagData.value?.total_packets || 0)) * 100,
        totalScore: tcpFlagData.value?.fin_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "RST (Reset) 数据包",
        proportion:
          ((tcpFlagData.value?.rst_packets || 0) / (tcpFlagData.value?.total_packets || 0)) * 100,
        totalScore: tcpFlagData.value?.rst_packets || 0,
        color: "#445fde",
        unit: ""
      },
      {
        name: "PSH (Push) 数据包",
        proportion:
          ((tcpFlagData.value?.psh_packets || 0) / (tcpFlagData.value?.total_packets || 0)) * 100,
        totalScore: tcpFlagData.value?.psh_packets || 0,
        color: "#445fde",
        unit: ""
      }
    ];
  }
};
const getDataPacketrange = async () => {
  const res = await getDataPacketRange();
  if (res.code === 0) {
    const data = res.entity.ranges.map(
      (item: { range_start: number; range_end: number; count: number; percentage: number }) => {
        const formattedValue = formatNums(item.count);
        return {
          value: item.percentage.toFixed(2),
          name: `${item.range_start} - ${item.range_end}`,
          unit: formattedValue.unit
        };
      }
    );
    packetSizeDistributionOptions.value = {
      ...packetSizeDistributionOptions.value,
      ...setting,
      title: {
        ...setting.title,
        text: "数据包大小分布"
      },
      legend: {
        bottom: "1%",
        left: "center",
        type: "scroll"
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "65%"],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: "center"
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 15
            }
          },
          labelLine: {
            show: false
          },
          data
        }
      ],
      tooltip: {
        trigger: "item",
        formatter: function (params: any) {
          const { name, value, data } = params;
          return `${name}: ${value} ${data.unit} `;
        }
      }
    };
  }
};
//! 通用配置
const setting = {
  title: {
    // text: "",
    x: "10px",
    y: "1px",
    textStyle: {
      fontSize: 16,
      fontWeight: "400",
      color: "#000000"
    }
  },
  graphic: [
    {
      type: "rect",
      shape: { x: 0, y: 5, width: 4, height: 15 },
      style: { fill: "#3375f9" },
      z: 100
    }
  ],
  grid: {
    left: "1px",
    right: "1%",
    bottom: "1px",
    top: "60px",
    containLabel: true
  }
};
const getOverviewregion = async () => {
  const res = await getOverviewRegion();
  if (res.code === 0) {
    const data = res.entity.region_traffic.map((item: { region: string; total_bytes: number }) => {
      const formattedValue = convertBytes(item.total_bytes);
      return {
        value: item.total_bytes,
        name: item.region,
        formattedCount: `${formattedValue.fixValue} ${formattedValue.unit}`
      };
    });
    regionTrafficOptions.value = {
      ...regionTrafficOptions.value,
      ...setting,
      title: {
        ...setting.title,
        text: "地区信息"
      },
      legend: {
        bottom: "1%",
        left: "center",
        type: "scroll"
      },
      series: [
        {
          type: "pie",
          radius: ["40%", "65%"],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: "center"
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 15
            }
          },
          labelLine: {
            show: false
          },
          data
        }
      ],
      tooltip: {
        trigger: "item",
        formatter: function (params: any) {
          const { name, data } = params;
          return `${name}: ${data.formattedCount}`;
        }
      }
    };
  }
};
onMounted(() => {
  loading.value = true;
  chartLoading.value = true;
  Promise.all([
    getFlowOverviewData(),
    getDataPacket(),
    getDataPacketrange(),
    getOverviewregion()
  ]).finally(() => {
    loading.value = false;
    chartLoading.value = false;
  });
});
</script>
<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
