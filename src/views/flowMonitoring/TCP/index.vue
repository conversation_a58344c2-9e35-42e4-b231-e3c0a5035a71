<template>
  <div>
    <div
      ref="chartContainer"
      :style="{ width: isIpDetail ? '74vw' : '100%', height: '350px' }"
      v-loading="chartLoading"
    ></div>
    <div class="tb-header mt-20px">
      <div v-if="!isIpDetail">
        <el-input
          style="width: 240px"
          placeholder="***********"
          v-model="pageParams.targetIp"
          clearable
          @blur="inputChange"
        ></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
          >搜索</el-button
        >
      </div>
      <div>
        <el-button type="primary" @click="dialogFormVisible = true" :icon="Filter">筛选</el-button>
      </div>
    </div>
    <MyTable
      :data="list.records"
      :total="list.total"
      style="width: 100%"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      v-loading="tableLoading"
      :default-sort="{
        prop: 'start_ts && last_ts && packets && bytes && packets_1to2 && packets_2to1 && bytes_1to2 && bytes_2to1 && syn_count && fin_count && rst_count',
        order: 'descending'
      }"
      @sort-change="handleSortChange"
    >
      <my-column property="ip1" label="端点1" width="180" />
      <my-column
        v-for="column in tableColumns"
        :key="column.property"
        :property="column.property"
        :label="column.label"
        :sortable="column.sortable"
      />
      <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
        <template #default="scope">
          <span class="operate" @click="goDetail(scope)">查看详情</span>
        </template>
      </my-column>
    </MyTable>
  </div>
  <el-dialog
    draggable
    :align-center="true"
    v-model="dialogFormVisible"
    title="筛选列表"
    :close-on-click-modal="false"
    width="900"
  >
    <div class="column-selector">
      <el-checkbox-group v-model="selectedColumns">
        <el-checkbox v-for="column in columnConfig" :key="column.property" :value="column.property">
          {{ column.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { convertBytes } from "@/utils/trafficStr";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search, Filter } from "@element-plus/icons-vue";
import { getTcpList } from "@/api/flow/tcp";
import { createChartOptions } from "../components/flowChart";
import { debounce } from "lodash-es";

const props = defineProps({
  // 标题
  isIpDetail: {
    type: Boolean,
    required: true,
    default: false
  },
  ipDetailAddress: {
    type: String,
    default: ""
  }
});
import { getChartTraffic } from "@/api/flow/detail";
import { serviceTimeStore } from "@/store/modules/global";
import { formatTimestamp } from "@/utils/dateStr";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
const timeVals = ref({ start: "", end: "" });
const dialogFormVisible = ref(false);
const searchFlag = ref(false);
const chartLoading = ref(false);
//loading动画
const tableLoading = ref(false);
//列表参数
const pageParams = reactive({
  startTime: "",
  endTime: "",
  page: "1",
  rows: "10",
  order: "",
  sort: "",
  // ip: 1,
  appid: useApplicationStore.appId,
  targetIp: ""
});
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "1";
  } else {
    pageParams.order = "0";
  }
  pageParams.sort = sort;
  loadData();
};
const inputChange = () => {
  const ipv4Regex =
    /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/;
  if (pageParams.targetIp && !ipv4Regex.test(pageParams.targetIp)) {
    ElMessage.error("请输入正确的IP地址");
    return;
  }
};
// 列配置，包括是否显示
const columnConfig = ref([
  { property: "port1", label: "端口1", sortable: false, show: true },
  { property: "ip1_region", label: "端点1地址", sortable: false, show: true },
  { property: "ip2", label: "端点2", sortable: false, show: true },
  { property: "port2", label: "端口2", sortable: false, show: true },
  { property: "ip2_region", label: "端点2地址", sortable: false, show: true },
  { property: "service", label: "服务名", sortable: false, show: true },
  { property: "protocol", label: "协议", sortable: false, show: true },
  { property: "packets", label: "包数量", sortable: "custom", show: true },
  { property: "packets_1to2", label: "端点1到2包数", sortable: "custom", show: true },
  { property: "packets_2to1", label: "端点2到1包数", sortable: "custom", show: true },
  { property: "bytes", label: "字节数", sortable: "custom", show: true },
  { property: "bytes_1to2", label: "端点1到2字节数", sortable: "custom", show: true },
  { property: "bytes_2to1", label: "端点2到1字节数", sortable: "custom", show: true },
  { property: "syn_count", label: "SYN数量", sortable: "custom", show: true },
  { property: "fin_count", label: "FIN数量", sortable: "custom", show: true },
  { property: "rst_count", label: "RST数量", sortable: "custom", show: true },
  { property: "startTs", label: "开始时间", sortable: "custom", show: true },
  { property: "lastTs", label: "结束时间", sortable: "custom", show: true }
]);

// 用户选择的列
const selectedColumns = ref(columnConfig.value.slice(0, 10).map(col => col.property));

// 根据用户选择更新tableColumns
const tableColumns = computed(() => {
  return columnConfig.value
    .filter(col => selectedColumns.value.includes(col.property) || col.property === "operation")
    .map(col => ({
      ...col,
      sortable: col.sortable !== undefined ? col.sortable : false
    }));
});
//修改每页条数
const handleSizeChange = (val: string) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: string) => {
  pageParams.page = val;
  loadData();
};
//搜索
const search = debounce(() => {
  pageParams.page = "1";
  pageParams.sort = "";
  pageParams.order = "";
  searchFlag.value = true;
  loadData();
}, 500);
const list = reactive({
  records: [],
  total: 0
});
interface TimeVals {
  start: string;
  end: string;
}
//列表数据
function loadData(timeVals?: TimeVals) {
  tableLoading.value = true;
  if (searchFlag.value && props.isIpDetail && props.ipDetailAddress !== "") {
    pageParams.targetIp = pageParams.targetIp.trim();
  } else if (props.isIpDetail && props.ipDetailAddress !== "") {
    pageParams.targetIp = props.ipDetailAddress;
  } else {
    pageParams.targetIp = pageParams.targetIp.trim();
  }
  searchFlag.value = false;
  pageParams.startTime = timeVals ? timeVals.start : serTimeStore.serviceTimeData.start_time;
  pageParams.endTime = timeVals ? timeVals.end : serTimeStore.serviceTimeData.end_time;
  getTcpList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records.map((item: any) => {
          return {
            ...item,
            bytes: convertBytes(item.bytes).fixValue + " " + convertBytes(item.bytes).unit,
            bytes_1to2:
              convertBytes(item.bytes_1to2).fixValue + " " + convertBytes(item.bytes_1to2).unit,
            bytes_2to1:
              convertBytes(item.bytes_2to1).fixValue + " " + convertBytes(item.bytes_2to1).unit,
            startTs: formatTimestamp(item.start_ts),
            lastTs: formatTimestamp(item.last_ts),
            service: item.service === "UNKNOWN" ? "未知" : item.service
          };
        });
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const chartContainer = ref(null);
const selectedData = ref([]);
const totalRecords = ref(0);

const timestamps = ref([]);
const chartValues = reactive({
  tcp: [],
  inbound: [],
  outbound: [],
  ipv4: [],
  ipv6: [],
  total: [],
  udp: []
});
const serTimeStore = serviceTimeStore();
const initChartData = async () => {
  try {
    chartLoading.value = true;
    const params = {
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      appid: useApplicationStore.appId
      // ip: useServiceNameStore.serviceName
    };
    const res = await getChartTraffic(params);

    if (res.code === 0) {
      timestamps.value = res.entity.tcp.map(item => formatTimestamp(item.timestamp));

      const extractValues = data => data.map(item => item.value);

      chartValues.tcp = extractValues(res.entity.tcp);
      chartValues.inbound = extractValues(res.entity.inbound);
      chartValues.ipv4 = extractValues(res.entity.ipv4);
      chartValues.ipv6 = extractValues(res.entity.ipv6);
      chartValues.outbound = extractValues(res.entity.outbound);
      chartValues.total = extractValues(res.entity.total);
      chartValues.udp = extractValues(res.entity.udp);

      initChart();
    }
    chartLoading.value = false;
  } catch (err) {
    chartLoading.value = false;
    console.error(err);
  }
};
// 初始化 ECharts 实例
const initChart = () => {
  let chartInstance;
  let isLegendChanging = false;

  chartInstance = echarts.init(chartContainer.value);
  chartInstance.setOption(createChartOptions(timestamps.value, chartValues));
  chartInstance.dispatchAction({
    type: "takeGlobalCursor",
    key: "brush",
    brushOption: {
      brushType: "lineX",
      brushMode: "single"
    }
  });
  // 监听 legend 事件
  chartInstance.on("legendselectchanged", params => {
    isLegendChanging = true;
  });

  // 监听 brush 事件
  chartInstance.on("brushSelected", params => {
    if (isLegendChanging) {
      isLegendChanging = false;
      return;
    }
    const batch = params?.batch?.[0];
    const areas = batch?.areas?.[0];
    const coordRange = areas?.coordRange;
    let xStartValue = coordRange?.[0] ?? null;
    let xEndValue = coordRange?.[1] ?? null;
    if (
      xStartValue === null ||
      xEndValue === null ||
      xStartValue >= timestamps.value.length ||
      xEndValue >= timestamps.value.length
    ) {
      loadData();
      chartInstance.setOption({
        graphic: [
          {
            type: "text",
            style: {
              text: ``
            }
          }
        ]
      });
      selectedData.value = [];
      totalRecords.value = 0;
      return;
    }
    if (xStartValue === xEndValue) {
      if (xStartValue === 0) {
        xEndValue = xEndValue + 1;
      } else if (xEndValue === timestamps.value.length - 1) {
        xStartValue = xStartValue - 1;
      } else {
        xEndValue = xEndValue + 1;
      }
    }
    const selectedXData = timestamps.value.slice(xStartValue, xEndValue + 1);

    if (selectedXData.length < 2) {
      selectedData.value = [];
      totalRecords.value = 0;
      chartInstance.setOption({
        graphic: [
          {
            type: "text",
            style: {
              text: ``
            }
          }
        ]
      });
      return;
    }

    timeVals.value = {
      start: selectedXData[0],
      end: selectedXData[selectedXData.length - 1]
    };
    chartInstance.setOption({
      graphic: [
        {
          type: "text",
          style: {
            text: `当前选取时间范围：${selectedXData[0]} - ${selectedXData[selectedXData.length - 1]}`,
            fill: "#666",
            font: "bold 15px Microsoft YaHei"
          },
          left: "center",
          top: 40
        }
      ]
    });
    loadData(timeVals.value);
  });
};

// 跳转详情页
const goDetail = (scope: {
  row: { segment_id: string; session_id: string; start_ts: string; last_ts: string };
}) => {
  const params = new URLSearchParams();
  params.append("segment_id", scope.row.segment_id);
  params.append("session_id", scope.row.session_id);
  params.append("start", scope.row.start_ts);
  params.append("end", scope.row.last_ts);
  const url = `/TCPdetail?${params.toString()}`;
  window.open(url, "_blank");
};
// 组件挂载时初始化图表
onMounted(() => {
  initChartData();
  // loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.column-selector {
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
