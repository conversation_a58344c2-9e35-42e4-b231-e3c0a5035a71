<template>
  <div class="tb-header">
    <div>
      <el-input style="width: 240px" placeholder="192.168.1.1" clearable> </el-input>
      <el-button type="primary" class="ml-15px" :icon="Search">搜索</el-button>
    </div>
    <div>
      <span>总包数：123456</span>
      <span class="ml-15px">总字节数：123456</span>
    </div>
  </div>
  <my-table
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="time" label="时间" />
    <my-column property="time" label="端点1" />
    <my-column property="time" label="端口1" />
    <my-column property="time" label="端点2" />
    <my-column property="time" label="端口2" />
    <my-column property="time" label="tcpflag" />
    <my-column property="time" label="传输层协议" />
    <my-column property="time" label="长度" />
    <my-column property="time" label="端点1MAC" />
    <my-column property="time" label="端点2MAC" />
  </my-table>
  <div class="flex justify-between mt-10px indicator-wrapper">
    <Ranking class="w-100%" :title="'端点统计（Top5）'"></Ranking>
    <Ranking class="w-100%" :title="'端口统计（Top5）'"></Ranking>
    <Ranking class="w-100%" :title="'协议统计（Top5）'"></Ranking>
  </div>
</template>
<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import Ranking from "@/components/Ranking/index.vue";
import { Search } from "@element-plus/icons-vue";
import { getPacketPkt, getPacketStatis } from "@/api/flow/packet";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const tableLoading = ref(false);
const list = reactive({
  records: [],
  total: 0
});
const handleSizeChange = (val: number) => {
  console.log(val);
};
const handleCurrentChange = (val: number) => {
  console.log(val);
};
const pageParams = reactive({
  appid: "",
  page: 1,
  page_size: 10
});
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  getPacketPkt(pageParams).then(response => {
    if (response.code === 0) {
      console.log(response);
    }
  });
}
onMounted(() => {
  loadData();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
