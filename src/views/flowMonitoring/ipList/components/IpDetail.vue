<template>
  <div>
    <div class="tb-header">
      <div>
        <el-input style="width: 240px" placeholder="请输入TCP" v-model="TCP" clearable></el-input>
        <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
          >搜索</el-button
        >
      </div>
      <div>
        <el-button type="primary" @click="dialogFormVisible = true" :icon="Filter">筛选</el-button>
      </div>
    </div>
    <MyTable
      :data="tableData"
      :total="tableData.length"
      style="width: 100%"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <my-column
        v-for="column in tableColumns"
        :key="column.property"
        :property="column.property"
        :label="column.label"
        :sortable="column.sortable"
      />
    </MyTable>
  </div>
  <el-dialog
    draggable
    :align-center="true"
    v-model="dialogFormVisible"
    title="筛选列表"
    :close-on-click-modal="false"
    width="900"
  >
    <div class="column-selector">
      <el-checkbox-group v-model="selectedColumns">
        <el-checkbox v-for="column in columnConfig" :key="column.property" :value="column.property">
          {{ column.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search, Filter } from "@element-plus/icons-vue";
const TCP = ref("");
const dialogFormVisible = ref(false);
// 列配置，包括是否显示
const columnConfig = ref([
  { property: "extreme", label: "端点1", sortable: false, show: true },
  { property: "extremeMAC", label: "端点1MAC", sortable: false, show: true },
  { property: "extremeLocal", label: "端点1地理位置", sortable: false, show: true },
  { property: "extreme_point", label: "端点1端点", sortable: false, show: true },
  { property: "extreme2", label: "端点2", sortable: false, show: true },
  { property: "extremeMAC2", label: "端点2MAC", sortable: false, show: true },
  { property: "extreme2Local", label: "端点2地理位置", sortable: false, show: true },
  { property: "extreme2_point", label: "端点2端点", sortable: false, show: true },
  { property: "total_bytes", label: "总字节数", sortable: "custom", show: true },
  { property: "extreme_byte", label: "端点1发送字节", sortable: "custom", show: true },
  { property: "extreme2_byte", label: "端点2发送字节", sortable: "custom", show: true },
  { property: "createTime", label: "会话创建时间", sortable: "custom", show: true },
  { property: "endTime", label: "会话结束时间", sortable: "custom", show: true },
  { property: "sustainTime", label: "会话持续时间", sortable: "custom", show: true },
  { property: "total_packe", label: "总数据包", sortable: "custom", show: true },
  { property: "extreme_packe", label: "端点1发送数据包", sortable: "custom", show: true },
  { property: "extreme2_packe", label: "端点2数据包", sortable: "custom", show: true },
  { property: "average_packe", label: "平均包长", sortable: "custom", show: true },
  { property: "sync_package", label: "同步包", sortable: "custom", show: true },
  { property: "confirmation_package", label: "同步确认包", sortable: "custom", show: true }
]);

// 用户选择的列
const selectedColumns = ref(columnConfig.value.slice(0, 11).map(col => col.property));

// 根据用户选择更新tableColumns
const tableColumns = computed(() => {
  return columnConfig.value
    .filter(col => selectedColumns.value.includes(col.property) || col.property === "operation")
    .map(col => ({
      ...col,
      sortable: col.sortable !== undefined ? col.sortable : false
    }));
});

// 修改每页条数
const handleSizeChange = (val: number) => {
  console.log(val);
};

// 分页
const handleCurrentChange = (val: number) => {
  console.log(val);
};

const tableData = [
  {
    extreme: "192.168.1.10",
    extremeMAC: "00:00:SE",
    extremeLocal: "局域网",
    extreme_point: "38758",
    extreme2: "192.168.1.17",
    extremeMAC2: "00:00:SE",
    extreme2Local: "局域网",
    extreme2_point: "13002",
    total_bytes: "120KB",
    extreme_byte: "70KB",
    extreme2_byte: "50KB",
    createTime: "18000ms",
    endTime: "180ms",
    sustainTime: "100ms",
    total_packe: "100",
    extreme_packe: "90",
    extreme2_packe: "10",
    average_packe: "50",
    sync_package: "10",
    confirmation_package: "50"
  },
  {
    extreme: "192.168.1.11",
    extremeMAC: "00:00:SE",
    extremeLocal: "局域网",
    extreme_point: "38758",
    extreme2: "************",
    extremeMAC2: "00:00:SE",
    extreme2Local: "局域网",
    extreme2_point: "13002",
    total_bytes: "120KB",
    extreme_byte: "70KB",
    extreme2_byte: "50KB",
    createTime: "18000ms",
    endTime: "180ms",
    sustainTime: "100ms",
    total_packe: "100",
    extreme_packe: "90",
    extreme2_packe: "10",
    average_packe: "50",
    sync_package: "10",
    confirmation_package: "50"
  }
];

// 搜索
function search() {}
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.column-selector {
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
