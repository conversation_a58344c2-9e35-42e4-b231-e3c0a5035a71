<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入源IP"
        v-model="pageParams.srcIp"
        clearable
      ></el-input>
      <el-input
        style="width: 240px; margin-left: 15px"
        placeholder="请输入源端口"
        v-model="pageParams.srcPort"
        clearable
      ></el-input>
      <el-input
        style="width: 240px; margin-left: 15px"
        placeholder="请输入目的IP"
        v-model="pageParams.dstIp"
        clearable
      ></el-input>
      <el-input
        style="width: 240px; margin-left: 15px"
        placeholder="请输入目的端口"
        v-model="pageParams.dstPort"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'totalBytes && retRate && rstRate && errorCount',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="srcIp" label="源IP" />
    <my-column property="srcPort" label="源端口" />
    <my-column property="dstIp" label="目的IP" />
    <my-column property="dstPort" label="目的端口" />
    <my-column property="region" label="地理位置">
      <template v-slot="{ row }">
        {{ row.region ? row.region : "-" }}
      </template>
    </my-column>
    <my-column property="totalBytes" label="总流量" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ convertBytes(row.totalBytes).fixValue }}{{ convertBytes(row.totalBytes).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalPack" label="总数据包" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ row.totalPack }}
        </span>
      </template>
    </my-column>
    <my-column property="retRate" label="重传包占比" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.retRate > 0 ? '#f56c6c' : '' }"> {{ row.retRate }}% </span>
      </template>
    </my-column>

    <my-column property="rstRate" label="重置包占比" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.rstRate > 0 ? '#f56c6c' : '' }"> {{ row.rstRate }}% </span>
      </template>
    </my-column>

    <my-column property="errorCount" label="连接失败数（次）" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.errorCount > 0 ? '#f56c6c' : '' }"> {{ row.errorCount }} </span>
      </template>
    </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { getFlowSessionList } from "@/api/traffic/ip";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { applicationStore } from "@/store/modules/application";
import { convertBytes } from "@/utils/trafficStr";
import { serviceNameStore } from "@/store/modules/service";
import { number } from "echarts";
const useServiceNameStore = serviceNameStore();
const useApplicationStore = applicationStore();
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  srcIp: "",
  srcPort: "",
  dstIp: "",
  dstPort: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  ip: ""
});
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.ip = useServiceNameStore.serviceName;
  pageParams.srcIp = pageParams.srcIp.trim();
  pageParams.srcPort = pageParams.srcPort.trim();
  pageParams.dstIp = pageParams.dstIp.trim();
  pageParams.dstPort = pageParams.dstPort.trim();
  pageParams.appid = useApplicationStore.appId;
  getFlowSessionList(pageParams).then(response => {
    if (response.code === 0) {
      response.records.forEach((record: { TCP: string }) => {
        if (!record.TCP) {
          record.TCP = "TCP";
        }
      });
      list.records = response.records;
      list.total = Number(response.total);
      tableLoading.value = false;
    } else {
      tableLoading.value = false;
    }
  });
}
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
</style>
