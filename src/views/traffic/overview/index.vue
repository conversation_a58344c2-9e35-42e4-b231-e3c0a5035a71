<template>
  <div class="flex justify-between mb-15px indicator-container" v-loading="loading">
    <Indicator
      v-for="(item, i) in summaryCount.slice(0, 5)"
      :key="'front-' + i"
      :trafficType="item.type"
      :value="item.value"
      :unit="item.label"
    ></Indicator>
  </div>
  <div class="flex justify-between mb-15px indicator-container" v-loading="loading">
    <Indicator
      v-for="(item, i) in summaryCount.slice(-5)"
      :key="'back-' + i"
      :trafficType="item.type"
      :value="item.value"
      :unit="item.label"
    ></Indicator>
  </div>

  <div class="flex justify-between mb-10px">
    <TitleCom :title="'比特率'" style="padding-bottom: 0px"></TitleCom>
    <div class="tag">
      <span :class="{ active: isSelected.edit }" @click="selectButton('edit', granularityVal)">{{
        getTimeValue(granularityVal)
      }}</span>
      <span
        :class="{ active: isSelected.share }"
        @click="selectButton('share', granularityNextVal)"
        >{{ getTimeValue(granularityNextVal) }}</span
      >
    </div>
  </div>
  <div class="flex justify-between mb-15px indicator-container">
    <BaseEcharts :options="apiDurationChartData" height="250px" v-loading="stutterCountLoading" />
  </div>
  <div class="tabel-container">
    <div style="width: 50%">
      <MyTable :data="bytesList" v-loading="isLoading" style="width: 100%">
        <MyColumn property="label" label="流量类型" />
        <MyColumn property="value" label="字节数">
          <template #default="{ row }">
            <span>{{ row.value }}</span>
          </template>
        </MyColumn>
        <MyColumn property="type" label="百分比" width="350">
          <template #default="{ row }">
            <el-progress
              :text-inside="true"
              :stroke-width="24"
              :percentage="row.type"
              :color="row.color"
              stroke-linecap="butt"
              status="success"
            />
          </template>
        </MyColumn>
        <MyColumn property="reference" label="参考值" />
      </MyTable>
    </div>
    <div style="width: 49.5%">
      <MyTable :data="PackRetList" v-loading="isLoading">
        <MyColumn property="label" label="TCP概览" />
        <MyColumn property="value" label="数据包">
          <template #default="{ row }">
            <span>{{ row.value }}</span>
          </template>
        </MyColumn>
        <MyColumn property="type" label="百分比" width="350">
          <template #default="{ row }">
            <el-progress
              :text-inside="true"
              :stroke-width="24"
              :percentage="row.type"
              :stroke-linecap="'square'"
              :color="row.color"
              status="success"
            />
          </template>
        </MyColumn>
        <MyColumn property="reference" label="参考值" />
      </MyTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import {
  getSummaryType,
  getStatTrafficBytes,
  getStatPackRet,
  getStatAvgBytes
} from "@/api/traffic/overview";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { convertBytes } from "@/utils/trafficStr";
import { getTimeValue } from "@/components/baseEcharts/chartUtils";

const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const apiDurationChartData = ref({});
const isLoading = ref(false);
const stutterCountLoading = ref(false);
const loading = ref(false); // 顶部loading
const chartLoading = ref(false); // 顶部loading
// 上方数据
const summaryCount = ref([]);
const initSummary = async () => {
  const queue = { appid: useApplicationStore.appId };

  try {
    loading.value = true;
    const res = await getSummaryType(queue);
    if (res.code === 0 && res.entity) {
      const {
        maxBytesIn,
        totalPackIn,
        totalPack,
        maxPackIn,
        totalBytesIn,
        totalBytesOut,
        totalBytes,
        maxBytesOut,
        totalPackOut,
        maxPackOut
      } = res.entity;

      summaryCount.value = [
        { label: "总数据包", value: totalPack, type: false },
        { label: "流入数据包", value: totalPackIn, type: false },
        { label: "流入数据包峰值", value: maxPackIn, type: false },
        { label: "流出数据包", value: totalPackOut, type: false },
        { label: "流出数据包峰值", value: maxPackOut, type: false },
        { label: "总流量", value: totalBytes, type: true },
        { label: "流入流量", value: totalBytesIn, type: true },
        { label: "流入流量峰值", value: maxBytesIn, type: true },
        { label: "流出流量", value: totalBytesOut, type: true },
        { label: "流出流量峰值", value: maxBytesOut, type: true }
      ];
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
    console.error("Error logs:", error);
  }
};

// 图表
const granularityVal = ref(100);
const granularityNextVal = ref(null);
const initApiDurationChart = async (type = undefined) => {
  stutterCountLoading.value = true;
  const queue = {
    granularity: type
  };
  try {
    const res = await getStatAvgBytes(queue);
    if (res.code === 0 && res.entity) {
      const granularity = res.entity.granularity;
      if (granularityVal.value === 100) {
        granularityVal.value = granularity;
      }

      granularityNextVal.value = granularityNextVal.value
        ? granularityNextVal.value >= 0
          ? granularityNextVal.value
          : granularity
        : granularity === 0
          ? granularity
          : granularity - 1;
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#6376d2"];
      const params = {
        typ: granularity,
        color: color,
        name: " Gbps",
        titleType: "比特率",
        titleShow: false,
        originalTimes: originalTimes,
        seriesData: seriesData,
        areaStyle: true,
        enableDataZoom: true,
        setGrid: {
          left: "0%",
          right: "0%",
          bottom: "20%",
          top: "5%"
        },
        type: "line"
      };
      apiDurationChartData.value = getChartOptions(params);
      stutterCountLoading.value = false;
    }
  } catch (error) {
    stutterCountLoading.value = false;
    console.error("Error logs:", error);
  }
};

// 流量列表
const bytesList = ref([{}]);
const fetchBytesList = async () => {
  isLoading.value = true;

  try {
    const res = await getStatTrafficBytes({});
    if (res.code === 0) {
      const { ucastBytes, bcastBytes, mcastRate, mcastBytes, totalBytes, ucastRate, bcastRate } =
        res.entity;

      const ucastColor = ucastRate >= 90.0 ? "#6376D2" : "#f56c6c";
      const bcastColor = bcastRate < 5.0 ? "#6376D2" : "#f56c6c";
      const mcastColor = mcastRate < 5.0 ? "#6376D2" : "#f56c6c";

      bytesList.value = [
        {
          label: "单播流量",
          value:
            convertBytes(Number(ucastBytes)).fixValue + " " + convertBytes(Number(ucastBytes)).unit,
          type: ucastRate,
          reference: ">= 90.00%",
          color: ucastColor
        },
        {
          label: "广播流量",
          value:
            convertBytes(Number(bcastBytes)).fixValue + " " + convertBytes(Number(bcastBytes)).unit,
          type: bcastRate,
          reference: "< 5.00%",
          color: bcastColor
        },
        {
          label: "组播流量",
          value:
            convertBytes(Number(mcastBytes)).fixValue + " " + convertBytes(Number(mcastBytes)).unit,
          type: mcastRate,
          reference: "< 5.00%",
          color: mcastColor
        }
      ];
      isLoading.value = false;
    }
  } catch (error) {
    console.error("Error callChain:", error);
    isLoading.value = false;
  }
};

// 超时重传
const PackRetList = ref([{}]);
const fetchPackRet = async () => {
  isLoading.value = true;

  try {
    const res = await getStatPackRet({});
    if (res.code === 0) {
      const {
        timeoutRetCount,
        timeoutRetRate,
        rstCount,
        fastRetCount,
        sackRetCount,
        totalPackCount,
        sackRetRate,
        fastRetRate,
        rstRate
      } = res.entity;
      const timeoutRetColor = timeoutRetRate < 10.0096 ? "#6376D2" : "#f56c6c";
      const fastRetColor = fastRetRate >= 80.0 ? "#6376D2" : "#f56c6c";
      const sackRetColor = sackRetRate < 20.0 ? "#6376D2" : "#f56c6c";
      const rstColor = rstRate < 20.0 ? "#6376D2" : "#f56c6c";
      PackRetList.value = [
        {
          label: "服务器超时重传",
          value: timeoutRetCount,
          type: timeoutRetRate,
          reference: "< 10.0096",
          color: timeoutRetColor
        },
        {
          label: "快速重传",
          value: fastRetCount,
          type: fastRetRate,
          reference: ">= 80.00%",
          color: fastRetColor
        },
        {
          label: "DSACK(虚假重传)",
          value: sackRetCount,
          type: sackRetRate,
          reference: "< 20.00%",
          color: sackRetColor
        },
        {
          label: "重置包",
          value: rstCount,
          type: rstRate,
          reference: "< 20.00%",
          color: rstColor
        }
      ];
      isLoading.value = false;
    }
  } catch (error) {
    console.error("Error callChain:", error);
    isLoading.value = false;
  }
};
const isSelected = ref({
  edit: true,
  share: false
});
const selectButton = (type: string, granularity: number) => {
  if ((type === "edit" && isSelected.value.edit) || (type === "share" && isSelected.value.share)) {
    return;
  }
  isSelected.value.edit = false;
  isSelected.value.share = false;
  if (type === "edit") {
    isSelected.value.edit = true;
  } else if (type === "share") {
    isSelected.value.share = true;
  }
  initApiDurationChart(granularity);
};

onMounted(() => {
  initApiDurationChart();
  initSummary();
  fetchBytesList();
  fetchPackRet();
});
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.tabel-container {
  display: flex;
  justify-content: space-between;
}
.tag {
  font-size: 0px;
  display: inline-block;
  box-sizing: border-box;
  margin-left: 20px;
}
.tag span {
  width: 90px;
  height: 30px;
  cursor: pointer;
  line-height: 30px;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  border: 1px solid #e1e4e9;
}
.tag span:nth-child(2n) {
  border-right: 1px solid #e1e4e9;
}
.tag span.active {
  color: #fff;
  border: 1px solid #445fde;
  border-right: 0px;
  background: #445fde;
}
.tag span.active:nth-child(2n) {
  border: 1px solid #445fde;
}
</style>
