<template>
  <div class="floating-panel" @click.self.stop>
    <el-button class="toggle-btn" size="mini" @click="emit('toggle')" circle> – </el-button>

    <transition name="fade">
      <div class="panel-content">
        <!-- <el-scrollbar style="max-height: 100%" border> -->
        <div v-for="dev in devices" :key="dev.id" class="dev-item" style="margin-bottom: 10px">
          <TitlecCom :title="dev.name"></TitlecCom>
          <el-descriptions :column="1" size="small" border style="margin-bottom: 10px">
            <el-descriptions-item label="ID">{{ dev.id }}</el-descriptions-item>
            <el-descriptions-item label="类型">
              {{ getChineseTypeNameByNumber(dev.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="IP">{{ dev.ip }}</el-descriptions-item>
            <el-descriptions-item label="状态">{{ dev.status }}</el-descriptions-item>

            <!-- 防火墙专属字段 -->
            <template v-if="dev.type === 1">
              <el-descriptions-item label="CPU 利用率">{{ dev.cpuUsage }}</el-descriptions-item>
              <el-descriptions-item label="规则数">{{ dev.rulesCount }}</el-descriptions-item>
              <el-descriptions-item label="安全等级">{{ dev.securityLevel }}</el-descriptions-item>
            </template>

            <!-- 虚拟机专属字段 -->
            <template v-else-if="dev.type === 7">
              <el-descriptions-item label="CPU">{{ dev.cpu }}</el-descriptions-item>
              <el-descriptions-item label="内存">{{ dev.memory }}</el-descriptions-item>
              <el-descriptions-item label="磁盘">{{ dev.disk }}</el-descriptions-item>
            </template>

            <!-- 负载均衡专属字段 -->
            <template v-else-if="dev.type === 3">
              <el-descriptions-item label="后端实例数">{{ dev.backendCount }}</el-descriptions-item>
              <el-descriptions-item label="健康状态">{{ dev.healthStatus }}</el-descriptions-item>
              <el-descriptions-item label="调度策略">{{ dev.strategy }}</el-descriptions-item>
            </template>
          </el-descriptions>
          <!-- <hr /> -->
        </div>
        <!-- </el-scrollbar> -->
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getChineseTypeNameByNumber } from "../utils/index";
import TitlecCom from "@/components/TitleCom/index.vue";
const props = defineProps({
  isExpanded: {
    type: Boolean
  },
  lf: {
    type: Object,
    required: true
  }
});
const emit = defineEmits(["toggle"]);

// 模拟设备数据
const devices = [
  {
    id: "fw-001",
    name: "Firewall-001",
    type: 1,
    ip: "************",
    status: "online",
    cpuUsage: "23%",
    rulesCount: 120,
    securityLevel: "高"
  },
  {
    id: "fw-002",
    name: "Firewall-002",
    type: 1,
    ip: "************",
    status: "maintenance",
    cpuUsage: "15%",
    rulesCount: 80,
    securityLevel: "中"
  },
  {
    id: "fw-003",
    name: "Firewall-003",
    type: 1,
    ip: "********",
    status: "offline",
    cpuUsage: "N/A",
    rulesCount: 0,
    securityLevel: "低"
  },
  {
    id: "vm-101",
    name: "VM-Web-01",
    type: 7,
    ip: "*********",
    status: "online",
    cpu: "2 vCPU",
    memory: "4 GB",
    disk: "40 GB / 100 GB"
  },
  {
    id: "vm-102",
    name: "VM-DB-01",
    type: 7,
    ip: "*********",
    status: "online",
    cpu: "4 vCPU",
    memory: "8 GB",
    disk: "70 GB / 150 GB"
  },
  {
    id: "vm-103",
    name: "VM-App-01",
    type: 7,
    ip: "*********",
    status: "offline",
    cpu: "2 vCPU",
    memory: "4 GB",
    disk: "20 GB / 100 GB"
  },
  {
    id: "elb-201",
    name: "ELB-Frontend",
    type: 3,
    ip: "*********",
    status: "online",
    backendCount: 5,
    healthStatus: "健康",
    strategy: "轮询"
  },
  {
    id: "elb-202",
    name: "ELB-Backend",
    type: 3,
    ip: "*********",
    status: "online",
    backendCount: 3,
    healthStatus: "健康",
    strategy: "最少连接"
  },
  {
    id: "elb-203",
    name: "ELB-API",
    type: 3,
    ip: "*********",
    status: "maintenance",
    backendCount: 4,
    healthStatus: "部分异常",
    strategy: "源地址哈希"
  }
];
</script>

<style lang="scss" scoped>
.floating-panel {
  position: absolute;
  top: 40px;
  right: 120px;
  width: 200px;
  height: 200px;
  background: #fff;
  border: 1px solid #000;
  border-radius: 8px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-content {
  padding: 10px;
  overflow: auto;
  flex: 1;
}

.toggle-btn {
  align-self: flex-end;
  margin: 8px;
  z-index: 1;
}

.floating-panel {
  padding: 10px;
  position: absolute;
  top: 40px;
  right: 120px;
  z-index: 999;

  width: 250px;
  height: 350px;
  border: 1px solid #959595;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  display: flex;
  flex-direction: column;
  transition:
    width 0.3s ease,
    height 0.3s ease;
  overflow: hidden;
}

/* 收起状态 */
.floating-panel.collapsed {
  width: 40px;
  height: 40px;
}

/* 切换按钮 */
.toggle-btn {
  position: absolute;
  align-self: flex-end;
  // margin: 7px 7px 0 0;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 18px;
  line-height: 1;
  border: 1px solid #959595;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: transform 0.2s ease;
  z-index: 999;
}
.toggle-btn:hover {
  transform: scale(1.1);
}

/* 内容区域 */
.panel-content {
  flex: 1;
  padding: 10px;
  overflow: scroll;
  scrollbar-width: none;
}

/* 简单的淡入淡出过渡 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
