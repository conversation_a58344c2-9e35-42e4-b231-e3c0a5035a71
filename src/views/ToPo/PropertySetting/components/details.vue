<!-- 详情 -->
<template>
  <div>
    <el-descriptions :column="1" label-width="150" :size="size" border>
      <!-- <el-descriptions-item label="ID"> {{ nodeData.id }} </el-descriptions-item> -->
      <el-descriptions-item label="名称">
        <el-tooltip :content="`跳转至调用链分析`" placement="top">
          <!-- <span @click="toDetail(1)" class="service-name">{{ nodeData.relation }}</span> -->
          <span>{{ nodeData.name }}</span>
        </el-tooltip>
      </el-descriptions-item>
      <el-descriptions-item label="自定义名称">{{ nodeData.name }}</el-descriptions-item>
      <el-descriptions-item label="描述">{{ nodeData.remark }}</el-descriptions-item>
      <!-- <el-descriptions-item label="节点状态">{{
        nodeData.properties.frontend_status == 1 ? "连接正常" : "连接异常"
      }}</el-descriptions-item> -->
      <el-descriptions-item label="type">
        {{ getChineseTypeNameByNumber(nodeData.type) }}
      </el-descriptions-item>
      <el-descriptions-item label="网段"> {{ nodeData.networkSegment }} </el-descriptions-item>
      <el-descriptions-item label="ip">
        <div style="display: flex; justify-content: space-between">
          <span>
            {{ nodeData.ip }}
          </span>

          <span class="textSize">
            <span class="operate" @click="JumpTo(nodeData.ip, 1)">调用链分析</span>
            <span class="divider"> / </span>
            <span class="operate" @click="JumpTo(nodeData.ip, 2)">服务日志</span>
            <span class="divider"> / </span>
            <span class="operate" @click="JumpTo(nodeData.ip, 3)">主机列表</span>
          </span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="创建时间"> {{ nodeData.createTime }} </el-descriptions-item>
      <el-descriptions-item label="修改时间"> {{ nodeData.updateTime }} </el-descriptions-item>
      <el-descriptions-item v-if="nodeData.extNodeInfo" label="CPU核数">
        {{ nodeData.extNodeInfo.cpuPit }}
      </el-descriptions-item>
      <el-descriptions-item v-if="nodeData.extNodeInfo" label="内存（G）">
        {{ nodeData.extNodeInfo.memory }}
      </el-descriptions-item>
      <el-descriptions-item v-if="nodeData.extNodeInfo" label="操作系统">
        {{ nodeData.extNodeInfo.monitorSystem }}
      </el-descriptions-item>
      <el-descriptions-item v-if="nodeData.extNodeInfo" label="云主机私有IP地址">
        {{ nodeData.extNodeInfo.ip }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="修改时间">
        {{ nodeData.extNodeInfo.updateTime }}
      </el-descriptions-item>
      <el-descriptions-item label="修改时间">
        {{ nodeData.extNodeInfo.updateTime }}
      </el-descriptions-item> -->
    </el-descriptions>
    <div v-if="nodeData.extHardDiskList && nodeData.extHardDiskList.length > 0">
      <TitlecCom :title="`硬盘信息`" class="mt-10px"></TitlecCom>
      <el-descriptions
        v-for="(item, index) in nodeData.extHardDiskList"
        :key="index"
        :column="1"
        label-width="150"
        :size="size"
        border
        class="mb-10px"
      >
        <el-descriptions-item label="硬盘名称">{{ item.name }}</el-descriptions-item>
        <el-descriptions-item label="硬盘容量（GB）">{{ item.capacity }}</el-descriptions-item>
        <el-descriptions-item label="硬盘类型">{{ item.type }}</el-descriptions-item>
        <el-descriptions-item label="是否共享">{{
          item.shared === 1 ? "是" : "否"
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mt15" v-if="flowDetail.status != '2'">
      <el-button type="primary" @click="cancelFunc"> 确定 </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { getChineseTypeNameByNumber } from "../../utils/index";
import TitlecCom from "@/components/TitleCom/index.vue";
import { useSubMenuStore } from "@/store/modules/tagsView";
import { constantRoutes } from "@/router";
const subMenuStore = useSubMenuStore();
const menuList = ref(constantRoutes[0].children);
console.log("menuList ===>>>", menuList.value[0]);

const router = useRouter();
const props = defineProps({
  nodeData: Object,
  lf: Object || String,
  //详情
  flowDetail: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emit = defineEmits();
const toDetail = val => {
  if (val === 1) {
    router.push("/dashboard/CallChain");
  } else if (val === 2) {
    router.push("/dashboard/serviceList");
  }
};
let propertyForm = reactive({
  name: "",
  desc: "",
  assignList: []
});

//取消
const cancelFunc = () => {
  emit("closed");
};

// 跳转
const JumpTo = (val: string, typ: number) => {
  subMenuStore.addSubMenu(menuList.value[0].children);
  if (typ === 1) {
    router.push({
      path: "/dashboard/CallChain",
      query: {
        ip: val
      }
    });
  } else if (typ === 2) {
    router.push({
      path: "/dashboard/querylog",
      query: {
        ip: val
      }
    });
  } else if (typ === 3) {
    router.push({
      path: "/dashboard/hostList",
      query: {
        ip: val
      }
    });
  }
};

onMounted(() => {
  console.log("props.nodeData ===>>>", props.nodeData);
  console.log("props.flowDetail ===>>>", props.flowDetail.flowJson);
});
</script>
<style scoped lang="scss">
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.textSize {
  font-size: 12px;

  .operate {
    color: #0064c8;
    cursor: pointer;
  }
}
</style>
