<!-- 编辑 -->
<template>
  <div>
    <el-form
      ref="propertyFormRef"
      :model="propertyForm"
      :inline-message="true"
      :rules="rules"
      label-position="right"
      label-width="auto"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="propertyForm.name" clearable> </el-input>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="propertyForm.remark" type="textarea" :rows="2"> </el-input>
      </el-form-item>
      <el-form-item label="ip地址">
        <el-input v-model="propertyForm.ip" :rows="2"> </el-input>
      </el-form-item>
      <el-form-item label="网段" prop="networkSegment">
        <el-input v-model="propertyForm.networkSegment" :rows="2"> </el-input>
      </el-form-item>
      <el-form-item label="节点状态" prop="status">
        <el-select v-model="propertyForm.status" style="width: 200px">
          <el-option
            v-for="(item, index) in statusOption"
            :key="index"
            :value="item.value"
            :label="item.label"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="mt15">
      <el-button @click="cancelFunc"> 取消 </el-button>
      <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { networkView, topoView, editTopo, delTopo } from "@/api/topo/index";
const router = useRouter();
const props = defineProps({
  nodeData: Object,
  lf: Object || String,
  //详情
  flowDetail: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
let statusOption = ref([
  {
    value: 1,
    label: "正常"
  },
  {
    value: 2,
    label: "异常"
  },
  {
    value: 3,
    label: "断开"
  }
]);
const emit = defineEmits();
let propertyForm = reactive({
  name: "",
  remark: "",
  type: props.nodeData.type,
  id: props.nodeData.id,
  ip: "",
  networkSegment: "",
  status: ""
});
let rules = reactive({
  name: [
    { required: true, message: "名称不能为空" },
    {
      max: 50,
      message: "最大50字符"
    }
  ],
  networkSegment: [
    { required: true, message: "网段不能为空" },
    {
      max: 50,
      message: "最大50字符"
    }
  ],
  status: [{ required: true, message: "节点状态不能为空" }],
  remark: [
    {
      max: 50,
      message: "最大50字符"
    }
  ]
});
let propertyFormRef = ref(null);

//更新节点属性
const setProperties = () => {
  props.lf.setProperties(props.nodeData.id, {
    name: propertyForm.name,
    remark: propertyForm.remark,
    status: propertyForm.status,
    networkSegment: propertyForm.networkSegment,
    ip: propertyForm.ip
  });
};
const data = {
  firewall: 1,
  jwd: 2,
  cdn: 3,
  connector: 4,
  gateway: 5,
  rss: 6,
  serverIcon: 7,
  dns: 8
};
const getNameToNumberMapping = name => {
  const entry = Object.entries(data).find(([key, value]) => key === name);
  return entry ? entry[1] : null; // 如果找到了，返回对应的数字，否则返回 null
};

//确定
const confirmFunc = () => {
  propertyFormRef.value.validate(valid => {
    if (valid) {
      setProperties();
      props.lf.updateText(props.nodeData.id, propertyForm.name);
      emit("closed");
    }
  });
};

//取消
const cancelFunc = () => {
  props.lf.deleteNode(props.nodeData.id);
  emit("closed");
};

onMounted(() => {});
</script>
<style scoped>
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
