<template>
  <div class="app-container">
    <!-- <div class="floating-panel" v-show="isExpanded" @click.self.stop>
      <el-button class="toggle-btn" size="mini" @click.stop="toggle" circle> – </el-button>

      <transition name="fade">
        <div class="panel-content" v-if="isExpanded">
          <div v-for="dev in devices" :key="dev.id" class="dev-item" style="margin-bottom: 10px">
            <TitlecCom :title="dev.name"></TitlecCom>
            <el-descriptions :column="1" size="small" border style="margin-bottom: 10px">
              <el-descriptions-item label="ID">{{ dev.id }}</el-descriptions-item>
              <el-descriptions-item label="类型">
                {{ getChineseTypeNameByNumber(dev.type) }}
              </el-descriptions-item>
              <el-descriptions-item label="IP">{{ dev.ip }}</el-descriptions-item>
              <el-descriptions-item label="状态">{{ dev.status }}</el-descriptions-item>

              <template v-if="dev.type === 1">
                <el-descriptions-item label="CPU 利用率">{{ dev.cpuUsage }}</el-descriptions-item>
                <el-descriptions-item label="规则数">{{ dev.rulesCount }}</el-descriptions-item>
                <el-descriptions-item label="安全等级">{{
                  dev.securityLevel
                }}</el-descriptions-item>
              </template>

              <template v-else-if="dev.type === 7">
                <el-descriptions-item label="CPU">{{ dev.cpu }}</el-descriptions-item>
                <el-descriptions-item label="内存">{{ dev.memory }}</el-descriptions-item>
                <el-descriptions-item label="磁盘">{{ dev.disk }}</el-descriptions-item>
              </template>

              <template v-else-if="dev.type === 3">
                <el-descriptions-item label="后端实例数">{{
                  dev.backendCount
                }}</el-descriptions-item>
                <el-descriptions-item label="健康状态">{{ dev.healthStatus }}</el-descriptions-item>
                <el-descriptions-item label="调度策略">{{ dev.strategy }}</el-descriptions-item>
              </template>
            </el-descriptions>
          </div>
        </div>
      </transition>
    </div>
    <div class="floating-toggle" v-show="!isExpanded" @click="toggle">+</div> -->
    <!-- 顶部 -->
    <!-- <popup :lf="lf" v-show="isExpanded" @toggle="toggle" /> -->
    <div class="floating-toggle" v-show="!isExpanded" @click="toggle">+</div>
    <div class="mb-10px top-container">
      <el-button v-if="showLf" @click="forTemplate">{{ "使用模板" }}</el-button>
      <el-button v-if="showLf" type="primary" @click="saveVisible = true">发布</el-button>
      <!-- <span
        v-if="!showLf"
        :style="{
          color: apdexColor,
          fontSize: '18px'
        }"
      >
        <span
          class="color-container"
          :style="{
            background: apdexColor
          }"
        ></span>
        Apdex：{{ apdex }}
      </span> -->
    </div>
    <div class="logic-flow-view" v-loading="!topoLoading">
      <!-- 工具栏 -->
      <Control v-if="topoLoading" class="demo-control" :lf="lf"></Control>
      <!-- 左侧面板 -->
      <NodePanel v-if="showLf" :lf="lf"></NodePanel>
      <Status v-if="!showLf && !topoNull" :lf="lf"></Status>
      <span v-if="!showLf && topoNull">未查询到拓扑图数据</span>
      <!-- 画布 -->
      <div id="container" style="width: 100%; height: 100%"></div>
      <!-- 操作面板 -->
      <PropertyDialog
        v-if="showAttribute"
        :title="title"
        :nodeData="nodeData"
        :flowDetail="flowDetail"
        :lf="lf"
        :operate="operate"
        @closed="showAttribute = false"
      >
      </PropertyDialog>
    </div>
    <el-dialog
      :align-center="true"
      v-model="saveVisible"
      title="发布"
      width="500"
      :close-on-click-modal="false"
    >
      <span>确认要发布拓扑设置吗？</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCommit" :loading="saveLoading"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- <el-dialog
      :align-center="true"
      v-model="showVisible"
      title="切换查看模式"
      width="500"
      :close-on-click-modal="false"
    >
      <span>请确认编辑内容已发布</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showVisible = false">取消</el-button>
          <el-button type="primary" @click="showCommit"> 确定 </el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import LogicFlow from "@logicflow/core";
import {
  Menu,
  Snapshot,
  MiniMap,
  SelectionSelect,
  DynamicGroup,
  NodeSelection,
  Label
} from "@logicflow/extension";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import registerBezier from "./registerEdge/registerBezier.js";
import registerLine from "./registerEdge/registerLine.js";
import registerPolyline from "./registerEdge/registerPolyline.js";
import myCustomGroup from "./registerDynamicGroup/dynamic.js";
import customNode from "./registerDynamicGroup/baseNode.js";
// import dynamic from "./registerNode/dynamic/dynamic.ts";
import registerNodes from "./registerNode/components/registerNodes.js";
import myData from "./utils/template.json";
import PropertyDialog from "./PropertySetting/PropertyDialog.vue";
import popup from "./LFComponents/popup.vue";
import NodePanel from "./LFComponents/NodePanel.vue";
import Control from "./LFComponents/Control.vue";
import Status from "./LFComponents/Status.vue";
import { networkView, topoView, detailTopo, saveTopo } from "@/api/topo/index";
import {
  coordinateData,
  processEntireData,
  flattenTree,
  gtEdgeFlowJson,
  getChineseTypeNameByNumber
} from "./utils/index";
import { BASE_CONFIG, SILENT_CONFIG, configuration } from "./components/flowConfig";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
import { useRoute } from "vue-router";
const route = useRoute();
const isExpanded = ref(true);
const props = defineProps({
  title: {
    type: String,
    default: ""
  }
});

const saveVisible = ref(false);
let lf = reactive({});
let showAddPanel = ref(false);
let addPanelStyle = reactive({
  top: 0,
  left: 0
});
const apdex = ref(Math.random().toFixed(2));
let nodeData = ref(null);
let addClickNode = ref(null);
let showAttribute = ref(false);
let operate = ref("edit"); // 操作类型

let flowDetail = reactive({});
let showLf = ref(false);
let topoLoading = ref(false);
const topoNull = ref(false);

const $_initLf = () => {
  // 画布配置
  const myLf = new LogicFlow({
    // 基础配置
    ...BASE_CONFIG,
    // 静默模式配置
    // ...(showLf.value ? {} : SILENT_CONFIG),
    isSilentMode: !showLf.value,
    // 插件系统配置
    plugins: [Menu, MiniMap, Snapshot, SelectionSelect, DynamicGroup, NodeSelection],
    // 容器绑定
    container: document.querySelector("#container")
    // container: container.value,
  });

  lf = myLf;
  // console.log(lf.graphModel);
  const { transformModel } = lf.graphModel;
  // 缩放
  transformModel.zoom(0.4);
  // 设置主题
  lf.focusOn({
    coordinate: {
      x: 0,
      y: 1000
    }
  });
  lf.setTheme(configuration);
  lf.setDefaultEdgeType("myBezier");
  $_registerNode();
  topoLoading.value = true;
};

// 自定义
const $_registerNode = () => {
  registerNodes(lf);
  lf.register(customNode);
  lf.register(myCustomGroup);
  lf.register(registerBezier);
  lf.register(registerPolyline);
  lf.register(registerLine);
  $_render();
};

const $_render = () => {
  let flowJson;

  if (getOut.value === "template") {
    flowJson = myData;
  } else {
    flowJson = {
      nodes: topoNodesData.value,
      // nodes: generateFlowJson(topoNodesData.value),
      edges: topoEdgesData.value
    };
  }
  // flowJson = processData(topoNodesData.value);
  console.log("flowJson ===>>>", flowJson);

  flowDetail = {
    flowName: "测试",
    flowId: "1",
    flowJson: flowJson
  };

  lf.render(flowJson); // 使用 LogicFlow 渲染流程图，传入 flowJson 数据
  $_LfEvent(); // 执行自定义的 LogicFlow 事件处理函数
};

const $_LfEvent = () => {
  lf.on("node:dbclick", ({ data }) => {
    if (data.type === "myCustomGroup") {
      return;
    }
    getDetail(data.id);
    operate.value = "detail";
  });
  // lf.on("edge:dbclick", ({ data }) => {
  //   // operate.value = "edgeEdit";
  //   nodeData.value = data;
  //   showAttribute.value = true;
  // });
  lf.on("node:dnd-add", ({ data }) => {
    if (data.type === "myCustomGroup") {
      return;
    } else if (data.type === "text") {
      operate.value = "addText";
    } else {
      operate.value = "add";
    }
    nodeData.value = data;
    showAttribute.value = true;
  });
  //来自边的事件中心发出的事件
  lf.on("edge:app-config", res => {
    operate.value = "edge";
    nodeData.value = res;
    showAttribute.value = true;
  });
  lf.on("element:click", ({ data }) => {
    hideAddPanel();
  });
  lf.on("blank:click", () => {
    hideAddPanel();
  });
  lf.on("connection:not-allowed", data => {
    if (data.msg) {
      ElMessage({
        type: "error",
        message: data.msg,
        duration: 8000
      });
    }
  });

  lf.on("edge:add", ({ data }) => {
    setjwdStatus(data);
  });

  // lf.on("edge:delete", ({ data }) => {
  //   setserverStatus(data);
  //   setjwdStatus(data);
  // });
  lf.on("user:open-edit-dialog", node => {
    console.log("user:open-edit-dialog", node);
    nodeData.value = node;
    showAttribute.value = true;
    operate.value = "edit";
  });
  lf.on("user:open-details-dialog", node => {
    getDetail(node.id);
    operate.value = "detail";
  });
};

const hideAddPanel = () => {
  showAddPanel.value = false;
  addPanelStyle.top = 0;
  addPanelStyle.left = 0;
  addClickNode.value = null;
};

//设置并行结束节点状态
const setjwdStatus = data => {
  let graphData = lf.getGraphData();
  const edgeModel = lf.getEdgeModelById(data.id);
  graphData.nodes.forEach(i => {
    if (i.id == data.targetNodeId) {
      edgeModel.setProperties({
        ...i.properties
      });
    }
  });
};
const saveLoading = ref(false);

const saveCommit = async () => {
  saveLoading.value = true;
  // 获取当前图形的原始数据
  const graphData = lf.getGraphRawData();
  console.log("当前图形数据:", graphData);
  const jsonText = JSON.stringify(graphData);
  // console.log(jsonText);

  const params = {
    id: TopoViewId.value,
    appid: useApplicationStore.appId,
    jsonText: jsonText
  };
  console.log(params);

  try {
    const res = await saveTopo(params);
    if (res.code === 0) {
      saveLoading.value = false;
      saveVisible.value = false;
    }
  } catch (err) {
    saveLoading.value = false;
    console.error("发布过程中出现错误：", err);
  }
};

const topoNodesData = ref();
const topoEdgesData = ref();

// 查询节点详情
const getDetail = async type => {
  try {
    const res = await detailTopo(type);
    if (res.code === 0) {
      nodeData.value = res.entity;

      showAttribute.value = true;
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

const filteredOutData = ref();
const TopoViewId = ref();

const getTopoView = async () => {
  try {
    const res = await topoView(useApplicationStore.appId);
    if (res.code === 0 && res.entity) {
      // getNetwork();
      // console.log(res.entity.jsonText);

      TopoViewId.value = res.entity.id;
      const topoView = JSON.parse(res.entity.jsonText);
      // console.log(topoView.edges);
      topoNodesData.value = topoView.nodes;
      topoEdgesData.value = topoView.edges.map(item => {
        return {
          ...item,
          // text: `192.168.1.${Math.floor(Math.random() * 256)}`
          text: `响应时长：${Math.floor(Math.random() * 256)}ms`
        };
      });

      $_initLf();
    } else {
      getNetwork();
    }

    // if (route.path === "/system/topology" || route.name === "topology") {
    //   showLf.value = true;
    // }
  } catch (error) {
    topoNull.value = true;
    $_initLf();
    console.error("Error logs:", error);
  }
};

// 最终业务逻辑调用
const getNetwork = async () => {
  try {
    const res = await networkView(useApplicationStore.appId);
    if (res.code === 0 && res.entity.topoGroupList) {
      //  原始数据
      const rawData = res.entity.topoGroupList;

      //  处理顺序
      const order = ["防火墙/负载均衡", "NG/NGINX/LVS", "实例", "数据存储", "运维安全管理区"];
      const orderMap = new Map(order.map((id, index) => [id, index]));
      const orderedChildren = [];
      const otherChildren = [];
      rawData[0].children.forEach(child => {
        if (orderMap.has(child.name)) {
          orderedChildren.push(child);
        } else {
          otherChildren.push(child);
        }
      });

      rawData[0].children = orderedChildren.sort(
        (a, b) => orderMap.get(a.name) - orderMap.get(b.name)
      );
      // console.log("处理后数据 ===>>>", rawData);

      //  处理宽高
      const sizedData = rawData.map((node: any) => processEntireData(node));
      // console.log("处理尺寸后数据 ===>>>", sizedData[0]);

      //  计算坐标
      const positionedData = sizedData.map(node => coordinateData(node));
      // console.log("坐标处理后数据 ===>>>", positionedData[0]);

      //  处理数组为node节点数据
      const LogicFlowData = flattenTree(positionedData);
      // console.log("处理尺寸后数据 ===>>>", LogicFlowData);
      const filteredData = LogicFlowData.filter(
        item => item.properties.name === "运维安全管理区" || !order.includes(item.properties.name)
      );
      // 过滤掉的数据
      filteredOutData.value = Array.isArray(LogicFlowData)
        ? LogicFlowData.filter(item => !filteredData.includes(item))
        : [];

      const flattenedChildrenData = Array.isArray(filteredOutData.value)
        ? filteredOutData.value.flatMap((item: { children?: any[] }) =>
            Array.isArray(item.children) ? item.children : []
          )
        : [];
      const firstFilteredData = filteredData.find(item => item.properties.parentId == 0);
      if (firstFilteredData) {
        firstFilteredData.children = firstFilteredData.children || [];
        firstFilteredData.properties = firstFilteredData.properties || {};
        firstFilteredData.properties.children = firstFilteredData.properties.children || [];

        firstFilteredData.children.push(...flattenedChildrenData);
        firstFilteredData.properties.children.push(...flattenedChildrenData);
      }
      topoNodesData.value = filteredData;
      topoEdgesData.value = gtEdgeFlowJson(res.entity.extTopoEdgeList);
    } else {
      topoNull.value = true;
    }
    $_initLf();
  } catch (error) {
    $_initLf();
    console.error("Error logs:", error);
  }
};

const getOut = ref("");
const forTemplate = () => {
  getOut.value = "template";
  $_initLf();
};

const apdexColor = computed(() => {
  const value = parseFloat(apdex.value);
  // Apdex标准颜色分段（0-0.3红，0.3-0.7黄，0.7-1绿）
  if (value < 0.3) return "#f56c6c"; // 红色
  if (value < 0.7) return "#ffa940"; // 黄色
  return "#78bf75"; // 绿色
});

//  悬浮窗点击出现/隐藏
const toggle = () => {
  isExpanded.value = !isExpanded.value;
};

let intervalId: number | null = null;

onMounted(() => {
  if (route.path === "/system/topology" || route.name === "editTopology") {
    showLf.value = true;
  }
  getTopoView();
});
watch(
  () => route.fullPath, // 或 route.name/route.path，视实际需求
  newRoute => {
    if (newRoute.path === "/system/topology" || newRoute.name === "editTopology") {
      showLf.value = true;
      if (!intervalId) {
        startInterval();
      }
    } else {
      showLf.value = false;
      stopInterval();
    }
  }
);
function startInterval() {
  stopInterval(); // 保险起见先清理一次
  intervalId = setInterval(() => {
    console.log(123456798);
    saveCommit();
  }, 600000);
}

function stopInterval() {
  if (intervalId !== null) {
    clearInterval(intervalId);
    intervalId = null;
  }
}
onUnmounted(() => {
  stopInterval();
});
</script>
<style lang="scss" scoped>
@import "./styles/variables.module.scss";

.logic-flow-view {
  height: 99%;
  position: relative;
}

.demo-title {
  text-align: center;
  margin: 20px;
}

.demo-control {
  position: absolute;
  top: 15px;
  right: 100px;
  z-index: 2;
}
.app-container {
  height: 99%;
  width: 100%;
  outline: none;
  overflow-y: hidden;
  overflow-x: hidden;
  position: relative;
}

.time-plus {
  cursor: pointer;
}

.add-panel {
  position: absolute;
  z-index: 11;
  background-color: white;
  padding: 10px 5px;
}

.el-drawer__body {
  height: 80%;
  overflow: auto;
  margin-top: -30px;
  z-index: 3;
}

.lf-node-text-auto-wrap {
  cursor: pointer;
}

/* 适应节点图标 */
.lf-node-text-ellipsis-content {
  padding: 0 8px 0 34px !important;
}

.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-name > span {
  border: none !important;
}

//logicflow小地图
.lf-mini-map {
  border-radius: $appRadius;
  border: none !important;
  box-shadow: 3px 0 10px 1px rgb(228, 224, 219);
}

.lf-mini-map-header {
  border: none !important;
  font-size: 13px;
  height: 24px !important;
  line-height: 24px !important;
  // color: #fff;
  background-color: #ecf5ff !important;
  background-image: none !important;
}

.lf-mini-map-close {
  top: 2px !important;
}

@keyframes lf_animate_dash {
  to {
    stroke-dashoffset: 0;
  }
}

.lf-node-default .lf-node .lf-node-content {
  // width: 2000px !important;
  padding: 200px !important;
}
.top-container {
  display: flex;
  justify-content: end;
  padding-right: 10px;
}
.color-container {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-bottom: 1px;
  border-radius: 50%;
}
.floating-toggle {
  position: absolute;
  top: 40px;
  right: 120px;
  width: 30px;
  height: 30px;
  border: 1px solid #959595;
  border-radius: 8px;
  background: #fff;
  font-size: 24px;
  font-weight: bold;
  color: #959595;
  text-align: center;
  line-height: 25px;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.2s ease;
  user-select: none;
  .floating-toggle:hover {
    transform: scale(1.1);
  }
}
</style>
