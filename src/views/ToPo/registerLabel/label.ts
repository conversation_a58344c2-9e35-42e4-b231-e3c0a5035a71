import { LabelNode, LabelNodeModel } from "@logicflow/core";

// 自定义Label视图
class LabelView extends LabelNode {
  // 自定义渲染函数
  getShape() {
    const { model, graphModel } = this.props;
    const { x, y } = model;
    
    return (
      <g className="lf-label">
        {/* 添加背景矩形 */}
        <rect
          x={x - model.width / 2}
          y={y - model.height / 2}
          width={model.width}
          height={model.height}
          fill="#f5f5f5"
          stroke="#AEAFAE"
          strokeDasharray="3 3"
          rx="4"
          ry="4"
        />
        {/* 调用原有文本渲染 */}
        {super.getShape()}
      </g>
    );
  }

  // 自定义编辑样式
  startEdit() {
    super.startEdit();
    const input = document.querySelector('.lf-input');
    if (input) {
      input.style.border = '2px solid #1890ff';
      input.style.borderRadius = '4px';
    }
  }
}

// 自定义Label模型
class LabelModel extends LabelNodeModel {
  // 初始化配置
  initNodeData(data) {
    super.initNodeData(data);
    this.width = 120;
    this.height = 30;
    this.text = {
      value: "New Label",
      editable: true,
      style: {
        fontSize: 14,
        fill: "#666",
      }
    };
  }

  // 自动调整位置（示例：始终居中）
  getTextPosition() {
    const { x, y } = this;
    return {
      x: x,
      y: y,
      // 可以添加更复杂的定位逻辑
    };
  }

  // 自定义样式获取
  getNodeStyle() {
    const style = super.getNodeStyle();
    return {
      ...style,
      className: 'label-style'
    };
  }
}

export default {
  type: "myLabel",
  view: LabelView,
  model: LabelModel,
  config: {
    editable: true,
    moveable: true,
    autoSize: true,
    adjustPosition: true
  }
};
