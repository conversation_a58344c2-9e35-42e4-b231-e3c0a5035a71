import { ElMessage } from "element-plus";

// 生成19位唯一数字标识符
export const randomNumber = () => {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 9000000000000000) + 1000000000000000;
  let identifier = timestamp + random.toString();
  if (identifier.length > 19) {
    identifier = identifier.substr(0, 19);
  }
  if (identifier.charAt(0) === "0") {
    identifier = "1" + identifier.substr(1);
  }
  return identifier;
};

//logicflow-获取节点输出几条线。graph是json数组，node是节点数据
export const getNodeOutputCount = (graph, nodeData) => {
  // 找到所有节点
  const nodes = graph.nodes;
  // 找到所有边
  const edges = graph.edges;
  // 初始化节点输出线条数量
  let count = 0;
  // 遍历所有节点
  for (const node of nodes) {
    // 判断节点类型是否为
    if (node.id === nodeData.id) {
      // 遍历所有边，查找与该节点相关的边
      for (const edge of edges) {
        if (edge.sourceNodeId === node.id) {
          // 如果找到了与该节点相连的边，增加计数
          count++;
        }
      }
    }
  }
  return count; // 节点输出线条数量
};

//logicflow-获取节点输入几条线。graph是json数组，node是节点数据
export const getNodeInputCount = (graph, nodeData) => {
  // 找到所有节点
  const nodes = graph.nodes;
  // 找到所有边
  const edges = graph.edges;
  // 初始化节点输出线条数量
  let count = 0;
  // 遍历所有节点
  for (const node of nodes) {
    // 判断节点类型是否为
    if (node.id === nodeData.id) {
      // 遍历所有边，查找与该节点相关的边
      for (const edge of edges) {
        if (edge.targetNodeId === node.id) {
          // 如果找到了与该节点相连的边，增加计数
          count++;
        }
      }
    }
  }
  return count; // 节点输出线条数量
};

//复制
export const copyFunc = data => {
  if (data) {
    const oInput = document.createElement("input");
    oInput.value = data;
    document.body.appendChild(oInput);
    oInput.select();
    ElMessage.success("复制成功");
    document.execCommand("Copy");
    document.body.removeChild(oInput);
  }
};

// 根据数组将value变成中文;
export const getLabelByValue = (value, arr, typeValue, typeLabel) => {
  let label = "";
  arr.forEach(i => {
    if (i[typeValue] == value) {
      label = i[typeLabel];
    }
  });
  return label;
};
export const getNameToNumberMapping = val => {
  const data = {
    WEBfw: 1,
    jwd: 2,
    cdn: 3,
    gateway: 4,
    connector: 5,
    rss: 6,
    serverIcon: 7,
    dns: 8,
    fort: 9,
    log: 10,
    database: 11,
    loophole: 12,
    Webpage: 13,
    machine: 14,
    pw: 15
  };
  const entry = Object.entries(data).find(([key, value]) => key === val);
  return entry ? entry[1] : null;
};

export const getChineseTypeNameByNumber = val => {
  const typeMap = {
    1: "防火墙", // 防火墙
    2: "NAT网关", // NAT网关
    3: "弹性负载均衡", // 弹性负载均衡
    4: "网关", // 网关
    5: "系统端口", // 系统端口
    6: "虚拟IP", // 虚拟IP
    7: "虚拟机", // 虚拟机
    8: "DHCP服务", // DHCP服务
    9: "堡垒机", // WEB防火墙
    10: "日志审计", // 日志审计
    11: "数据库审计", // 数据库审计
    12: "漏洞扫描", // 漏洞扫描
    13: "网页防篡改服务", // 网页防篡改
    14: "主机安全", // 主机安全
    15: "密码服务" // 密码服务
  };
  return typeMap[val];
};

export const getEnglishTypeNameByNumber = val => {
  const typeMap = {
    1: "firewall", // 防火墙
    2: "jwd", // NAT网关
    3: "cdn", // 弹性负载均衡
    4: "gateway", // 网关
    5: "connector", // 系统端口
    6: "rss", // 虚拟IP
    7: "serverIcon", // 虚拟机
    8: "dns", // DHCP服务
    9: "fort", // 堡垒机
    10: "log", // 日志审计
    11: "database", // 数据库审计
    12: "loophole", // 漏洞扫描
    13: "Webpage", // 网页防篡改
    14: "machine", // 主机安全
    15: "pw" // 密码服务
  };
  return typeMap[val];
};

export function isValidUuid(uuid) {
  const uuidPattern =
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  return uuidPattern.test(uuid);
}

// 计算节点坐标
// function coordinateData(data, parentX = 0, parentY = 0, parentWidth = 0, parentHeight = 0) {
//   // 若未传入父级尺寸，则用当前节点自身的宽高
//   const NodeWidth = parentWidth || data.width;
//   const NodeHeight = parentHeight || data.height;

//   // 根据父节点的中心计算出父节点左上角坐标
//   // （这里沿用了你原来的逻辑：x 减半宽，y 加半高）
//   const NodeX = parentX - NodeWidth / 2;
//   const NodeY = parentY + NodeHeight / 2;

//   // 计算父节点的中心（应等于传入的 parentX, parentY）
//   const centerX = NodeX + data.width / 2;
//   const centerY = NodeY - data.height / 2;
//   // console.log("centerX", centerX);

//   // 保存当前节点的坐标
//   data.x = NodeX;
//   data.y = NodeY;

//   // 如果有子节点，则进行排列
//   if (data.children && data.children.length > 0) {
//     const verticalSpacing = 200; // 垂直间距（可根据需要调整）

//     if (data.children.length === 1) {
//       // 仅有一个子节点时，子节点的中心直接对齐父节点中心下方
//       const child = data.children[0];
//       coordinateData(child, centerX, centerY + verticalSpacing, child.width, child.height);
//     } else if (data.children.length === 2) {
//       // 两个子节点时，利用各自宽度计算左右两个子节点的中心坐标
//       const child1 = data.children[0];
//       const child2 = data.children[1];

//       // 第一个子节点的中心坐标：往左偏移 (child1.width/2 + child2.width/2)
//       const child1CenterX = NodeX + child1.width / 2 + 200;
//       // console.log(child1CenterX);
//       // 第二个子节点的中心坐标：往右偏移同样的距离
//       const child2CenterX = centerX + (child1.width / 2 + child2.width / 2);
//       const childCenterY = centerY + verticalSpacing;

//       coordinateData(child1, child1CenterX, childCenterY, child1.width, child1.height);
//       coordinateData(child2, child2CenterX, childCenterY, child2.width, child2.height);
//     } else {
//       // 多个子节点时，先计算所有子节点总宽度（含间距），使得整体居中
//       const spacing = 20; // 子节点之间的水平间距
//       const totalChildrenWidth =
//         data.children.reduce((sum, child) => sum + child.width, 0) +
//         spacing * (data.children.length - 1);
//       // 第一个子节点的起始 x 坐标（使整个子节点区域居中于父节点中心）
//       let startX = centerX - totalChildrenWidth / 2;
//       const childCenterY = centerY + verticalSpacing;

//       data.children.forEach(child => {
//         // 每个子节点的中心坐标 = 当前起始位置 + 子节点宽度的一半
//         const childCenterX = startX + child.width / 2;
//         coordinateData(child, childCenterX, childCenterY, child.width, child.height);
//         startX += child.width + spacing;
//       });
//     }
//   }

//   // 返回合并后的节点数据
//   return {
//     ...data,
//     x: NodeX,
//     y: NodeY
//   };
// }
// function coordinateData(
//   data,
//   parentX = 0,
//   parentY = 0,
//   levelGap = 300,
//   siblingGap = 50,
//   level = 1
// ) {
//   let NodeX = parentX;
//   let NodeY = parentY;

//   let nodeWidth = data.width || 400;
//   let nodeHeight = data.height || 100;

//   if (data.children && data.children.length > 0) {
//     let totalWidth =
//       data.children.reduce((sum, child) => sum + (child.width || 400), 0) +
//       (data.children.length - 1) * siblingGap;

//     let startX = parentX - totalWidth / 2 + nodeWidth / 2;

//     let maxHeight = Math.max(...data.children.map(child => child.height || 100));
//     // let maxWidth = Math.max(...data.children.map(child => child.width || 100));
//     // console.log("maxWidth ===>>>", maxWidth);

//     if (level > 2) {
//       startX = parentX + siblingGap * 2;
//       // startX = parentX + maxWidth / 2;
//     }
//     data.children.forEach((item, index) => {
//       let childX = startX + index * (item.width || 400) + (index > 0 ? siblingGap : 0);
//       let childY = NodeY + levelGap;

//       if (level === 2) {
//         childY += (maxHeight - 50 - nodeHeight) / 2;
//       } else if (level > 2) {
//         childY = childY + 30 - nodeHeight / 2 - 200 + index * 110;

//         childX =
//           startX +
//           (item.width === 400 ? 0 : item.width === 600 ? 100 : item.width === 450 ? 20 : 0);
//       }

//       if (item.nodeList && item.nodeList.length > 0) {
//         item.nodeList.forEach((node, nodeIndex) => {
//           if (item.nodeList.length === 1) {
//             node.x = childX;
//             node.y = childY;
//           } else if (item.nodeList.length === 2) {
//             if (nodeIndex === 0) {
//               node.x = childX - siblingGap - 20;
//             } else {
//               node.x = childX + siblingGap;
//             }
//             node.y = childY;
//           } else if (item.nodeList.length === 3) {
//             node.x = childX + (nodeIndex - 1) * 150;
//             node.y = childY;
//           } else if (item.nodeList.length === 4) {
//             node.x = childX + (nodeIndex - 1) * 150 - 70;
//             node.y = childY;
//           }
//         });
//       }

//       data.children[index] = coordinateData(item, childX, childY, levelGap, siblingGap, level + 1);
//     });
//   }

//   return {
//     ...data,
//     x: NodeX,
//     y: NodeY
//   };
// }
// 计算坐标
export function coordinateData(
  data,
  parentX = 0,
  parentY = 1000,
  levelGap = 300,
  siblingGap = 50,
  level = 1
) {
  const nodeWidth = data.width || 400;
  const nodeHeight = data.height || 100;

  // 创建当前节点的副本，并初始化坐标
  const nodeCopy = {
    ...data,
    x: parentX,
    y: parentY
  };

  if (data.children && Array.isArray(data.children) && data.children.length > 0) {
    // 计算所有子节点的总宽度
    const totalChildrenWidth =
      data.children.reduce((sum, child) => sum + (child.width || 400), 0) +
      (data.children.length - 1) * siblingGap;

    // 计算子节点的起始 X 坐标，使子节点整体相对于父节点水平居中
    const startX = parentX - totalChildrenWidth / 2 + nodeWidth / 2;
    // 子节点的起始 Y 坐标，位于父节点下方
    const startY = parentY + levelGap;

    // 遍历每个子节点并递归计算其坐标
    if (level === 1) {
      nodeCopy.children = data.children.map((child, index) => {
        // 计算子节点的 X 坐标
        let childX = startX + 200 + nodeWidth / 2 + 420;
        // console.log("nodeHeight ===>>>", nodeHeight);
        // console.log("child.height ===>>>", child.height);
        let childY;
        // 计算子节点的 Y 坐标，可以根据需要调整层级间距
        if (index === 0) {
          // childY = (startY - child.height) * (index + 1) + child.height;
          childY = 130;
        } else if (index === 1) {
          childY = 520;
        } else if (index === 2) {
          childY = 1120;
        } else if (index === 3) {
          childY = 1720;
        } else if (index === 4) {
          childX = 800;
        }
        if (child.nodeList && child.nodeList.length > 0) {
          if (index === 4) {
            child.nodeList.forEach((node, nodeIndex) => {
              node.x = childX;
              node.y = 300 + nodeIndex * 200;
            });
          }
          child.nodeList.forEach((node, nodeIndex) => {
            if (child.nodeList.length === 1) {
              node.x = childX;
              node.y = childY;
            } else if (child.nodeList.length === 2) {
              if (nodeIndex === 0) {
                node.x = childX - siblingGap - 20;
              } else {
                node.x = childX + siblingGap;
              }
              node.y = childY;
            } else if (child.nodeList.length === 3) {
              node.x = childX + (nodeIndex - 1) * 150;
              node.y = childY;
            } else if (child.nodeList.length === 4) {
              node.x = childX + (nodeIndex - 1) * 150 - 70;
              node.y = childY;
            }
          });
        }
        return coordinateData(child, childX, childY, levelGap, siblingGap, level + 1);
      });
    } else if (level === 2) {
      nodeCopy.children = data.children.map((child, index) => {
        const randomNumber = Math.floor(Math.random() * 30) + 1;
        let childX;
        let childY = parentY - data.height / 2 + 200 + randomNumber;
        if (data.children.length >= 4) {
          childX = parentX - 700 + index * 500 + 200;
          if (index >= 3) {
            childX = parentX - 2200 + index * 500 + 200;
            childY = childY + 250 + randomNumber;
          }
        } else {
          childX = parentX - 600 + index * 500 + 200;
        }
        child.nodeList.forEach((node, nodeIndex) => {
          if (child.nodeList.length === 1) {
            node.x = childX;
            node.y = childY;
          } else if (child.nodeList.length === 2) {
            if (nodeIndex === 0) {
              node.x = childX - siblingGap - 20;
            } else {
              node.x = childX + siblingGap;
            }
            node.y = childY;
          } else if (child.nodeList.length === 3) {
            node.x = childX + (nodeIndex - 1) * 150;
            node.y = childY;
          } else if (child.nodeList.length === 4) {
            node.x = childX + (nodeIndex - 1) * 150 - 70;
            node.y = childY;
          }
        });
        return coordinateData(child, childX, childY, levelGap, siblingGap, level + 1);
      });
    }
  }

  return nodeCopy;
}
// 计算节点分组宽高
export function processEntireData(data) {
  let width = 0;
  let height = 0;

  const nodeCount = data.nodeList ? Math.min(data.nodeList.length, 4) : 0;
  if (data.parentId == 0) {
    width = 2000;
    height = 2000;
    data.children.forEach(child => processEntireData(child));
    // width = data.children.reduce((acc, child) => acc + processEntireData(child).width, 0) + 200;
    // height = data.children.length >= 4 ? data.children.length * 500 : 200;
  } else if (data.name === "运维安全管理区") {
    width = 300;
    height = data.nodeList.length ? data.nodeList.length * 200 : 500;
    // width = data.children.reduce((acc, child) => acc + processEntireData(child).width, 0) + 200;
    // height = data.children.length >= 4 ? data.children.length * 500 : 200;
  } else {
    if (data.children && data.children.length > 0) {
      //   // width = data.children.reduce((acc, child) => acc + processEntireData(child).width, 0) + 200;
      // width = nodeCount * 150;
      data.children.forEach(child => processEntireData(child));
      width = Math.max(width, 1500);
      height = data.children.length >= 4 ? data.children.length * 110 : 200;
      height = Math.max(height, 500);
    } else {
      width = nodeCount * 150;
      width = Math.max(width, 200);
      height = 200;
    }
  }
  // if (data.children && data.children.length > 0) {
  //   data.children.forEach(child => processEntireData(child));
  //   // width = data.children.reduce((acc, child) => acc + processEntireData(child).width, 0) + 200;
  //   // height = data.children.length >= 4 ? data.children.length * 110 : data.children.length * 150;
  //   height = data.children.length >= 4 ? data.children.length * 110 : 200;

  //   // height =
  //   //   data.children.reduce((maxHeight, child) => {
  //   //     const childHeight = processEntireData(child).height;
  //   //     return Math.max(maxHeight, childHeight);
  //   //   }, 0) + 200;
  // } else {
  //   width = nodeCount * 150;
  //   width = Math.max(width, 200);
  //   height = 200;
  // }

  data.width = width;
  data.height = height;
  return {
    ...data,
    width: width,
    height: height
  };
}

// 处理接收数据，宽高，坐标，平铺分组
// function generateFlowJson(
//   data,
//   parentInfo = { depth: 0, parentId: null },
//   result = [],
//   layout = { depths: {} }
// ) {
//   // 基础布局参数
//   const BASE_GROUP_WIDTH = 400;
//   const NODE_WIDTH = 80;
//   const NODE_HEIGHT = 80;
//   const COLUMN_SPACING = 60;
//   const ROW_SPACING = 50; // 减少行间距
//   const MARGIN_X = 30;
//   const MARGIN_Y = 30; // 统一边距
//   const VERTICAL_SPACING = 60;

//   data.forEach((item, index) => {
//     const currentDepth = parentInfo.depth;
//     if (item.extJson) {
//       try {
//         JSON.parse(item.extJson);
//         item.extJson = JSON.parse(item.extJson);
//       } catch (error) {
//         item.extJson = item.extJson;
//       }
//     }
//     // 初始化层级布局
//     if (!layout.depths[currentDepth]) {
//       const prevDepth = currentDepth - 1;
//       const prevHeight =
//         prevDepth >= 0 ? layout.depths[prevDepth].totalHeight + VERTICAL_SPACING : 0;

//       layout.depths[currentDepth] = {
//         currentX: BASE_GROUP_WIDTH / 2,
//         baseY:
//           prevDepth >= 0
//             ? layout.depths[prevDepth].baseY +
//               layout.depths[prevDepth].totalHeight +
//               VERTICAL_SPACING
//             : 0,
//         totalHeight: 0,
//         groupCount: 0
//       };
//     }

//     // 计算动态高度
//     let groupHeight = 150; // 默认高度
//     if (item.nodeList?.length) {
//       const nodeCount = Math.min(item.nodeList.length, 8);
//       const rows = Math.ceil(nodeCount / 2);

//       // 精确计算内容高度
//       groupHeight = rows * NODE_HEIGHT + (rows - 1) * ROW_SPACING + MARGIN_Y * 2; // 上下边距

//       // 设置合理的最小高度
//       groupHeight = Math.max(groupHeight, rows < 3 ? 200 : 300);
//     }

//     // 更新层级最大高度
//     layout.depths[currentDepth].totalHeight = Math.max(
//       layout.depths[currentDepth].totalHeight,
//       groupHeight
//     );

//     // 计算分组坐标
//     const groupX = layout.depths[currentDepth].currentX;
//     const groupY = layout.depths[currentDepth].baseY + groupHeight / 2;

//     // 创建分组节点
//     const groupId = `${item.id}`;
//     const groupNode = {
//       id: groupId,
//       type: "myCustomGroup",
//       x: item.extJson?.x ? item.extJson.x : groupX,
//       y: item.extJson?.y ? item.extJson.y : groupY,
//       properties: {
//         width: item.extJson?.width ? item.extJson.width : BASE_GROUP_WIDTH,
//         height: item.extJson?.height ? item.extJson.height : groupHeight,
//         children: [], // 用于存储子节点的 ID
//         isCollapsed: parentInfo.depth >= 3 ? true : false,
//         // 限制组内节点的移动范围
//         // isRestrict: true,
//         // autoResize: true,
//         parentId: item.parentId,
//         name: getDisplayName(item.name),
//         createName: item.name,
//         ...(item.nodeList && item.nodeList.length > 8
//           ? {
//               nodeList: item.nodeList.slice(8).map(node => ({
//                 ...node
//               }))
//             }
//           : {})
//       },
//       text: {
//         x: item.extJson?.x ? item.extJson.x : groupX,
//         y: item.extJson?.y ? item.extJson.y : groupY,
//         value: getDisplayName(item.name),
//         createName: item.name
//         // value: item.name
//       }
//     };
//     result.push(groupNode);

//     // 处理节点列表
//     if (item.nodeList) {
//       const startX = groupX - BASE_GROUP_WIDTH / 2 + MARGIN_X;
//       const startY = groupY - groupHeight / 2 + MARGIN_Y;

//       item.nodeList.slice(0, 8).forEach((node, nodeIndex) => {
//         const row = Math.floor(nodeIndex / 2);
//         const col = nodeIndex % 2;

//         // 计算节点精确位置
//         const nodeX = startX + col * (NODE_WIDTH + COLUMN_SPACING);
//         const nodeY = startY + row * (NODE_HEIGHT + ROW_SPACING);

//         const nodeId = `${node.id}`;
//         result.push({
//           id: nodeId,
//           type: getEnglishTypeNameByNumber(node.type),
//           x: node.positionX ? node.positionX : nodeX + 100,
//           y: node.positionY ? node.positionY : nodeY + 40,
//           properties: {
//             width: NODE_WIDTH,
//             height: NODE_HEIGHT,
//             relation: node.relation,
//             ...node
//           },
//           text: {
//             value: getDisplayName(node.name),
//             createName: node.name
//             // value: node.name || node.relation
//           }
//         });
//         groupNode.properties.children.push(nodeId);
//       });
//     }

//     // 更新层级布局状态
//     layout.depths[currentDepth].currentX += BASE_GROUP_WIDTH + COLUMN_SPACING;
//     layout.depths[currentDepth].groupCount++;

//     // 递归处理子分组
//     if (item.children?.length) {
//       generateFlowJson(
//         item.children,
//         {
//           depth: currentDepth + 1,
//           parentId: groupId
//         },
//         result,
//         layout
//       );

//       // 关联子分组
//       const childGroups = result.filter(
//         n => n.type === "myCustomGroup" && n.properties.parentId === groupId
//       );
//       childGroups.forEach(child => {
//         groupNode.properties.children.push(child.id);
//       });
//     }
//   });

//   return result;
// }

// 将结果格式化为所需的结构
export function getDisplayName(name) {
  if (!name || typeof name !== "string") {
    return "";
  }
  const firstDashIndex = name.indexOf("-");
  if (firstDashIndex === -1) {
    return name;
  }
  const secondDashIndex = name.indexOf("-", firstDashIndex + 1);
  if (secondDashIndex === -1) {
    return name.slice(firstDashIndex + 1);
  }
  return name.slice(secondDashIndex + 1);
}

// 处理数组为node节点数据
export function flattenTree(arr, depth = 1) {
  let result = [];

  arr.forEach((item: any, index: Number) => {
    if (item.parentId === "0") {
      const fireWallNode = {
        id: randomNumber(),
        type: "firewall",
        x: item.x,
        y: item.y - item.height / 2 - 200,
        properties: {
          name: "防火墙",
          parentId: item.id,
          remark: `${item.name}的防火墙`,
          status: 1
        },
        text: {
          value: "防火墙"
        }
      };
      result.push(fireWallNode);
    }
    const GroupX = 0;
    const GroupY = 0;
    const flattenedNode = {
      id: item.id,
      type: "myCustomGroup",
      x: item.x ? item.x : GroupX,
      y: item.y ? item.y : GroupY,
      properties: {
        width: item.width || 400,
        height: item.height || 150,
        children: [],
        isCollapsed: depth > 2,
        // isCollapsed: false,
        parentId: item.parentId,
        status: 0,
        name: getDisplayName(item.name) || "",
        createName: item.name || "",
        ...(item.nodeList && item.nodeList.length > 4
          ? {
              nodeList: item.nodeList.slice(4).map(node => ({
                ...node
              }))
            }
          : {})
      },
      text: {
        x: item.x ? item.x : GroupX,
        y: item.y ? item.y : GroupY,
        value: getDisplayName(item.name)
      },
      children: []
    };

    if (item.nodeList && item.nodeList.length > 0) {
      flattenedNode.children = item.nodeList.map((child: any) => child.id);
      flattenedNode.properties.children = flattenedNode.children;
      result.push(flattenedNode);
      const getVIP = item.nodeList.filter(child => child.virtualIp).map(child => child);

      if (getVIP.length >= 2) {
        item.nodeList = item.nodeList.map(i => {
          return { ...i, vip: true };
        });
        const fireWallText = {
          id: randomNumber(),
          type: "text",
          properties: {
            name: `VIP：${getVIP[0].virtualIp}`,
            remark: `VIP：${getVIP[0].virtualIp}`
          },
          text: {
            // x: item.x,
            x:
              getVIP.length === 4
                ? 120 * 2 - 100 * 4
                : getVIP.length === 2
                  ? 120 * 1 + 80 * 2
                  : 120 * 2 + 60 * 2,
            y: item.y - item.height / 2 + 20,
            value: `VIP：${getVIP[0].virtualIp}`
          }
        };
        // let VIPGroup = {
        //   id: randomNumber(),
        //   type: "customNode",
        //   x: item.x ? item.x : GroupX,
        //   y: item.y ? item.y : GroupY,
        //   properties: {
        //     node_selection_ids: getVIP,
        //     strokeColor: "skyblue",
        //     disabledDelete: true
        //   }
        // };
        flattenedNode.children = [...flattenedNode.children, fireWallText.id];
        flattenedNode.properties.children = flattenedNode.children;
        result.push(fireWallText);
      }
      if (item.name === "运维安全管理区") {
        item.nodeList.forEach((node: any) => {
          const NodeX = 0;
          const NodeY = 0;
          if (node.status > 1) {
            flattenedNode.properties.status = node.status;
          }
          const flattenedNodeInList = {
            id: node.id,
            type: getEnglishTypeNameByNumber(node.type),
            x: node.x ? node.x : NodeX,
            y: node.y ? node.y : NodeY,
            properties: {
              name: node.name || "",
              createName: node.name || "",
              relation: node.relation || "",
              appid: node.appid || "",
              networkSegment: node.networkSegment || "",
              ip: node.ip || "",
              publicIp: node.publicIp || "",
              virtualIp: node.virtualIp || "",
              status: node.status || 0,
              createTime: node.createTime || "",
              updateTime: node.updateTime || ""
            },
            text: {
              x: node.x ? node.x : NodeX,
              y: node.y ? node.y : NodeY,
              value: getDisplayName(node.name)
              //         value:
              //           `${node.publicIp
              //             ? `EIP: ${node.publicIp}
              //  IP: ${node.ip}`
              //             : node.ip
              //               ? `IP: ${node.ip}`
              //               : `${getDisplayName(node.name)}`
              //           }` || ""
            }
          };
          result.push(flattenedNodeInList);
        });
      } else {
        item.nodeList.slice(0, 4).forEach((node: any) => {
          if (node.virtualIp && !node.vip) {
            // console.log(node);

            const fireWallText = {
              id: randomNumber(),
              type: "text",
              nodeId: node.id,
              properties: {
                name: `VIP：${node.virtualIp}`,
                remark: `VIP：${node.virtualIp}`
              },
              text: {
                x: node.x,
                y: node.y - 80,
                value: `VIP：${node.virtualIp}`
              }
            };
            flattenedNode.children = [...flattenedNode.children, fireWallText.id];
            flattenedNode.properties.children = flattenedNode.children;
            result.push(fireWallText);
          }
          const NodeX = 0;
          const NodeY = 0;
          // if (node.status > 1) {
          //   flattenedNode.properties.status = node.status;
          // }
          const flattenedNodeInList = {
            id: node.id,
            type: getEnglishTypeNameByNumber(node.type),
            x: node.x ? node.x : NodeX,
            y: node.y ? node.y : NodeY,
            properties: {
              name: node.ip || "",
              createName: node.name || "",
              relation: node.relation || "",
              appid: node.appid || "",
              networkSegment: node.networkSegment || "",
              ip: node.ip || "",
              publicIp: node.publicIp || "",
              virtualIp: node.virtualIp || "",
              status: node.status || 0,
              createTime: node.createTime || "",
              updateTime: node.updateTime || ""
            },
            text: {
              x: node.x ? node.x : NodeX,
              y: node.y ? node.y : NodeY,
              value:
                `${
                  node.publicIp
                    ? `EIP: ${node.publicIp}
       IP: ${node.ip}`
                    : node.ip
                      ? `IP: ${node.ip}`
                      : `${getDisplayName(node.name)}`
                }` || ""
            }
          };
          result.push(flattenedNodeInList);
        });
      }
    }

    if (item.children && item.children.length > 0) {
      flattenedNode.children = item.children.map((child: any) => child.id);
      flattenedNode.properties.children = flattenedNode.children;
      result.push(flattenedNode);

      // 递归调用，增加深度
      result = result.concat(flattenTree(item.children, depth + 1));
    }
  });

  return result;
}

// 保存接口
function processNodes(nodes) {
  console.log(nodes);
  const topoGroupList = [];

  // 创建一个映射，方便根据 id 快速查找节点
  const nodeMap = new Map();
  nodes.forEach(node => {
    nodeMap.set(node.id, node);
  });

  // 递归处理节点
  function processNode(node) {
    const nonDynamicGroupChildren = []; // 非 myCustomGroup 的子节点
    const children = []; // myCustomGroup 的子节点

    // 处理 children 中的节点
    if (node.properties && node.properties.children) {
      node.properties.children.forEach(childId => {
        const childNode = nodeMap.get(childId);
        if (childNode) {
          if (childNode.type === "myCustomGroup") {
            // 递归处理嵌套的 myCustomGroup 节点
            const processedChild = processNode(childNode);
            children.push(processedChild);
          } else {
            // 处理非 myCustomGroup 节点
            nonDynamicGroupChildren.push(childNode);
          }
        }
      });
    }

    // 处理 nodeIds
    const nodeIds = [
      ...nonDynamicGroupChildren.map(child => child.id),
      ...(node.properties.nodeList || []).map(node => node.id)
    ].join(",");

    // 处理 nodeList
    const nodeList = nonDynamicGroupChildren.map(child => ({
      id: child.id,
      appid: child.properties.appid || "",
      name: child.properties.createName || "",
      networkSegment: child.properties.networkSegment || "",
      ip: child.properties.ip || "",
      type: getNameToNumberMapping(child.type) || 0,
      relation: child.properties.relation || "",
      remark: child.properties.remark || "",
      positionX: child.x || 0,
      positionY: child.y || 0,
      publicIp: child.properties.publicIp || "",
      status: child.properties.status || 0,
      createTime: child.properties.createTime || "",
      updateTime: child.properties.updateTime || ""
    }));

    const extJson = JSON.stringify({
      width: node.properties.width || 0,
      height: node.properties.height || 0,
      x: node.x || 0,
      y: node.y || 0
    });

    return {
      // ...node,
      id: node.id,
      name: node.text.value,
      parentId: node.properties.parentId,
      nodeIds: nodeIds, // 用逗号连接的节点 ID 字符串
      extJson: extJson,
      nodeList: nodeList, // 处理后的非 myCustomGroup 节点
      children: children // 处理后的 myCustomGroup 节点
    };
  }

  // 处理顶层节点
  nodes.forEach(node => {
    if (node.properties.parentId === "0") {
      const processedGroup = processNode(node);
      topoGroupList.push(processedGroup);
    }
  });

  return topoGroupList;
}

// 处理edges返回信息
export const gtEdgeFlowJson = data => {
  const result = data.map(item => {
    // 处理 pointsList
    const formattedPoints = pointsList => {
      return pointsList.split(";").map(point => {
        const [x, y] = point.split(",");
        return { x: parseInt(x), y: parseInt(y) };
      });
    };

    // 处理 startPoint 和 endPoint
    const formattedPoint = point => {
      const [x, y] = point.split(",");
      return { x: parseInt(x), y: parseInt(y) };
    };
    return {
      id: item.id,
      sourceNodeId: item.sourceId,
      targetNodeId: item.targetId,
      type: item.type,
      startPoint: formattedPoint(item.startPoint),
      endPoint: formattedPoint(item.endPoint),
      pointsList: formattedPoints(item.pointsList)
    };
  });
  return result;
};
