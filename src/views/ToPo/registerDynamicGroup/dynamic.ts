import LogicFlow, { h } from "@logicflow/core";
import { dynamicGroup } from "@logicflow/extension";
import { randomNumber } from "../utils/index";

class CustomGroupView extends dynamicGroup.view {
  // 添加渲染生命周期日志
  createId() {
    return randomNumber();
  }
}

// 自定义分组模型
class CustomGroupModel extends dynamicGroup.model {
  // 初始化日志增强版
  initNodeData(data) {
    super.initNodeData(data);
    this.width = 400;
    this.height = 200;
    this.maxWidth = 99999;
    this.maxHeight = 99999;
    this.collapsedWidth = 180;
    this.radius = 4;
    this.textPadding = 8;
  }

  // 样式获取日志
  getNodeStyle() {
    const style = super.getNodeStyle();
    // console.log(this.properties);

    return {
      ...style,
      stroke: this.properties.status === 2 ? "#f56c6c" : "#666666",
      strokeDasharray: "3 3",
      strokeWidth: 1,
      fill: "#ffffff",
      // fill: "rgba(245, 245, 245, 0.5)",
      rx: this.radius,
      ry: this.radius
    };
  }
}

export default {
  type: "myCustomGroup",
  view: CustomGroupView,
  model: CustomGroupModel
};
