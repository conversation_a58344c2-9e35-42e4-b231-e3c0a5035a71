<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1743399998393"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="2792"
      width="200"
      height="200"
    >
      <path
        d="M387.264 479.68l-192 0c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C483.264 436.64 440.192 479.68 387.264 479.68zM195.264 159.68c-17.632 0-32 14.368-32 32l0 192c0 17.632 14.368 32 32 32l192 0c17.632 0 32-14.368 32-32l0-192c0-17.632-14.368-32-32-32L195.264 159.68z"
        p-id="2793"
      ></path>
      <path
        d="M387.264 927.68l-192 0c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C483.264 884.64 440.192 927.68 387.264 927.68zM195.264 607.68c-17.632 0-32 14.336-32 32l0 192c0 17.664 14.368 32 32 32l192 0c17.632 0 32-14.336 32-32l0-192c0-17.664-14.368-32-32-32L195.264 607.68z"
        p-id="2794"
      ></path>
      <path
        d="M832.128 479.68l-192 0c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C928.128 436.64 885.056 479.68 832.128 479.68zM640.128 159.68c-17.664 0-32 14.368-32 32l0 192c0 17.632 14.336 32 32 32l192 0c17.664 0 32-14.368 32-32l0-192c0-17.632-14.336-32-32-32L640.128 159.68z"
        p-id="2795"
      ></path>
      <path
        d="M832.128 927.68l-192 0c-52.928 0-96-43.072-96-96l0-192c0-52.928 43.072-96 96-96l192 0c52.928 0 96 43.072 96 96l0 192C928.128 884.64 885.056 927.68 832.128 927.68zM640.128 607.68c-17.664 0-32 14.336-32 32l0 192c0 17.664 14.336 32 32 32l192 0c17.664 0 32-14.336 32-32l0-192c0-17.664-14.336-32-32-32L640.128 607.68z"
        p-id="2796"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
