<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742112408625"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="10332"
      width="200"
      height="200"
    >
      <path
        d="M848 472c8.82 0 16 7.18 16 16v361c0 8.82-7.18 16-16 16H178c-8.82 0-16-7.18-16-16V488c0-8.82 7.18-16 16-16h670m0-64H178c-44.18 0-80 35.82-80 80v361c0 44.18 35.82 80 80 80h670c44.18 0 80-35.82 80-80V488c0-44.18-35.82-80-80-80z"
        p-id="10333"
      ></path>
      <path
        d="M513 128c50.75 0 98.46 19.76 134.35 55.65S703 267.25 703 318v90H323v-90c0-50.75 19.76-98.46 55.65-134.35S462.25 128 513 128m0-64c-140.28 0-254 113.72-254 254v154h508V318c0-140.28-113.72-254-254-254zM513 573.5c-17.67 0-32 14.33-32 32v126c0 17.67 14.33 32 32 32s32-14.33 32-32v-126c0-17.67-14.33-32-32-32z"
        p-id="10334"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
