<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742109737035"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="12559"
      width="200"
      height="200"
    >
      <path
        d="M108.2 278c-12.3 0-22.7-4.1-31.5-12.2-8.7-8.2-13.1-17.8-13.1-28.9v-94.5c0-11.7 4.4-21.6 13.1-29.7 8.7-8.2 19.2-12.2 31.5-12.2h570.3c12.2 0 22.7 4.1 31.5 12.2 8.7 8.2 13.1 18.1 13.1 29.7v94.5c0 11.1-4.4 20.7-13.1 28.9S690.8 278 678.5 278H108.2z m0 230.9c-12.3 0-22.7-4.1-31.5-12.2-8.7-8.2-13.1-18.1-13.1-29.7v-94.5c0-11.1 4.4-20.7 13.1-28.9s19.2-12.3 31.5-12.3h570.3c12.2 0 22.7 4.1 31.5 12.3 8.7 8.2 13.1 17.8 13.1 28.9V467c0 11.7-4.4 21.6-13.1 29.7-8.7 8.2-19.2 12.2-31.5 12.2H108.2z m304.4 114.6c-38.5 9.9-72.3 28.4-101.5 55.5-29.2 27.1-44.3 61.4-45.5 102.8-1.7 42 12.2 88.3 42 139.1H104.7c-19.8 0-33-12.8-39.4-38.5V601.7c0-21 13.1-34.1 39.4-39.4l347.3 0.9c-15.8 9.9-28.9 30-39.4 60.3zM158.9 142.4c-12.2 0-22.6 4.2-31 12.7-8.5 8.5-12.7 18.8-12.7 31.1 0 11.7 4.2 21.7 12.7 30.2 8.4 8.5 18.8 12.7 31 12.7 11.7 0 21.7-4.2 30.2-12.7 8.4-8.4 12.7-18.5 12.7-30.2 0-12.2-4.2-22.6-12.7-31.1-8.4-8.4-18.5-12.7-30.2-12.7z m0 233.6c-12.2 0-22.6 4.2-31 12.7-8.5 8.5-12.7 18.8-12.7 31.1 0 11.7 4.2 21.7 12.7 30.2 8.4 8.5 18.8 12.7 31 12.7 11.7 0 21.7-4.2 30.2-12.7 8.4-8.4 12.7-18.8 12.7-31.1 0-11.7-4.2-21.7-12.7-30.2-8.4-8.5-18.5-12.7-30.2-12.7z m715.6 320.1c56.6 28.6 84.8 67.1 84.8 115.5 0 49-28.6 84.8-85.7 107.6h-468c-59.5-30.9-89.2-74.1-89.2-129.5 0-55.4 19.1-89.5 57.3-102.3 38.2-12.8 66.3-16.6 84.4-11.4 2.9-50.1 22.4-79.9 58.6-89.2 43.7-4.1 75.8 3.5 96.2 22.7 60.1-54.2 119-70.3 176.7-48.1 58.3 21.6 86.6 66.5 84.9 134.7z m0 0"
        p-id="12560"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
