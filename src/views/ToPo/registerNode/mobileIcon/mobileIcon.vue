<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1743400482987"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="4839"
      width="200"
      height="200"
    >
      <path
        d="M513.232572 801.024649c-4.230348 0-8.444323-0.75827-12.467963-2.293229l-185.809893-70.886467c-12.544711-4.787026-21.234627-16.373922-22.350031-29.780257l-6.889921-83.941807c-1.574868-19.2269 12.727882-36.09508 31.950689-37.671994 19.48989-1.638313 36.094056 12.734022 37.682227 31.963992l5.097088 62.005192 153.11833 58.414411 162.173566-58.526975 6.444782-149.767002L315.026348 520.540514c-18.438954 0-33.701566-14.340613-34.858926-32.741705l-13.095249-208.28272c-0.621147-9.632381 2.795673-19.084661 9.388834-26.109658 6.614651-7.028067 15.827477-11.019984 25.470091-11.019984l433.917644 0c19.293415 0 34.936697 15.636119 34.936697 34.929534 0 19.300578-15.642259 34.936697-34.936697 34.936697l-396.715347 0 8.69094 138.420583 370.835933 0c9.530051 0 18.651802 3.889587 25.222451 10.769274 6.592139 6.879688 10.102079 16.143679 9.685593 25.664519l-9.03477 209.603809c-0.607844 14.157441-9.699919 26.542516-23.048949 31.358195l-196.407252 70.892607C521.258363 800.338011 517.234723 801.024649 513.232572 801.024649zM513.232572 980.803175c-4.299933 0-8.578376-0.783853-12.650111-2.371L159.556461 845.934488c-12.860912-4.992711-21.580504-17.104563-22.236444-30.885428L103.095541 100.733239c0.184195-36.966936 30.948873-37.361933 63.513546-37.796838 91.086531-1.203408 364.468922 1.196245 364.489388 1.196245-0.791016 0 246.738567-2.378164 329.086064-1.196245 27.059286 0.403183 60.745503 0.890276 60.745503 36.129872l-34.209126 715.983811c-0.657986 13.762445-9.325389 25.836435-22.145369 30.859845L525.958408 978.410686C521.86416 980.014206 517.545808 980.803175 513.232572 980.803175zM206.001259 789.033547l307.188334 119.355365 304.830637-119.28885 31.407314-656.423297c-12.742209-0.119727-30.560016-0.211824-55.515385-0.211824-105.08536 0-262.721587 1.531889-262.770705 1.531889-0.670266 0-175.036525-1.531889-291.298436-1.531889-30.156834 0-50.960649 0.112564-65.342194 0.257873L206.001259 789.033547z"
        p-id="4840"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
