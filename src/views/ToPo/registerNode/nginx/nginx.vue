<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1747035524079"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="2667"
      width="200"
      height="200"
    >
      <path
        d="M512 0L68.48 256v512L512 1024l443.52-256V256L512 0z m256 707.84c0 30.08-27.562667 55.04-65.237333 55.04-26.922667 0-57.642667-10.88-76.842667-34.56l-256-304.682667v284.16c0 30.762667-24.32 55.04-54.357333 55.04H312.32c-30.762667 0-55.04-25.6-55.04-55.04V316.16c0-30.08 26.88-55.04 64-55.04 27.562667 0 58.88 10.88 78.08 34.56l254.72 304.682667V316.16c0-30.762667 25.6-55.04 55.04-55.04h3.2c30.72 0 55.04 25.6 55.04 55.04v391.68H768z"
        p-id="2668"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
