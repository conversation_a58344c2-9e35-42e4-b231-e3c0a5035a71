<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742112153588"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="9328"
      width="200"
      height="200"
    >
      <path
        d="M256 832h512v64H256zM64 96v672h896V96H64z m832 608H128V160h768v544z m-390.4-44.8l-12.8-6.4S313.6 595.2 313.6 448V256L512 198.4 704 256v192c0 147.2-179.2 204.8-185.6 204.8l-12.8 6.4z m-140.8-352V448c0 76.8 96 115.2 140.8 140.8 44.8-25.6 140.8-64 140.8-140.8V307.2l-140.8-38.4-140.8 38.4z"
        p-id="9329"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
