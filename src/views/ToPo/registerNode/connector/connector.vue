<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742195537021"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="7964"
      width="200"
      height="200"
    >
      <path
        d="M0 64v832h960V64H0z m896 768H64V128h832v704z m-64-448h-128V256h-64V192H320v64H256v128H128v384h704V384z m-64 320H192V448h128V320h64V256h192v64h64v128h128v256zM256 512h64v128H256V512z m128 0h64v128H384V512z m128 0h64v128H512V512z m128 0h64v128h-64V512z"
        fill=""
        p-id="7965"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);

onMounted(() => {
  let borderColor = variables.dragNodeBorderColor;
  let iconColor;
  switch (props.properties.status) {
    case 1:
      iconColor = variables.primaryColor;
      borderColor = variables.primaryColor;
      break;
    case 2:
      iconColor = variables.dangerColor;
      borderColor = variables.dangerColor;
      break;
    case 3:
      iconColor = variables.infoColor;
      borderColor = variables.infoColor;
      break;
  }
  myNode.value.style.color = iconColor;
  myNode.value.style.border = `1px solid ${borderColor}`;
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 30px;
}
</style>
