<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742111907091"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="7342"
      width="200"
      height="200"
    >
      <path
        d="M514.74545778 1003.25489778c-2.83192889 0-5.71278222-0.42666667-8.55722666-1.32323556-76.43477333-24.08561778-165.30318222-102.87331555-211.47306667-147.69493333-70.38407111-68.32810667-133.64337778-146.45589333-165.09269333-203.89319111-30.47765333-55.66691555-47.91751111-147.17838222-51.83374223-271.99260445-2.85923555-91.12462222 2.67491555-165.62858667 2.91157333-168.75633778l1.87733334-24.77852444 24.80696889-1.46659556c1.09112889-0.06599111 111.81056-7.13614222 204.75335111-47.73205333 98.87857778-43.18776889 182.46314667-119.77500445 183.296-120.54300444 11.53820445-10.65073778 29.53216-9.93962667 40.18858666 1.59402667 10.65642667 11.53479111 9.95555555 29.51736889-1.57354666 40.17948444-3.67388445 3.39854222-91.22816 83.76775111-199.14069334 130.90133333-75.20597333 32.84878222-158.13859555 45.54410667-198.87786666 50.08839111-3.54531555 72.26936889-9.18869333 288.96938667 43.49041778 385.18442667 61.17489778 111.73205333 242.32618667 292.68423111 343.76362666 324.65009778 14.98339555 4.72064 23.30168889 20.69390222 18.57991112 35.67729777-3.82179555 12.13895111-15.03345778 19.90542222-27.11893334 19.90542223z"
        p-id="7343"
      ></path>
      <path
        d="M515.01624889 1003.25489778c-12.08661333 0-23.296-7.76419555-27.12120889-19.90314667-4.72064-14.98339555 3.59765333-30.95665778 18.58104889-35.67729778 101.43630222-31.96586667 282.58759111-212.91804445 343.76362667-324.65009778 52.73486222-96.31744 47.05507555-312.94008889 43.49610666-385.18442666-40.73813333-4.54428445-123.67644445-17.24074667-198.88241777-50.08839111-107.91480889-47.13472-195.46680889-127.50279111-199.14069333-130.90133333-11.53365333-10.66666667-12.23566222-28.66289778-1.56899556-40.19541334 10.66666667-11.53365333 28.66176-12.2368 40.19541333-1.56899556 0.82261333 0.75889778 84.40604445 77.34499555 183.28576 120.53390223 93.25681778 40.73358222 203.64856889 47.6672 204.75335111 47.73205333l24.80696889 1.46659556 1.87733333 24.77852444c0.23665778 3.12775111 5.76967111 77.63171555 2.91157334 168.75633778-3.91623111 124.81422222-21.35608889 216.32568889-51.83374222 271.99260444-31.44931555 57.43729778-94.70748445 135.56508445-165.09269334 203.89319112-46.17102222 44.82161778-135.03943111 123.60931555-211.47306666 147.69493333a28.51043555 28.51043555 0 0 1-8.55836445 1.32096z"
        p-id="7344"
      ></path>
      <path
        d="M654.59996445 695.84440889H390.65258667c-28.85859555 0-52.33777778-23.47918222-52.33777778-52.33777778V317.55491555c0-28.85859555 23.47918222-52.33777778 52.33777778-52.33777777h263.94737778c28.85859555 0 52.33777778 23.47918222 52.33777777 52.33777777v325.95171556c0 28.85859555-23.47918222 52.33777778-52.33777777 52.33777778z m-257.12071111-59.16444444h250.29404444V324.38158222H397.47925334v312.29838223z"
        p-id="7345"
      ></path>
      <path
        d="M581.21898667 427.91025778c0 12.56789333-10.18766222 22.75555555-22.75555555 22.75555555h-73.94986667c-12.56789333 0-22.75555555-10.18766222-22.75555556-22.75555555v-15.92547556c0-12.56789333 10.18766222-22.75555555 22.75555556-22.75555555h73.94986667c12.56789333 0 22.75555555 10.18766222 22.75555555 22.75555555v15.92547556z"
        p-id="7346"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
