<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742109494655"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="2641"
      width="200"
      height="200"
    >
      <path
        d="M918.905263 328.757895c35.031579 0 61.978947-26.947368 61.978948-61.978948V97.010526c0-32.336842-26.947368-61.978947-61.978948-61.978947H105.094737c-35.031579 0-61.978947 26.947368-61.978948 61.978947V269.473684c0 35.031579 26.947368 61.978947 61.978948 61.978948h72.757895v35.031579H105.094737c-35.031579 0-61.978947 26.947368-61.978948 61.978947v172.463158c0 35.031579 26.947368 61.978947 61.978948 61.978947h72.757895v35.031579H105.094737c-35.031579 0-61.978947 26.947368-61.978948 61.978947v172.463158c0 35.031579 26.947368 61.978947 61.978948 61.978948H916.210526c35.031579 0 61.978947-26.947368 61.978948-61.978948V754.526316c0-32.336842-26.947368-61.978947-61.978948-61.978948h-72.757894v-35.031579H916.210526c35.031579 0 61.978947-26.947368 61.978948-61.978947v-172.463158c0-32.336842-26.947368-61.978947-61.978948-61.978947h-72.757894v-35.031579h75.452631zM80.842105 269.473684V97.010526c0-13.473684 10.778947-24.252632 24.252632-24.252631h813.810526c13.473684 0 24.252632 10.778947 24.252632 24.252631V269.473684c0 13.473684-10.778947 24.252632-24.252632 24.252632H105.094737c-13.473684-2.694737-24.252632-10.778947-24.252632-24.252632z m862.31579 485.052632v172.463158c0 13.473684-10.778947 24.252632-24.252632 24.252631H105.094737c-13.473684 0-24.252632-10.778947-24.252632-24.252631V754.526316c0-13.473684 10.778947-24.252632 24.252632-24.252632h813.810526c13.473684 2.694737 24.252632 10.778947 24.252632 24.252632z m-134.736842-59.284211H215.578947v-35.031579h592.842106v35.031579z m134.736842-269.473684v172.463158c0 13.473684-10.778947 24.252632-24.252632 24.252632H105.094737c-13.473684 0-24.252632-10.778947-24.252632-24.252632v-172.463158c0-13.473684 10.778947-24.252632 24.252632-24.252632h813.810526c13.473684 0 24.252632 10.778947 24.252632 24.252632z m-134.736842-61.978947H215.578947v-35.031579h592.842106v35.031579z"
        p-id="2642"
      ></path>
      <path
        d="M388.042105 183.242105c0-10.778947-8.084211-18.863158-18.863158-18.863158H150.905263c-10.778947 0-18.863158 8.084211-18.863158 18.863158s8.084211 18.863158 18.863158 18.863158h218.273684c10.778947 0 18.863158-8.084211 18.863158-18.863158zM576.673684 234.442105c26.947368 0 51.2-21.557895 51.2-51.2 0-26.947368-21.557895-51.2-51.2-51.2s-51.2 21.557895-51.2 51.2c0 26.947368 24.252632 51.2 51.2 51.2z m0-64.673684c8.084211 0 13.473684 5.389474 13.473684 13.473684s-5.389474 13.473684-13.473684 13.473684-13.473684-8.084211-13.473684-13.473684c0-8.084211 5.389474-13.473684 13.473684-13.473684zM708.715789 234.442105c26.947368 0 51.2-21.557895 51.2-51.2 0-26.947368-21.557895-51.2-51.2-51.2-26.947368 0-51.2 21.557895-51.2 51.2 0 26.947368 24.252632 51.2 51.2 51.2z m0-64.673684c8.084211 0 13.473684 5.389474 13.473685 13.473684s-5.389474 13.473684-13.473685 13.473684-13.473684-8.084211-13.473684-13.473684c0-8.084211 5.389474-13.473684 13.473684-13.473684zM840.757895 234.442105c26.947368 0 51.2-21.557895 51.2-51.2 0-26.947368-21.557895-51.2-51.2-51.2-26.947368 0-51.2 21.557895-51.2 51.2 0 26.947368 21.557895 51.2 51.2 51.2z m0-64.673684c8.084211 0 13.473684 5.389474 13.473684 13.473684s-5.389474 13.473684-13.473684 13.473684-13.473684-8.084211-13.473684-13.473684c0-8.084211 5.389474-13.473684 13.473684-13.473684zM369.178947 493.136842H150.905263c-10.778947 0-18.863158 8.084211-18.863158 18.863158s8.084211 18.863158 18.863158 18.863158h218.273684c10.778947 0 18.863158-8.084211 18.863158-18.863158s-8.084211-18.863158-18.863158-18.863158zM576.673684 460.8c-26.947368 0-51.2 21.557895-51.2 51.2 0 26.947368 21.557895 51.2 51.2 51.2s51.2-21.557895 51.2-51.2c0-26.947368-24.252632-51.2-51.2-51.2z m0 64.673684c-8.084211 0-13.473684-5.389474-13.473684-13.473684s5.389474-13.473684 13.473684-13.473684 13.473684 5.389474 13.473684 13.473684-5.389474 13.473684-13.473684 13.473684zM708.715789 460.8c-26.947368 0-51.2 21.557895-51.2 51.2 0 26.947368 21.557895 51.2 51.2 51.2 26.947368 0 51.2-21.557895 51.2-51.2 0-26.947368-24.252632-51.2-51.2-51.2z m0 64.673684c-8.084211 0-13.473684-5.389474-13.473684-13.473684s5.389474-13.473684 13.473684-13.473684 13.473684 5.389474 13.473685 13.473684-5.389474 13.473684-13.473685 13.473684zM789.557895 512c0 26.947368 21.557895 51.2 51.2 51.2 26.947368 0 51.2-21.557895 51.2-51.2 0-26.947368-21.557895-51.2-51.2-51.2-29.642105 0-51.2 24.252632-51.2 51.2z m51.2-13.473684c8.084211 0 13.473684 5.389474 13.473684 13.473684s-5.389474 13.473684-13.473684 13.473684-13.473684-5.389474-13.473684-13.473684 5.389474-13.473684 13.473684-13.473684zM369.178947 821.894737H150.905263c-10.778947 0-18.863158 8.084211-18.863158 18.863158s8.084211 18.863158 18.863158 18.863158h218.273684c10.778947 0 18.863158-8.084211 18.863158-18.863158s-8.084211-18.863158-18.863158-18.863158zM576.673684 789.557895c-26.947368 0-51.2 21.557895-51.2 51.2s21.557895 51.2 51.2 51.2 51.2-21.557895 51.2-51.2-24.252632-51.2-51.2-51.2z m0 64.673684c-8.084211 0-13.473684-5.389474-13.473684-13.473684s5.389474-13.473684 13.473684-13.473684 13.473684 5.389474 13.473684 13.473684-5.389474 13.473684-13.473684 13.473684zM708.715789 789.557895c-26.947368 0-51.2 21.557895-51.2 51.2s21.557895 51.2 51.2 51.2c26.947368 0 51.2-21.557895 51.2-51.2s-24.252632-51.2-51.2-51.2z m0 64.673684c-8.084211 0-13.473684-5.389474-13.473684-13.473684s5.389474-13.473684 13.473684-13.473684 13.473684 5.389474 13.473685 13.473684-5.389474 13.473684-13.473685 13.473684zM789.557895 840.757895c0 26.947368 21.557895 51.2 51.2 51.2 26.947368 0 51.2-21.557895 51.2-51.2s-21.557895-51.2-51.2-51.2c-29.642105 0-51.2 24.252632-51.2 51.2z m51.2-13.473684c8.084211 0 13.473684 5.389474 13.473684 13.473684s-5.389474 13.473684-13.473684 13.473684-13.473684-5.389474-13.473684-13.473684 5.389474-13.473684 13.473684-13.473684z"
        p-id="2643"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
