<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742112959715"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="15436"
      width="200"
      height="200"
    >
      <path
        d="M859.2 710.4c-44.8-101.6-175.2-146.4-276.8-96-50.4 22.4-90.4 68-107.2 118.4-16.8 50.4-11.2 107.2 11.2 157.6 22.4 50.4 68 90.4 118.4 107.2 22.4 5.6 44.8 11.2 68 11.2 33.6 0 62.4-5.6 90.4-22.4 50.4-22.4 90.4-68 107.2-118.4 16.8-50.4 11.2-106.4-11.2-157.6z m-60.8 136c-11.2 34.4-34.4 56.8-68.8 74.4-34.4 16.8-68.8 16.8-102.4 5.6-34.4-11.2-56.8-34.4-74.4-68.8-16.8-33.6-16.8-68-5.6-102.4 11.2-34.4 34.4-56.8 68.8-74.4 16.8-5.6 40-11.2 56.8-11.2 51.2 0 96.8 28.8 120 74.4 16.8 34.4 16.8 68 5.6 102.4z"
        p-id="15437"
      ></path>
      <path
        d="M420.8 940H187.2c-36 0-64.8-28.8-64.8-64.8V148c0-36 28.8-64.8 64.8-64.8h610.4c36 0 64.8 28.8 64.8 64.8V432h72V64.8c0-36-28.8-64.8-64.8-64.8H115.2C79.2 0 50.4 28.8 50.4 64.8v894.4c0 36 28.8 64.8 64.8 64.8h305.6c23.2 0 41.6-18.4 41.6-41.6 0-23.2-19.2-42.4-41.6-42.4z"
        p-id="15438"
      ></path>
      <path d="M898.4 432m-36 0a36 36 0 1 0 72 0 36 36 0 1 0-72 0Z" p-id="15439"></path>
      <path
        d="M683.2 305.6H188.8c-20 0-36.8-16.8-36.8-36.8 0-20 16.8-36.8 36.8-36.8h494.4c20 0 36.8 16.8 36.8 36.8 0 20-16.8 36.8-36.8 36.8zM569.6 487.2H188.8c-20 0-36.8-16.8-36.8-36.8 0-20 16.8-36.8 36.8-36.8h380.8c20 0 36.8 16.8 36.8 36.8 0 20-16.8 36.8-36.8 36.8zM456 668.8H188.8C168 668.8 152 652 152 632c0-20 16.8-36.8 36.8-36.8H456c20 0 36.8 16.8 36.8 36.8 0 20-16.8 36.8-36.8 36.8zM917.6 1006.4l-114.4-71.2c-17.6-10.4-22.4-33.6-12-51.2 10.4-17.6 33.6-22.4 51.2-12l114.4 71.2c17.6 10.4 22.4 33.6 12 51.2-11.2 17.6-33.6 23.2-51.2 12z"
        p-id="15440"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);

onMounted(() => {
  let borderColor = variables.dragNodeBorderColor;
  let iconColor;
  switch (props.properties.status) {
    case 1:
      iconColor = variables.primaryColor;
      borderColor = variables.primaryColor;
      break;
    case 2:
      iconColor = variables.dangerColor;
      borderColor = variables.dangerColor;
      break;
    case 3:
      iconColor = variables.infoColor;
      borderColor = variables.infoColor;
      break;
  }
  myNode.value.style.color = iconColor;
  myNode.value.style.border = `1px solid ${borderColor}`;
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 30px;
}
</style>
