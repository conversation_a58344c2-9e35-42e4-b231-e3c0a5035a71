<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742110436978"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="2530"
      width="200"
      height="200"
    >
      <path
        d="M874.8544 853.3504c17.664 0 32 14.336 32 32v21.2992a32 32 0 0 1-64 0v-21.2992c0-17.7152 14.336-32 32-32z m-725.2992 0c17.664 0 32 14.336 32 32v21.2992a32 32 0 0 1-64 0v-21.2992c0-17.7152 14.336-32 32-32z m234.6496 0c17.664 0 32 14.336 32 32v21.2992a32 32 0 0 1-64 0v-21.2992c0-17.7152 14.336-32 32-32z m256 0c17.664 0 32 14.336 32 32v21.2992a32 32 0 0 1-64 0v-21.2992c0-17.7152 14.336-32 32-32z m-128-768a138.6496 138.6496 0 0 1 32 273.6128v110.4896l96-0.1024 2.4064 0.1536 232.2432 0.1024c17.7152 0 32 14.336 32 32v213.0432a32 32 0 0 1-64 0v-181.0432l-170.6496-0.1536v181.248a32 32 0 0 1-64 0v-181.248l-192-0.1024v181.2992a32 32 0 0 1-64 0v-181.248l-170.6496-0.1024v181.3504a32 32 0 0 1-27.2896 31.6928l-4.7104 0.3072a32 32 0 0 1-32-32V501.3504c0-17.7152 14.336-32 32-32l330.6496 0.0512V358.9632a138.752 138.752 0 0 1 32-273.6128z m0 64a74.6496 74.6496 0 1 0 0 149.2992 74.6496 74.6496 0 0 0 0-149.2992z"
        p-id="2531"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
