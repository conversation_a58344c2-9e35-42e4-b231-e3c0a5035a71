<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742109953257"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="13593"
      width="200"
      height="200"
    >
      <path
        d="M832 544c23.466667 2.133333 44.8 8.533333 64 17.066667v-17.066667c0-155.733333-91.733333-292.266667-224-356.266667 0 23.466667-6.4 44.8-14.933333 64 104.533333 55.466667 174.933333 166.4 174.933333 292.266667zM172.8 567.466667c0-8.533333-2.133333-17.066667-2.133333-23.466667 0-125.866667 70.4-236.8 174.933333-292.266667-8.533333-19.2-14.933333-40.533333-14.933333-64C198.4 251.733333 106.666667 388.266667 106.666667 544c0 17.066667 2.133333 32 4.266666 49.066667 17.066667-12.8 38.4-21.333333 61.866667-25.6zM686.933333 819.2c-53.333333 36.266667-117.333333 55.466667-185.6 55.466667-57.6 0-110.933333-14.933333-160-40.533334-12.8 19.2-29.866667 34.133333-46.933333 44.8 59.733333 36.266667 130.133333 59.733333 206.933333 59.733334 87.466667 0 168.533333-29.866667 232.533334-76.8-17.066667-10.666667-34.133333-25.6-46.933334-42.666667z"
        p-id="13594"
      ></path>
      <path
        d="M501.333333 181.333333m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z"
        p-id="13595"
      ></path>
      <path d="M202.666667 736m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" p-id="13596"></path>
      <path
        d="M821.333333 714.666667m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z"
        p-id="13597"
      ></path>
      <path
        d="M300.8 443.733333c-12.8 0-19.2 6.4-19.2 19.2v145.066667c0 12.8 6.4 17.066667 19.2 17.066667s19.2-6.4 19.2-17.066667V512l81.066667 106.666667c4.266667 4.266667 8.533333 6.4 17.066666 6.4 12.8 0 19.2-6.4 19.2-17.066667v-145.066667c0-12.8-6.4-19.2-19.2-19.2-12.8 0-19.2 6.4-19.2 19.2v96l-81.066666-108.8c-4.266667-4.266667-10.666667-6.4-17.066667-6.4zM526.933333 441.6c-12.8 0-21.333333 8.533333-27.733333 21.333333L448 601.6v8.533333c0 10.666667 6.4 17.066667 17.066667 17.066667 8.533333 0 14.933333-4.266667 19.2-12.8l8.533333-23.466667h70.4l6.4 23.466667c4.266667 8.533333 10.666667 12.8 19.2 12.8 10.666667 0 17.066667-4.266667 17.066667-14.933333 0-2.133333 0-6.4-2.133334-10.666667L554.666667 462.933333c-4.266667-12.8-14.933333-21.333333-27.733334-21.333333z m-23.466666 115.2l23.466666-74.666667 23.466667 74.666667h-46.933333zM622.933333 480h27.733334v128c0 12.8 6.4 17.066667 19.2 17.066667 12.8 0 19.2-6.4 19.2-17.066667v-128h27.733333c10.666667 0 17.066667-6.4 17.066667-17.066667s-6.4-17.066667-17.066667-17.066666h-96c-8.533333 0-14.933333 6.4-14.933333 17.066666 2.133333 10.666667 6.4 17.066667 17.066666 17.066667z"
        p-id="13598"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
