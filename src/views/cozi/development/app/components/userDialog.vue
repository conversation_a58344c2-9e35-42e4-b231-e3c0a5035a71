<template>
  <!-- 弹窗核心：通过 visible 同步父组件状态 -->
  <el-dialog v-model="visible" title="编辑应用" width="500px" @close="handleCancel">
    <!-- 表单区域：包含验证规则 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="validationRules"
      label-position="top"
      label-width="100%"
    >
      <!-- 应用名称（必填） -->
      <el-form-item label="应用名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入应用名称"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <!-- 应用介绍（选填，最多50字） -->
      <el-form-item label="应用介绍" prop="desc">
        <el-input
          type="textarea"
          :rows="6"
          v-model="formData.desc"
          placeholder="请输入应用介绍"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from "vue";
import type { FormInstance, FormRules } from "element-plus";

// 1. 接收父组件的显示状态（单向绑定）
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userList: {
    type: Object,
    default: () => ({
      name: "",
      desc: ""
    })
  }
});

// 2. 定义向父组件发射的事件
const emit = defineEmits([
  "update:visible", // 通知父组件更新弹窗显示状态
  "onConfirm" // 通知父组件提交表单数据
]);

// 3. 子组件内部维护本地显示状态，同步父组件的 props
const visible = ref(props.visible);
watch(
  () => props.visible,
  newVal => {
    visible.value = newVal;
    // 每次打开弹窗时重置表单（避免残留数据）
    if (newVal) {
      formData.value = { name: "", desc: "", content: "" };
    }
  }
);

// 4. 表单数据 & 验证规则
const formData = ref(props.userList);

const validationRules = ref<FormRules>({
  name: [
    { required: true, message: "请输入应用名称", trigger: "blur" },
    { max: 20, message: "最多20个字符", trigger: "blur" }
  ],
  desc: [{ max: 500, message: "最多500个字符", trigger: "blur" }],
  content: [{ required: false, message: "请输入应用内容", trigger: "blur" }]
});

// 5. 表单引用（用于校验）
const formRef = ref<FormInstance>();

// 6. 事件处理函数
/** 取消按钮：关闭弹窗 + 通知父组件 */
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
};

/** 确认按钮：校验表单 + 提交数据 */
const handleConfirm = () => {
  formRef.value?.validate(isValid => {
    if (isValid) {
      // 表单校验通过，发射提交事件（传递表单数据）
      emit("onConfirm", { ...formData.value });
      // 关闭弹窗
      handleCancel();
    }
  });
};
// 定义需要暴露给父组件的方法
const handleSubmit = () => {
  // 子组件内部的提交逻辑
};
defineExpose({
  handleSubmit
});
</script>

<style scoped>
/* 底部按钮居右 */
.dialog-footer {
  text-align: right;
}
</style>
