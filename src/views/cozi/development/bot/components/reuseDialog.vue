<template>
  <!-- 弹窗核心：通过 visible 同步父组件状态 -->
  <el-dialog v-model="visible" :title="title" width="800px" @close="handleCancel">
    <!-- 主内容区域：左侧搜索区 + 右侧卡片展示区 -->
    <div class="dialog-container">
      <!-- 左侧搜索区域 -->
      <div class="left-panel">
        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索插件..."
          prefix-icon="Search"
          class="search-input"
        />
        <!-- 按钮 -->
        <el-button type="primary" class="create-button" @click="handleCreate">
          {{ buttonText }}
        </el-button>
      </div>

      <!-- 右侧卡片展示区域 -->
      <div class="right-panel">
        <div v-if="items.length > 0" class="cards-container">
          <el-card
            v-for="item in filteredItems"
            :key="item.id"
            class="item-card"
            :body-style="{ padding: '16px' }"
          >
            <div class="card-content">
              <div class="card-title">{{ item.name }}</div>
              <div class="card-desc">{{ item.description }}</div>
            </div>
            <div class="card-actions">
              <el-button size="small" @click="handleSelect(item)">选择</el-button>
            </div>
          </el-card>
        </div>
        <div v-else class="empty-state">
          <div class="empty-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-empty">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="2" y1="12" x2="22" y2="12"></line>
              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
            </svg>
          </div>
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, computed } from "vue";

// 1. 接收父组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "添加"
  },
  buttonText: {
    type: String,
    default: "创建"
  },
  items: {
    type: Array,
    default: () => []
  }
});

// 2. 定义向父组件发射的事件
const emit = defineEmits([
  "update:visible", // 通知父组件更新弹窗显示状态
  "onSelect", // 通知父组件选中了某个项
  "onCreate" // 通知父组件点击了创建按钮
]);

// 3. 子组件内部维护本地显示状态，同步父组件的 props
const visible = ref(props.visible);
watch(
  () => props.visible,
  newVal => {
    visible.value = newVal;
    // 每次打开弹窗时重置搜索框
    if (newVal) {
      searchQuery.value = "";
    }
  }
);

// 4. 搜索相关数据
const searchQuery = ref("");

// 5. 过滤后的项目列表
const filteredItems = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.items;
  }
  return props.items.filter(item =>
    item.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// 6. 事件处理函数
/** 取消按钮：关闭弹窗 + 通知父组件 */
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
};

/** 选择项：通知父组件选择了某个项 */
const handleSelect = (item: any) => {
  emit("onSelect", item);
  handleCancel();
};

/** 创建按钮：通知父组件点击了创建按钮 */
const handleCreate = () => {
  emit("onCreate");
};
</script>

<style scoped>
/* 底部按钮居右 */
.dialog-footer {
  text-align: right;
}

/* 主容器布局 */
.dialog-container {
  display: flex;
  gap: 20px;
  height: 400px;
  overflow: hidden;
}

/* 左侧面板 */
.left-panel {
  width: 240px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 10px 0;
}

/* 搜索框样式 */
.search-input {
  width: 100%;
}

/* 创建按钮样式 */
.create-button {
  width: 100%;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

/* 卡片容器 */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

/* 卡片样式 */
.item-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content {
  margin-bottom: 16px;
}

.card-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.card-desc {
  color: #606266;
  font-size: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.empty-icon {
  margin-bottom: 16px;
}
</style>
