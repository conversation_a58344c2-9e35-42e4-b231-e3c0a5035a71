<template>
  <!-- 弹窗核心：通过 visible 同步父组件状态 -->
  <el-dialog v-model="visible" title="发布记录" width="600px" @close="handleCancel">
    <!-- 发布记录文本框 -->
    <div class="release-record-container">
      <label class="block text-sm font-medium text-gray-700 mb-1">发布记录</label>
      <el-input
        v-model="formData.releaseNote"
        type="textarea"
        placeholder="请输入发布记录"
        :rows="4"
        maxlength="2000"
        show-word-limit
      />
    </div>

    <!-- 选择发布平台 -->
    <div class="platform-selection mt-6">
      <label class="block text-sm font-medium text-gray-700 mb-3"
        >选择发布平台 <span class="text-red-500">*</span></label
      >
      <p class="text-xs text-gray-500 mb-4">
        在以下平台发布的智能体，即表示你已充分理解并同意遵循各发布渠道服务条款（包括但不限于任何隐私政策、社区指南、数据处理协议等）。
      </p>

      <el-checkbox-group v-model="formData.selectedPlatforms" class="platform-list">
        <el-checkbox :label="'api'" class="platform-item">
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center">
              <span>API</span>
              <el-icon class="ml-2 text-green-500"><check /></el-icon>
            </div>
            <el-button size="small" type="text" @click="handleConfigure('api')">配置</el-button>
          </div>
        </el-checkbox>

        <el-checkbox :label="'chat_sdk'" class="platform-item">
          <div class="flex items-center justify-between w-full">
            <div class="flex items-center">
              <span>Chat SDK</span>
              <el-icon class="ml-2 text-green-500"><check /></el-icon>
            </div>
            <el-button size="small" type="text" @click="handleConfigure('chat_sdk')"
              >配置</el-button
            >
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认发布</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from "vue";

// 1. 接收父组件的显示状态（单向绑定）
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// 2. 定义向父组件发射的事件
const emit = defineEmits([
  "update:visible", // 通知父组件更新弹窗显示状态
  "onConfirm" // 通知父组件提交表单数据
]);

// 3. 子组件内部维护本地显示状态，同步父组件的 props
const visible = ref(props.visible);
watch(
  () => props.visible,
  newVal => {
    visible.value = newVal;
    // 每次打开弹窗时重置表单（避免残留数据）
    if (newVal) {
      formData.value = {
        releaseNote: "",
        selectedPlatforms: ["api", "chat_sdk"]
      };
    }
  }
);

// 4. 表单数据
const formData = ref({
  releaseNote: "", // 发布记录
  selectedPlatforms: ["api", "chat_sdk"] // 选中的平台（默认全选）
});

// 移除了额外的watch监听，因为复选框会直接绑定到selectedPlatforms

// 5. 事件处理函数
/** 取消按钮：关闭弹窗 + 通知父组件 */
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
};

/** 确认按钮：提交发布数据 */
const handleConfirm = () => {
  // 由于平台默认全选且无法取消，这个验证实际上不会触发
  // 但我们保留它以确保代码的健壮性
  if (formData.value.selectedPlatforms.length === 0) {
    ElMessage.error("请至少选择一个发布平台");
    return;
  }
  ElMessage.success("发布成功");
  // 发射提交事件（传递表单数据）
  emit("onConfirm", {
    releaseNote: formData.value.releaseNote,
    platforms: formData.value.selectedPlatforms
  });

  // 关闭弹窗
  handleCancel();
};

/** 处理平台配置 */
const handleConfigure = (platform: string) => {
  // ElMessage.info(`配置${platform}平台`);
  // 这里可以添加平台配置逻辑
};

// 定义需要暴露给父组件的方法
const handleSubmit = () => {
  handleConfirm();
};
defineExpose({
  handleSubmit
});
</script>

<style scoped>
/* 底部按钮居右 */
.dialog-footer {
  text-align: right;
}

/* 发布记录容器样式 */
.release-record-container {
  margin-top: 10px;
}

/* 平台选择样式 */
.platform-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.platform-item {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
  width: 100%;
}

.platform-item:hover {
  background-color: #f0f0f0;
  transition: background-color 0.2s;
}

/* 图标样式 */
.el-icon {
  width: 16px;
  height: 16px;
}
</style>
