<template>
  <div class="agent-editor">
    <!-- 顶部栏：头像信息 + 操作按钮 -->
    <div class="top-bar">
      <!-- 头像 hover 触发悬浮框 -->
      <div class="avatar-container" ref="avatarContainer">
        <div
          class="avatar-group flex items-center"
          @mouseenter="showProfile = true"
          @mouseleave="showProfile = false"
        >
          <el-avatar :size="40" class="ml-3">
            <img src="https://picsum.photos/60/60" alt="智能体头像" />
          </el-avatar>
          <span class="ml-2 text-lg font-semibold">{{ agentName }}</span>
        </div>

        <div
          v-if="showProfile"
          class="profile-popup bg-white p-3 rounded shadow-md z-10 transition-all duration-200"
          @mouseenter="showProfile = true"
          @mouseleave="showProfile = false"
          ref="profilePopup"
        >
          <el-avatar :size="70" class="mb-2" />
          <p class="text-base font-semibold mt-5">{{ agentName }}</p>
          <p class="text-sm text-gray-500 mt-5">{{ agentDesc }}</p>
          <p class="text-xs text-gray-400 mt-5">创建时间：{{ creationTime }}</p>
        </div>
        <div class="mode-selector ml-3">
          <!-- 触发区：当前选中模式 + 下拉箭头 -->
          <el-dropdown trigger="click" @command="handleModeSelect">
            <div
              class="trigger-wrapper flex items-center gap-2 px-3 py-2 border rounded cursor-pointer"
            >
              <span class="font-medium">{{ currentMode.label }}</span>
              <el-icon class="text-gray-500">
                <arrow-down />
              </el-icon>
            </div>

            <!-- 下拉菜单内容 -->
            <template #dropdown>
              <el-dropdown-menu class="custom-dropdown-menu">
                <el-dropdown-item
                  v-for="mode in modeList"
                  :key="mode.value"
                  :command="mode"
                  class="mode-item"
                >
                  <div class="flex items-center gap-3 pr-2 pl-2">
                    <!-- 文字内容 -->
                    <div class="flex flex-col">
                      <span class="font-bold text-15px">{{ mode.label }}</span>
                      <p class="">{{ mode.desc }}</p>
                    </div>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="top-actions flex items-center mr-4 gap-2">
        <span style="font-weight: normal; font-size: 13px; color: #666"
          >草稿自动保存于14:29:38</span
        >
        <el-button
          type="primary"
          size="large"
          class="ml-3"
          style="width: 70px"
          @click="handleRelease"
          >发布</el-button
        >
      </div>
    </div>

    <div class="bottombox">
      <!-- 主体布局：左-中-右 -->
      <div>
        <h2 class="text-lg font-semibold mt-2 mb-2 ml-3">编排</h2>
        <!-- 左侧：编排区 -->
        <div class="main-layout flex h-[calc(100vh-60px)]">
          <div class="leftbian p-4">
            <div>
              <div style="display: flex; align-items: center; justify-content: space-between">
                <p class="text-gray-600 mb-6">人设与回复逻辑</p>
                <div class="tishici" style="font-size: 13px">
                  <span class="mr-5" @click="showPromptDialog = true">提交到提示词库</span>
                  <span @click="showViewDialog = true">提示词库</span>
                </div>
              </div>
              <el-input
                type="textarea"
                :rows="25"
                placeholder="请输入内容"
                class="mb-4"
                style="border: none"
                v-model="promptContent"
              ></el-input>
            </div>
            <!-- 提示词卡片 -->
            <div class="recommend-cards">
              <div class="bt-5">
                <el-button @click="geren = false" :type="!geren ? 'primary' : ''"> 推荐 </el-button>
                <el-button @click="geren = true" :type="geren ? 'primary' : ''"> 个人 </el-button>
              </div>
              <div
                class="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
                style="
                  padding: 5px;
                  padding-left: 0;
                  scrollbar-width: thin;
                  -ms-overflow-style: scrollbar;
                "
              >
                <!-- 内层容器：Flex布局确保卡片横向排列 -->
                <div class="flex gap-4 min-w-max" style="height: 175px" v-if="!geren">
                  <div
                    v-for="(item, idx) in recommendCards"
                    :key="item.id || idx"
                    class="cardxia group w-[180px] flex-shrink-0 relative"
                    style="max-width: 200px"
                    @click="handleCardClick(item)"
                    @mouseenter="hoverIndex = idx"
                    @mouseleave="hoverIndex = null"
                  >
                    <div class="flex flex-col h-full p-4">
                      <h3
                        class="font-medium text-gray-800 mb-2 group-hover:text-blue-600 transition-colors overflow-hidden line-clamp-2"
                      >
                        {{ item.title }}
                      </h3>
                      <p class="text-sm text-gray-500 flex-grow overflow-hidden line-clamp-3">
                        {{ item.description }}
                      </p>
                      <div
                        class="mt-2 text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <i class="fa fa-arrow-right text-xs"></i>
                      </div>
                    </div>
                    <!-- 悬浮窗 -->
                    <div v-show="hoverIndex === idx" class="profile-popup w-72 -mt-10 ml-2">
                      <h3 class="font-bold text-lg mb-2">{{ item.title }}</h3>
                      <p class="text-sm text-gray-600 mb-2">{{ item.description }}</p>
                      <div class="text-xs text-gray-500 space-y-1">
                        <p><span class="font-semibold">#角色：</span>{{ item.role || "无" }}</p>
                        <p><span class="font-semibold">#目标：</span>{{ item.goal || "无" }}</p>
                      </div>
                      <div class="mt-3 flex justify-end">
                        <span class="text-xs text-blue-500 cursor-pointer hover:underline"
                          >查看详情</span
                        >
                      </div>
                    </div>
                    <!-- 悬浮窗结束 -->
                  </div>
                </div>
                <div class="flex gap-4 min-w-max" style="height: 175px" v-else-if="geren">
                  <div
                    v-for="(item, idx) in recommendGerenCards"
                    :key="item.id || idx"
                    class="cardxia group w-[180px] flex-shrink-0 relative"
                    style="max-width: 200px"
                    @click="handleCardClick(item)"
                    @mouseenter="hoverIndex = idx"
                    @mouseleave="hoverIndex = null"
                  >
                    <div class="flex flex-col h-full p-4">
                      <h3
                        class="font-medium text-gray-800 mb-2 group-hover:text-blue-600 transition-colors overflow-hidden line-clamp-2"
                      >
                        {{ item.title }}
                      </h3>
                      <p class="text-sm text-gray-500 flex-grow overflow-hidden line-clamp-3">
                        {{ item.description }}
                      </p>
                      <div
                        class="mt-2 text-blue-600 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <i class="fa fa-arrow-right text-xs"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间：技能配置区 -->
          <div
            class="p-4 flex-1 border-x border-gray-200 overflow-y-auto"
            style="
              border-top: #e6e8f2 1px solid;
              border-right: #e6e8f2 1px solid;
              min-width: 300px;
              width: 37vw;
            "
          >
            <div v-for="(module, mIndex) in modules" :key="mIndex" class="mb-4">
              <!-- 模块标题 -->
              <h3
                class="text-base font-semibold mb-2 flex justify-between items-center"
                style="color: #888888"
              >
                {{ module.title }}
                <span v-if="module.subtitle" class="text-xs text-gray-500 ml-1">{{
                  module.subtitle
                }}</span>
              </h3>

              <!-- 模块折叠面板 -->
              <el-collapse v-model="activeCollapse" expand-icon-position="left">
                <el-collapse-item
                  v-for="(item, iIndex) in module.items"
                  :key="iIndex"
                  :name="item.name"
                >
                  <template #title>
                    <div class="flex justify-between items-center w-full">
                      <span class="font-medium">{{ item.title }}</span>

                      <!-- 右侧附加内容 -->
                      <div class="flex items-center space-x-2">
                        <!-- <el-badge v-if="item.badge" :value="item.badge" class="ml-2" size="small" /> -->
                        <el-switch
                          v-if="item.switch"
                          v-model="feedbackSwitch"
                          class="ml-2"
                          size="small"
                        />
                        <el-icon
                          class="cursor-pointer text-gray-500 hover:text-blue-500"
                          @click.stop="handleAdd(item.name)"
                        >
                          <Plus />
                        </el-icon>
                      </div>
                    </div>
                  </template>

                  <!-- 描述 -->
                  <div class="p-2 text-sm text-gray-600">
                    {{ item.desc }}
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧：预览调试区 -->
      <div style="flex: 0 0 35vw">
        <h2 class="text-lg font-semibold mt-2 mb-2 ml-3">预览与调试</h2>
        <div
          class="chat-container flex flex-col h-full bg-gray-50 rounded-lg shadow-lg overflow-hidden"
        >
          <!-- 聊天头部 -->
          <div class="chat-header bg-primary text-white p-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <el-avatar :size="40" class="bg-white text-primary">
                <i class="el-icon-chat-line-round text-xl"></i>
              </el-avatar>
              <h2 class="text-lg font-semibold">智能助手对话</h2>
            </div>
            <el-button icon="el-icon-refresh" size="small" circle @click="clearChat" />
          </div>

          <!-- 聊天记录区域 -->
          <div class="message-list flex-1 overflow-auto p-4 space-y-6">
            <!-- 系统提示 -->
            <div class="text-center">
              <span class="inline-block px-3 py-1 bg-gray-200 text-gray-600 rounded-full text-xs">
                今天 {{ currentTime }}
              </span>
            </div>

            <!-- 消息列表 -->
            <div v-for="(msg, idx) in messageList" :key="idx" class="message-item">
              <!-- 接收的消息 -->
              <div v-if="msg.type === 'receive'" class="flex items-start space-x-3">
                <el-avatar :size="40" class="mt-1 flex-shrink-0">
                  <img src="https://picsum.photos/id/64/200/200" alt="智能助手头像" />
                </el-avatar>
                <div class="max-w-[75%]">
                  <div class="bg-white p-3 rounded-lg rounded-tl-none shadow-sm">
                    {{ msg.content }}
                  </div>
                  <span class="text-xs text-gray-400 ml-2 mt-1 inline-block">
                    {{ formatTime(msg.timestamp) }}
                  </span>
                </div>
              </div>

              <!-- 发送的消息 -->
              <div v-if="msg.type === 'send'" class="flex items-start justify-end space-x-3">
                <div class="max-w-[75%] text-right">
                  <div class="bg-primary p-3 rounded-lg rounded-tr-none shadow-sm">
                    {{ msg.content }}
                  </div>
                  <span class="text-xs text-gray-400 mr-2 mt-1 inline-block">
                    {{ formatTime(msg.timestamp) }}
                  </span>
                </div>
                <el-avatar :size="40" class="mt-1 flex-shrink-0">
                  <img src="https://picsum.photos/id/237/200/200" alt="用户头像" />
                </el-avatar>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isLoading" class="text-center py-2">
              <p class="text-xs text-gray-400 mt-1">正在思考...</p>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input-area p-4 border-t bg-white" style="width: 30vw">
            <el-input
              v-model="inputMessage"
              placeholder="请输入消息..."
              class="mb-3"
              :rows="3"
              type="textarea"
              @keyup.enter.exact="sendMessage"
              @keyup.enter.shift.prevent
            />

            <div class="flex justify-between items-center">
              <p class="text-xs text-gray-500">内容由AI生成，无法确保真实准确，仅供参考</p>
              <el-button
                type="primary"
                @click="sendMessage"
                :disabled="!inputMessage.trim() || isLoading"
              >
                发送消息
                <i class="el-icon-send ml-1"></i>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <promptDialog v-model:visible="showPromptDialog" @onConfirm="handleSubmit" />
  <viewDialog v-model:dialogVisible="showViewDialog" @onInsertPrompt="handleInsertPrompt" />
  <!-- 复用弹窗组件 -->
  <reuseDialog
    v-model:visible="dialogVisible"
    :title="dialogTitle"
    :button-text="dialogButtonText"
    :items="dialogItems"
    @on-select="handleDialogSelect"
    @on-create="handleDialogCreate"
  />
  <!-- 发布弹窗组件 -->
  <releaseDialog v-model:visible="releaseDialogVisible" @onConfirm="handleSubmit" />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
// 导入固定数据
import {
  modeList,
  recommendCards,
  recommendGerenCards,
  modules,
  dialogConfig,
  initialMessages,
  replies
} from "./data";
import promptDialog from "./components/promptDialog.vue";
import viewDialog from "./components/viewDialog.vue";
import reuseDialog from "./components/reuseDialog.vue";
import releaseDialog from "./components/releaseDialog.vue";
// 智能体基础信息
const agentName = ref("ssss");
const agentDesc = ref("这是一个智能体的描述信息...");
const creationTime = ref("");
const geren = ref(false);
const showPromptDialog = ref(false);
const hoverIndex = ref<number | null>(null);
const showViewDialog = ref(false);
const promptContent = ref("");

// 处理从提示词库插入的提示词
const handleInsertPrompt = (promptText: string) => {
  promptContent.value = promptText;
};

onMounted(() => {
  creationTime.value = new Date().toLocaleString(); // 初始化创建时间
});

// 悬浮框控制
const showProfile = ref(false);
const showcard = ref(false);
const avatarContainer = ref<HTMLElement | null>(null);
const profilePopup = ref<HTMLElement | null>(null);

// 推荐卡片
const handleCardClick = (item: any) => {
  console.log("点击推荐功能:", item);
};

// 折叠面板状态
const activeCollapse = ref(["plugin"]);

// 对话体验开关
const feedbackSwitch = ref(true);

// 当前选中模式
const currentMode = ref(modeList[0]);

// 选择模式事件
const handleModeSelect = (mode: any) => {
  currentMode.value = mode;
  // 可扩展：触发父组件事件或API请求
  console.log("切换模式:", mode.label);
};

// 弹窗控制状态
const dialogVisible = ref(false);
const releaseDialogVisible = ref(false);
const dialogTitle = ref("");
const dialogButtonText = ref("");
const dialogItems = ref([]);

// 处理添加按钮点击事件
const handleAdd = (type: string) => {
  // 从配置中获取弹窗信息
  const config = dialogConfig[type] || dialogConfig.plugin; // 默认使用plugin配置
  dialogTitle.value = config.title;
  dialogButtonText.value = config.buttonText;
  dialogItems.value = config.items;

  // 打开弹窗
  dialogVisible.value = true;
};

// 处理弹窗选择事件
const handleDialogSelect = (item: any) => {
  ElMessage.success(`选择了: ${item.name}`);
  console.log("选择了:", item);
};

// 处理弹窗创建事件
const handleDialogCreate = () => {
  ElMessage.success(`点击了创建按钮: ${dialogButtonText.value}`);
  console.log("创建:", dialogTitle.value);
  dialogVisible.value = false;
};

// 处理发布按钮点击事件
const handleRelease = () => {
  // 设置发布弹窗的相关数据
  dialogTitle.value = "发布智能体";
  dialogButtonText.value = "确认发布";
  dialogItems.value = [
    {
      id: 1,
      name: "测试环境",
      description: "发布到测试环境，仅供内部测试使用"
    },
    {
      id: 2,
      name: "生产环境",
      description: "发布到生产环境，对所有用户可见"
    }
  ];

  // 显示发布弹窗
  releaseDialogVisible.value = true;
};
// 初始聊天消息
const messageList = ref(initialMessages);

// 输入框内容
const inputMessage = ref("");

// 加载状态
const isLoading = ref(false);

// 获取当前时间
const currentTime = computed(() => {
  const date = new Date();
  return date.toLocaleDateString("zh-CN", {
    month: "long",
    day: "numeric",
    weekday: "long"
  });
});

// 格式化时间显示
const formatTime = timestamp => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 创建提示词
const handleSubmit = (formData: any) => {
  console.log("处理提交数据:", formData);
};

// 发送消息
const sendMessage = () => {
  const content = inputMessage.value.trim();
  if (!content) return;

  // 添加用户发送的消息
  messageList.value.push({
    type: "send",
    content,
    timestamp: new Date().toISOString()
  });

  // 清空输入框
  inputMessage.value = "";

  // 滚动到底部
  scrollToBottom();

  // 模拟加载状态
  isLoading.value = true;

  // 模拟AI回复
  setTimeout(() => {
    // 随机选择一个回复
    const randomIndex = Math.floor(Math.random() * replies.length);
    const randomReply = replies[randomIndex];

    // 添加AI回复的消息
    messageList.value.push({
      type: "receive",
      content: randomReply,
      timestamp: new Date().toISOString()
    });

    // 关闭加载状态
    isLoading.value = false;

    // 滚动到底部
    scrollToBottom();
  }, 1500);
};

// 清空聊天记录
const clearChat = () => {
  // 使用initialMessages重置聊天记录并更新时间戳
  messageList.value = initialMessages.map(msg => ({
    ...msg,
    timestamp: new Date().toISOString()
  }));
  scrollToBottom();
};

// 滚动到最新消息
const scrollToBottom = () => {
  setTimeout(() => {
    const messageListEl = document.querySelector(".message-list");
    if (messageListEl) {
      messageListEl.scrollTop = messageListEl.scrollHeight;
    }
  }, 100);
};
onMounted(() => {
  nextTick(() => {
    if (avatarContainer.value && profilePopup.value) {
      const rect = avatarContainer.value.getBoundingClientRect();
      if (profilePopup.value) {
        profilePopup.value.style.left = "0px";
        profilePopup.value.style.top = rect.height + 5 + "px";
      }
    }
  });
  scrollToBottom();
});
</script>

<style scoped lang="scss">
.agent-editor {
  height: 100vh;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

  // 顶部栏
  .top-bar {
    height: 60px;
    border-bottom: 1px solid #e5e7eb;
    background: #f7f7fc;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .avatar-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
    }

    .avatar-group {
      cursor: pointer;
      padding: 10px 0;
    }

    .profile-popup {
      position: absolute;
      min-width: 300px;
      min-height: 300px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      z-index: 100;
      top: 50px;
      left: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
    }

    .top-actions {
      .el-button {
        padding: 0 12px;
      }
    }
    .mode-selector {
      width: 360px;

      // 触发区样式
      .trigger-wrapper {
        border-color: #dcdcdc;
        transition: border-color 0.2s;

        &:hover {
          border-color: #999;
        }
      }

      // 下拉菜单自定义样式
      .custom-dropdown-menu {
        padding: 8px;
        min-width: 320px;
        border-radius: 6px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }

      // 每个模式项
      .mode-item {
        padding: 8px 0;
        transition: background 0.2s;

        &:hover {
          background: #f5f7fa;
        }
      }

      // 图标颜色
      .text-primary {
        color: #409eff;
      }
    }
  }

  // 主体区域
  .editor-left,
  .editor-middle,
  .editor-right {
    padding-top: 16px;
  }

  // 推荐卡片
  .recommend-cards {
    .el-card {
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 折叠面板优化
  :deep(.el-collapse-item__header) {
    padding: 12px 16px;
  }
  :deep(.el-collapse-item__content) {
    padding: 0 16px 16px;
  }

  // 聊天样式
  .message-list {
    height: 100%;
    overflow-y: auto;
    & > div {
      max-width: 85%;
    }
  }
}
.leftbian {
  border: #e6e8f2 1px solid;
  height: 89vh;
  width: 33vw;
  border-left: none;
  border-bottom: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.cardxia {
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  height: 150px;
  overflow: hidden;
  position: relative;
  .profile-popup {
    // 绝对定位，相对于卡片
    position: absolute;
    top: 100%; // 显示在卡片正下方
    left: 50%;
    transform: translateX(-50%); // 水平居中
    margin-top: 8px; // 与卡片保持间距
    z-index: 10; // 确保悬浮在其他元素上方

    // 基础样式（确保可见）
    min-width: 220px;
    padding: 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    // 默认隐藏，hover时通过v-if控制显示
    display: block;
  }
}
.recommend-cards {
  position: relative;
  z-index: 10; // 确保卡片容器层级足够，悬浮窗不会被父元素遮挡
}
.cardxia:hover {
  border-color: #4299e1;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}
.main-layout {
  width: 100%;
  overflow: hidden;
}
.scrollbar::-webkit-scrollbar {
  width: 6px;
}
.scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}
.scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
.bottombox {
  display: flex;
}
.chat-container {
  height: 89vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.message-item {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

::v-deep .el-textarea__inner {
  resize: none;
  border-radius: 8px;
}

::v-deep .el-loading-spinner {
  animation: rotation 1s linear infinite;
}
.tishici {
  span {
    cursor: pointer;
    color: #3b82f6;
  }
}

/* 悬浮窗样式 */
.profile-popup {
  display: none; /* 默认隐藏 */
  position: absolute;
  z-index: 20; /* 高于卡片容器的z-index */
  background-color: white;
  border-radius: 8px;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 12px;
  transition: all 0.2s ease-in-out;
}

/* 鼠标悬停时显示悬浮窗 */
.cardxia:hover .profile-popup {
  display: block;
}
</style>
