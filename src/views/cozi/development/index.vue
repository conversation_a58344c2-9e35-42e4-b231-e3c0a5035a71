<template>
  <TitlecCom :title="`项目开发`" />
  <div class="top-container">
    <div>
      <el-select v-model="value1" placeholder="Select" style="width: 150px; margin-right: 15px">
        <el-option
          v-for="item in options1"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="value2" placeholder="Select" style="width: 150px; margin-right: 15px">
        <el-option
          v-for="item in options2"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="input1"
        style="width: 200px"
        placeholder="搜索项目"
        :suffix-icon="Search"
      />
    </div>
    <!-- 创建按钮 -->
    <el-button type="primary" @click="dialogVisible = true">+ 创建</el-button>
  </div>

  <!-- 卡片列表区域 -->
  <div class="cards-container">
    <el-card
      v-for="card in filteredCards"
      :key="card.id"
      class="card-item"
      shadow="hover"
      @click="goBot(card)"
    >
      <!-- 卡片头部 -->
      <div class="card-header">
        <div class="card-title">{{ card.title }}</div>
        <el-icon class="star-icon" @click.stop="card.starred = !card.starred">
          <Star :color="card.starred ? 'gold' : '#999'" />
        </el-icon>
      </div>

      <!-- 卡片主体 -->
      <div class="card-body">
        <div class="card-desc">{{ card.desc }}</div>
        <div class="card-meta-container">
          <div class="card-icon">
            <el-icon size="36"><ChatRound /></el-icon>
          </div>
          <div class="card-meta">
            <el-tag :type="card.tag === '智能体' ? 'info' : 'primary'" style="width: 100px">
              {{ card.tag }}
            </el-tag>
            <span class="card-time">
              <el-icon size="14"><Clock /></el-icon>
              <span>最近编辑 {{ card.time }}</span>
            </span>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <!-- 将操作菜单移到卡片右下角，鼠标悬浮显示 -->
      <div class="card-actions" @click.stop>
        <div class="action-button">
          <el-dropdown trigger="hover" placement="top-end" :show-timeout="300" :hide-timeout="500">
            <el-icon class="more-icon">
              <More />
            </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleCopy(card)">创建副本</el-dropdown-item>
                <el-dropdown-item type="danger">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-card>
  </div>

  <!-- 创建弹框 -->
  <el-dialog v-model="dialogVisible" title="创建" width="600px" center>
    <div class="create-dialog">
      <!-- 左边：智能体 -->
      <div class="create-option" @click="handleCreate('agent')">
        <img src="@/assets/images/agent.png" class="create-img" />
        <div class="create-title">创建智能体</div>
        <div class="create-desc">适用于快速搭建对话式智能体</div>
      </div>
      <!-- 右边：应用 -->
      <div class="create-option" @click="handleCreate('app')">
        <img src="@/assets/images/project.png" class="create-img" />
        <div class="create-title">创建应用 <el-tag type="warning" size="small">Beta</el-tag></div>
        <div class="create-desc">适用于搭建包含用户界面的完整应用</div>
      </div>
    </div>
  </el-dialog>
  <el-dialog v-model="dialogVisibleText" title="创建智能体" width="600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="validationRules"
      label-position="top"
      label-width="100%"
    >
      <el-form-item label="智能体名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入提示词名称"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="智能体功能介绍" prop="desc">
        <el-input
          v-model="formData.desc"
          placeholder="请输入提示词简介"
          maxlength="500"
          show-word-limit
          type="textarea"
          :rows="6"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisibleText = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";

import { ref, computed } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
import type { FormInstance, FormRules } from "element-plus";

// 筛选器
const value1 = ref("1");
const value2 = ref("1");
const input1 = ref("");
const dialogVisibleText = ref(false);
const formRef = ref<FormInstance>();
const formData = ref({
  name: "", // 智能体名称（必填）
  desc: "" // 智能体功能介绍（选填）
});
const validationRules = ref<FormRules>({
  name: [
    { required: true, message: "请输入提示词名称", trigger: "blur" },
    { max: 20, message: "最多20个字符", trigger: "blur" }
  ],
  desc: [{ max: 50, message: "最多50个字符", trigger: "blur" }]
});
const options1 = [
  { value: "1", label: "全部" },
  { value: "2", label: "应用" },
  { value: "3", label: "智能体" }
];
const options2 = [
  { value: "1", label: "全部" },
  { value: "2", label: "已发布" },
  { value: "3", label: "最近打开" }
];

// 卡片数据
const cards = ref([
  {
    id: 1,
    title: "客户服务智能体",
    desc: "自动回复客户咨询",
    tag: "智能体",
    status: "已发布",
    time: "14:29",
    starred: false
  },
  {
    id: 2,
    title: "数据分析应用",
    desc: "行为数据分析展示",
    tag: "应用",
    status: "已发布",
    time: "13:39",
    starred: true
  },
  {
    id: 3,
    title: "内容生成工具",
    desc: "生成文档和创意内容",
    tag: "应用",
    status: "最近打开",
    time: "昨天",
    starred: false
  }
]);

// 过滤后的卡片
const filteredCards = computed(() => {
  return cards.value.filter(card => {
    if (value1.value !== "1" && card.tag !== options1.find(o => o.value === value1.value)?.label)
      return false;
    if (value2.value !== "1" && card.status !== options2.find(o => o.value === value2.value)?.label)
      return false;
    if (input1.value && !card.title.includes(input1.value) && !card.desc.includes(input1.value))
      return false;
    return true;
  });
});

// 弹框控制
const dialogVisible = ref(false);

// 点击创建
const handleCreate = (type: string) => {
  dialogVisible.value = false;
  if (type === "agent") {
    dialogVisibleText.value = true;
  } else {
    console.log("跳转到创建应用页面");
  }
};
/** 确认按钮：校验表单 + 提交数据 */
const handleConfirm = () => {
  formRef.value?.validate(isValid => {
    if (isValid) {
      // 关闭弹窗
      dialogVisibleText.value = false;
    }
  });
};

// 创建副本
const handleCopy = (card: any) => {
  cards.value.push({
    ...card,
    id: cards.value.length + 1
  });
};

// 跳转到修改智能体页面
const goBot = (card: any) => {
  if (card.tag === "智能体") {
    const baseUrl = window.location.origin;
    const targetPath = `/development/bot/${card.id}`;
    const fullUrl = baseUrl + targetPath;
    window.open(fullUrl, "_blank");
  } else if (card.tag === "应用") {
    const baseUrl = window.location.origin;
    const targetPath = `/development/app/${card.id}`;
    const fullUrl = baseUrl + targetPath;
    window.open(fullUrl, "_blank");
  }
};
</script>

<style scoped>
.card-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.el-card:hover .card-actions,
.card-actions:hover {
  opacity: 1;
}

.action-button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20%;
  cursor: pointer;
  color: #606266;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* 确保按钮自身也能保持悬停状态 */
}

.more-icon {
  color: #606266;
  font-size: 18px;
}

.top-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  margin-bottom: 16px;
}

/* 卡片容器 */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

/* 卡片样式 */
.card-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 400px;
}

.card-item:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: 8px;
}

.star-icon {
  cursor: pointer;
  font-size: 18px;
  transition: color 0.2s;
}

.star-icon:hover {
  color: #e6a23c;
}

/* 卡片主体 */
.card-body {
  padding: 8px 16px 16px;
}

.card-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 42px; /* 两行文本高度 */
}

/* 元数据区域 */
.card-meta-container {
  display: flex;
  align-items: center;
}

.card-icon {
  color: #409eff;
  margin-right: 12px;
  flex-shrink: 0;
}

.card-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-time {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px;
  border-top: 1px solid #f5f5f5;
}

.more-icon {
  color: #999;
  cursor: pointer;
  font-size: 18px;
  transition: color 0.2s;
}

.more-icon:hover {
  color: #666;
}
.create-dialog {
  display: flex;
  gap: 16px;
}
.create-option {
  flex: 1;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
}
.create-option:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}
.create-img {
  width: 100px;
  margin-bottom: 16px;
}
.create-title {
  font-weight: bold;
  margin-bottom: 8px;
}
.create-desc {
  font-size: 14px;
  color: #666;
}
</style>
