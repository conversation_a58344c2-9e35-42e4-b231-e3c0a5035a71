<template>
  <TitlecCom :title="`资源库`" />
  <div class="top-container">
    <!-- 筛选与搜索区域 -->
    <div>
      <el-select v-model="value1" placeholder="类型筛选" style="width: 150px; margin-right: 15px">
        <el-option
          v-for="item in options1"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="value2" placeholder="状态筛选" style="width: 150px; margin-right: 15px">
        <el-option
          v-for="item in options2"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model="searchVal"
        style="width: 200px"
        placeholder="搜索资源名称"
        :suffix-icon="Search"
      />
    </div>

    <!-- 创建资源下拉按钮 -->
    <el-dropdown trigger="hover" placement="bottom-end" :show-timeout="300" :hide-timeout="300">
      <el-button type="primary">+ 资源</el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="createResource(1)">插件</el-dropdown-item>
          <el-dropdown-item @click="createResource(2)">工作流</el-dropdown-item>
          <el-dropdown-item @click="createResource(3)">对话流</el-dropdown-item>
          <el-dropdown-item @click="createResource(4)">知识库</el-dropdown-item>
          <el-dropdown-item @click="createResource(5)">提示词</el-dropdown-item>
          <el-dropdown-item @click="createResource(6)">数据库</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>

  <!-- 资源列表表格 -->
  <div class="list-container">
    <el-table :data="filteredTableData" style="width: 100%">
      <!-- 资源信息列（图标+名称+描述+状态） -->
      <el-table-column label="资源" align="left">
        <template #default="scope">
          <div class="resource-item">
            <!-- 类型图标 -->
            <div class="resource-icon">
              <img :src="scope.row.icon" alt="资源图标" />
            </div>
            <!-- 名称与状态 -->
            <div class="resource-info">
              <div class="resource-name">
                {{ scope.row.name }}
                <el-icon v-if="scope.row.status" style="color: #00b23c"
                  ><CircleCheckFilled
                /></el-icon>
              </div>
              <div class="resource-desc">{{ scope.row.desc }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 资源类型列 -->
      <el-table-column prop="type" label="类型" width="200" />

      <!-- 最后编辑时间列 -->
      <el-table-column prop="editTime" label="编辑时间" />

      <!-- 操作列（下拉菜单） -->
      <el-table-column label="操作" align="center" width="80">
        <template #default="scope">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">...</span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleEdit(scope.row)">编辑</el-dropdown-item>
                <el-dropdown-item @click="handleDelete(scope.row)">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <reuseDialog
    v-model:visible="dialogVisible"
    :dialogTitle="dialogTitle"
    @onCreate="handleCreate"
  />
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { ref, computed } from "vue";
import { Search } from "@element-plus/icons-vue";
// 引入图片资源
import Python from "@/image/Python.png";
import php from "@/image/php.png";
import java from "@/image/java.png";
import reuseDialog from "./components/reuseDialog.vue";

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const dialogButtonText = ref("");
const dialogItems = ref([]);
// 筛选器数据
const value1 = ref("all"); // 默认为全部类型
const value2 = ref("all"); // 默认为全部状态
const searchVal = ref("");

// 类型选项与实际类型的映射关系
const typeMapping = {
  all: [],
  plugin: ["插件"],
  workflow: ["工作流"],
  prompt: ["提示词"]
};

const options1 = [
  { value: "all", label: "所有类型" },
  { value: "plugin", label: "插件" },
  { value: "workflow", label: "工作流" },
  { value: "prompt", label: "提示词" }
];

const options2 = [
  { value: "all", label: "全部" },
  { value: "published", label: "已发布" },
  { value: "draft", label: "未发布" }
];

// 表格数据
const tableData = ref([
  {
    name: "sss_1",
    desc: "阿水擦拭",
    type: "工作流",
    editTime: "2025-08-22 00:09",
    status: false,
    icon: Python
  },
  {
    name: "xx",
    desc: "asc",
    type: "提示词",
    editTime: "2025-08-20 20:27",
    status: true,
    icon: php
  },
  {
    name: "sss",
    desc: "阿水擦拭",
    type: "工作流",
    editTime: "2025-08-20 13:37",
    status: false,
    icon: java
  }
]);

// 修复后的筛选逻辑
const filteredTableData = computed(() => {
  return tableData.value.filter(item => {
    // 1. 类型筛选 - 使用映射关系进行匹配
    const typeMatch =
      value1.value === "all"
        ? true
        : typeMapping[value1.value as keyof typeof typeMapping].includes(item.type);

    // 2. 状态筛选
    const statusMatch =
      value2.value === "all"
        ? true
        : (value2.value === "published" && item.status) ||
          (value2.value === "draft" && !item.status);

    // 3. 搜索筛选
    const searchMatch =
      searchVal.value.trim() === ""
        ? true
        : item.name.toLowerCase().includes(searchVal.value.toLowerCase().trim());

    // 所有条件都满足才显示
    return typeMatch && statusMatch && searchMatch;
  });
});

// 编辑操作
const handleEdit = (row: any) => {
  // 编辑逻辑
};

// 删除操作
const handleDelete = (row: any) => {
  tableData.value = tableData.value.filter(item => item !== row);
};

// 创建资源
const createResource = (type: number) => {
  if (type === 1) {
    dialogTitle.value = "插件";
  } else if (type === 2) {
    dialogTitle.value = "工作流";
  } else if (type === 3) {
    dialogTitle.value = "对话流";
  } else if (type === 4) {
    dialogTitle.value = "知识库";
  } else if (type === 5) {
    dialogTitle.value = "提示词";
  } else if (type === 6) {
    dialogTitle.value = "数据库";
  }
  dialogVisible.value = true;
};

// 处理弹窗选择事件
const handleDialogSelect = (item: any) => {
  ElMessage.success(`选择了: ${item.name}`);
  console.log("选择了:", item);
};
const handleCreate = data => {
  console.log("提交的对话流数据：", data);
  // 此处可调用后端接口保存数据
};
// 处理弹窗创建事件
const handleDialogCreate = () => {
  ElMessage.success(`点击了创建按钮: ${dialogButtonText.value}`);
  console.log("创建:", dialogTitle.value);
  dialogVisible.value = false;
};
</script>

<style scoped>
.top-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  margin-bottom: 20px;
}

.list-container {
  background: #fff;
  border-radius: 8px;
}

/* 资源项样式 */
.resource-item {
  display: flex;
  align-items: center;
  gap: 12px;
}
.resource-icon {
  width: 50px;
  height: 50px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.resource-icon img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.resource-info {
  display: flex;
  flex-direction: column;
}
.resource-name {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}
.resource-desc {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 下拉按钮样式优化 */
.el-dropdown-link {
  cursor: pointer;
  color: #606266;
  font-size: 18px;
}
</style>
