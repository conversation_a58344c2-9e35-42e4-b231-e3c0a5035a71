<template>
  <div class="log-container">
    <div class="seach-container">
      <el-form>
        <el-form-item class="input-group">
          <label>日志内容：</label>
          <el-input placeholder="请输入日志内容" v-model="pageParams.message" clearable></el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label>日志级别：</label>
          <el-select placeholder="请选择日志级别" v-model="pageParams.level" clearable filterable>
            <el-option v-for="item in level" :key="item" :value="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>来源IP：</label>
          <el-input placeholder="请输入来源IP" v-model="pageParams.sourceIp" clearable></el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label>来源名称： </label>
          <el-input
            placeholder="请输入来源名称"
            v-model="pageParams.sourceName"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label>来源类型： </label>
          <el-select
            placeholder="请选择来源类型"
            v-model="pageParams.sourceType"
            clearable
            filterable
          >
            <el-option
              v-for="item in sourceTypes"
              :key="item"
              :value="item"
              :label="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <template v-for="key in metadataConditions" :key="key">
          <el-form-item class="input-group">
            <label>{{ key }}：</label>
            <el-input
              v-model="pageParams.metadata[key]"
              :placeholder="`请输入${key}`"
              clearable
            ></el-input>
          </el-form-item>
        </template>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px">清空</el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-wrapper">
      <div class="table-search">
        <el-input
          v-model="pageParams.query"
          placeholder="*"
          clearable
          @keyup.enter="search"
          style="margin-bottom: 8px"
        >
          <template #append>
            <el-button :icon="Search" @click="search" />
          </template>
        </el-input>
      </div>
      <div
        style="
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          position: relative;
        "
      >
        <div
          ref="chartContainer"
          :style="{ width: '100%', height: '250px' }"
          v-loading="chartLoading"
        ></div>
        <el-button
          type="primary"
          style="margin-bottom: 8px; margin-left: 10px"
          @click="analyzeLogs"
          :disabled="!hasTimeRange"
        >
          日志分析
        </el-button>
      </div>
      <div
        v-loading="topTableLoading"
        class="flex items-center gap-3 p-2"
        style="min-width: max-content"
      >
        <span class="text-gray-500">高频关键词：</span>
        <div
          v-for="(item, index) in topTableData"
          :key="index"
          class="px-3 py-1 rounded-full text-white text-sm whitespace-nowrap transition-opacity duration-300"
          :style="{
            backgroundColor: getColor(index),
            cursor: 'pointer',
            opacity: hoverIndex === null || hoverIndex === index ? 1 : 0.3
          }"
          @mouseenter="hoverIndex = index"
          @mouseleave="hoverIndex = null"
          @click="fillSearch(item.key)"
        >
          {{ item.key }}
        </div>
      </div>

      <MyTable
        :data="tableData"
        :total="total"
        v-loading="tableLoading"
        style="width: 100%"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="expanded-content">
              <div class="expanded-row">
                <div class="message-header">
                  <span class="label">message:</span>
                  <span class="action-link" @click="copyToClipboard(props.row.message)"
                    >复制内容</span
                  >
                </div>
                <div class="message-content">
                  {{ props.row.message }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <my-column property="message" label="日志看板" :show-overflow-tooltip="false">
          <template #default="{ row }">
            <div class="log-content">
              <span class="log-label">time：</span>
              <span>{{ row.timestamp }}</span>
              <span class="log-label">sourceIp：</span>
              <span class="clickable-field" @click="fillSearchField('sourceIp', row.sourceIp)">{{
                row.sourceIp
              }}</span>
              <span class="log-label">sourceName：</span>
              <span
                class="clickable-field"
                @click="fillSearchField('sourceName', row.sourceName)"
                >{{ row.sourceName }}</span
              >
              <span class="log-label">sourceType：</span>
              <span
                class="clickable-field"
                @click="fillSearchField('sourceType', row.sourceType)"
                >{{ row.sourceType }}</span
              >
              <span class="log-label">level：</span>
              <span class="clickable-field" @click="fillSearchField('level', row.level)">{{
                row.level
              }}</span>
              <span class="log-label">metadata：</span>
              <template v-for="(value, key) in row.metadata" :key="key">
                <span class="clickable-field" @click="addMetadataCondition(String(key))">
                  <span class="log-child-label">{{ key }}:</span>
                  <span @click.stop="fillMetadataField(String(key), value)">{{ value }}</span>
                </span>
              </template>
              <span class="log-label">message：</span>
              <span class="clickable-field" @click="fillSearchField('message', row.message)">{{
                row.message.length > 500 ? row.message.slice(0, 500) + "..." : row.message
              }}</span>
            </div>
          </template>
        </my-column>
        <my-column label="操作" align="center" header-align="center" fixed="right" width="80">
          <template #default="scope">
            <span class="operate" @click="shouType(scope.row)">详情 </span>
          </template>
        </my-column>
      </MyTable>
    </div>
  </div>
  <div>
    <el-drawer v-model="logVisible" title="日志详情" size="50%">
      <table class="detail-table">
        <tbody>
          <tr>
            <td class="label">time</td>
            <td>{{ logType.timestamp }}</td>
          </tr>
          <tr>
            <td class="label">sourceIp</td>
            <td>{{ logType.sourceIp }}</td>
          </tr>
          <tr>
            <td class="label">sourceName</td>
            <td>{{ logType.sourceName }}</td>
          </tr>
          <tr>
            <td class="label">sourceType</td>
            <td>{{ logType.sourceType }}</td>
          </tr>
          <tr>
            <td class="label">level</td>
            <td>
              <el-tag
                size="large"
                :type="
                  ['ERROR', 'FATAL'].includes(logType.level)
                    ? 'danger'
                    : ['INFO', 'TRACE', 'DEBUG'].includes(logType.level)
                      ? 'success'
                      : 'warning'
                "
                >{{ logType.level }}</el-tag
              >
            </td>
          </tr>
          <template v-for="field in metadataFields" :key="field.key">
            <tr v-if="logType.metadata[field.key]">
              <td class="label">{{ field.label }}</td>
              <td>{{ logType.metadata[field.key] }}</td>
            </tr>
          </template>

          <tr>
            <td class="label">message</td>
            <td>
              <div class="content-wrapper">
                <span
                  class="content-text"
                  @click="toggleMessageExpand"
                  :style="{
                    cursor: 'pointer'
                  }"
                >
                  {{ isMessageExpanded ? logType.message : truncatedMessage }}
                </span>
                <span
                  class="action-link"
                  @click="handleAnalyzeInDetail(logType)"
                  v-loading="analyzing"
                >
                  分析
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div v-if="showAnalysis" class="analysis-result mt-12px">
        <div class="result-title">
          <el-icon><Lightning /></el-icon>
          <span class="ml-4px">AI 分析结果</span>
        </div>
        <div class="result-content" v-loading="analyzing">
          <div class="analysis-item">
            <div class="result-text">{{ analysisResult }}</div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>

  <!-- 日志分析弹窗 -->
  <el-dialog
    v-model="analysisDialogVisible"
    title="日志分析"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form :model="analysisForm" label-width="100px">
      <el-form-item label="提示词名称">
        <el-select
          v-model="analysisForm.promptName"
          placeholder="请选择提示词名称"
          clearable
          filterable
          @change="handlePromptChange"
        >
          <el-option
            v-for="item in promptList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提示词内容">
        <el-input
          v-model="analysisForm.promptContent"
          type="textarea"
          :rows="8"
          placeholder="提示词内容"
          readonly
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="analysisDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAnalysis" :loading="analysisLoading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import {
  logSearch,
  logSearchChart,
  getSourceTypeList,
  getLevelList,
  getTop10Logs
} from "@/api/log/index";
import { aiPromptSearch } from "@/api/system/aiPrompt";
import { onMounted, reactive, ref, computed } from "vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import dayjs from "dayjs";
import { useRoute } from "vue-router";
import { Lightning } from "@element-plus/icons-vue";
import { deepseekChat } from "@/api/warnManage/error";
import * as echarts from "echarts";
import { formatTime } from "@/utils/dateStr";
import { useRouter } from "vue-router";
const router = useRouter();
const chartContainer = ref<HTMLElement | null>(null);
const chartLoading = ref(false);
const timestamps = ref<string[]>([]);
const logCounts = ref<number[]>([]);
const analyzeLogs = async () => {
  if (!hasTimeRange.value) {
    ElMessage.warning("请先框选时间范围");
    return;
  }

  try {
    // 获取提示词列表
    const response = await aiPromptSearch();
    if (response.code === 0) {
      promptList.value = response.records || [];
      analysisDialogVisible.value = true;
      // 重置表单
      analysisForm.promptName = "";
      analysisForm.promptContent = "";
    } else {
      // ElMessage.error("获取提示词列表失败");
    }
  } catch (error) {
    console.error("获取提示词列表失败:", error);
    // ElMessage.error("获取提示词列表失败");
  }
};
// 定义 metadata 的类型
interface Metadata {
  traceId: string;
  spanId: string;
  instanceId: string;
  scopeName: string;
  serviceName: string;
  file: string;
  method: string;
  client: string;
  uri: string;
  status: string;
}

// 定义 logType 的类型
interface LogType {
  timestamp: string;
  sourceIp: string;
  sourceName: string;
  sourceType: string;
  level: string;
  message: string;
  metadata: Metadata;
}

// 新增状态
const isMessageExpanded = ref(false);
const showAnalysis = ref(false);
const truncatedMessage = computed(() =>
  logType.value.message.length > 200
    ? logType.value.message.slice(0, 200) + "..."
    : logType.value.message
);
// 切换消息展开状态
const toggleMessageExpand = () => {
  isMessageExpanded.value = !isMessageExpanded.value;
};
const logType = ref<LogType>({
  timestamp: "",
  sourceIp: "",
  sourceName: "",
  sourceType: "",
  level: "",
  message: "",
  metadata: {
    traceId: "",
    spanId: "",
    instanceId: "",
    scopeName: "",
    serviceName: "",
    file: "",
    method: "",
    client: "",
    uri: "",
    status: ""
  }
});

// metadataFields 的 key 必须是 Metadata 的键
const metadataFields: { key: keyof Metadata; label: string }[] = [
  { key: "traceId", label: "traceId" },
  { key: "spanId", label: "spanId" },
  { key: "instanceId", label: "instanceId" },
  { key: "scopeName", label: "scopeName" },
  { key: "serviceName", label: "serviceName" },
  { key: "file", label: "file" },
  { key: "method", label: "method" },
  { key: "client", label: "client" },
  { key: "uri", label: "uri" },
  { key: "status", label: "status" }
];
const logVisible = ref(false);
const shouType = async (row: any) => {
  logVisible.value = true;
  logType.value = row;
  showAnalysis.value = false;
  analysisResult.value = "";
  isMessageExpanded.value = false;
};
// 统一时间格式化方法
const formatFullTime = (timestamp: number) => {
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 与TCP页面完全一致的图表配置
const createChartOptions = () => ({
  tooltip: {
    trigger: "axis",
    axisPointer: {
      lineStyle: {
        width: 1,
        color: "#008000"
      }
    },
    formatter: (params: any) => {
      const timestamp = params[0].axisValue;
      const formattedTime = dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
      let tooltipContent = `<h2>${formattedTime}</h2>`;
      params.reverse().forEach((item: any) => {
        tooltipContent += `
          <div style="display: flex; align-items: center; margin: 5px 0;">
            <span style="display: inline-block; width: 8px; height: 8px;
              background-color: ${item.color}; border-radius: 50%; margin-right: 5px;"></span>
            <span>${item.seriesName}: ${item.value.toLocaleString()}</span>
          </div>
        `;
      });
      return tooltipContent;
    }
  },
  brush: {
    toolbox: [""],
    show: false,
    brushStyle: {
      borderWidth: 1,
      borderColor: "#27AE60",
      color: "rgba(39,174,96,0.2)"
    },
    xAxisIndex: "all",
    brushLink: "all",
    brushType: "lineX",
    brushMode: "single",
    outOfBrush: {
      colorAlpha: 0.1
    },
    throttleDelay: 500,
    throttleType: "debounce"
  },
  grid: {
    left: "1%",
    right: "1%",
    bottom: "14%",
    containLabel: true
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: timestamps.value,
    axisLabel: {
      formatter: (value: string) => dayjs(value).format("YYYY-MM-DD HH:mm:ss")
    }
  },
  yAxis: {
    type: "value",
    axisLabel: {
      formatter: (value: number) => value.toLocaleString()
    }
  },
  series: [
    {
      name: "日志数量",
      type: "line",
      stack: "Total",
      data: logCounts.value,
      areaStyle: {},
      itemStyle: { color: "#27AE60" },
      symbol: logCounts.value.length === 1 ? "emptyCircle" : "none"
    }
  ],
  dataZoom: [
    {
      type: "inside",
      start: 0,
      end: 100
    },
    {
      show: true,
      type: "slider",
      start: 0,
      end: 100,
      height: 20,
      bottom: 10
    }
  ],
  legend: {
    data: ["日志数量"],
    top: 10,
    itemGap: 25,
    itemWidth: 35
  }
});

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  const chartInstance = echarts.init(chartContainer.value);
  chartInstance.setOption(createChartOptions());

  // 自动激活框选模式
  chartInstance.dispatchAction({
    type: "takeGlobalCursor",
    key: "brush",
    brushOption: {
      brushType: "lineX",
      brushMode: "single"
    }
  });

  let timeRangeText: any = null;

  // 处理框选事件
  chartInstance.on("brushSelected", (params: any) => {
    const batch = params?.batch?.[0];

    // 清除旧提示
    if (timeRangeText) {
      chartInstance.setOption({ graphic: [] });
      timeRangeText = null;
    }

    if (!batch?.areas?.length) {
      pageParams.startTime = "";
      pageParams.endTime = "";
      loadData();
      getTopLogData();
      return;
    }

    const coordRange = batch.areas[0].coordRange;
    const startIdx = Math.floor(coordRange[0]);
    const endIdx = Math.ceil(coordRange[1]);

    // 处理边界情况
    const validStart = Math.max(0, startIdx);
    const validEnd = Math.min(timestamps.value.length - 1, endIdx);
    const selectedTimes = timestamps.value.slice(validStart, validEnd + 1);

    if (selectedTimes.length > 0) {
      let startTime = selectedTimes[0];
      let endTime = selectedTimes[selectedTimes.length - 1];

      // 处理相同时间点的情况
      if (validStart === validEnd) {
        const nextIndex = validEnd + 1;
        if (nextIndex < timestamps.value.length) {
          endTime = timestamps.value[nextIndex];
        }
      }
      pageParams.startTime = startTime;
      pageParams.endTime = endTime;

      // 先加载数据，再更新图表显示
      loadData().then(() => {
        getTopLogData();

        // 数据加载完成后，更新图表显示的总数
        timeRangeText = {
          type: "text",
          style: {
            text: `当前选取时间范围：${startTime} - ${endTime}     当前选取总日志数: ${total.value}`,
            fill: "#666",
            font: "bold 13px Microsoft YaHei"
          },
          left: "center",
          top: 40
        };

        chartInstance.setOption({
          graphic: [timeRangeText]
        });
      });
    }
  });
};
const analyzing = ref(false);
const analysisResult = ref("");

// 日志分析弹窗相关变量
const analysisDialogVisible = ref(false);
const analysisLoading = ref(false);
const promptList = ref<any[]>([]);
const analysisForm = reactive({
  promptName: "",
  promptContent: ""
});
// 根据日志级别生成不同的系统提示
const getSystemPrompt = (level: string) => {
  const prompts: { [key: string]: string } = {
    ERROR: "请分析以下错误日志，指出引发错误的原因并提供解决方案，使用简洁的技术语言：",
    WARN: "请分析以下警告日志，说明潜在风险并提出预防措施，使用简洁的技术语言：",
    INFO: "请总结以下信息日志的关键内容，提取重要上下文信息，使用简洁的技术语言：",
    DEBUG: "请解析以下调试日志，说明程序执行状态并提出排查建议，使用简洁的技术语言："
  };
  return prompts[level] || "请分析以下日志信息：";
};
// 处理详情分析
const handleAnalyzeInDetail = async (log: LogType) => {
  showAnalysis.value = true;
  analyzing.value = true;
  analysisResult.value = "";
  try {
    const res = await deepseekChat({
      model: "deepseek-chat",
      messages: [
        {
          role: "system",
          content: getSystemPrompt(log.level)
        },
        {
          role: "user",
          content: log.message
        }
      ]
    });

    if (res.code === 0 && res.entity?.choices?.[0]?.message?.content) {
      analysisResult.value = res.entity.choices[0].message.content;
    } else {
      ElMessage.error("分析失败，请重试");
    }
  } catch (error) {
    console.error("分析失败:", error);
    ElMessage.error("分析请求失败");
  } finally {
    analyzing.value = false;
  }
};
const metadataConditions = ref<string[]>([]);
const pageParams = reactive({
  query: "*",
  level: "",
  message: "",
  sourceIp: "",
  sourceName: "",
  sourceType: "",
  pageNum: 1,
  pageSize: 10,
  startTime: "",
  endTime: "",
  metadata: {} as Record<string, string>
});
const addMetadataCondition = (key: string) => {
  if (!metadataConditions.value.includes(key)) {
    metadataConditions.value.push(key);
    pageParams.metadata[key] = "";
  }
};
const fillMetadataField = (key: string, value: string) => {
  if (!metadataConditions.value.includes(key)) {
    addMetadataCondition(key);
  }
  pageParams.metadata[key] = value;
};

const tableData = ref([]);
const total = ref(0);
const tableLoading = ref(false);
const route = useRoute();
interface TopTableItem {
  key: string;
  count: number;
}

const topTableData = ref<TopTableItem[]>([]);
const topTotal = ref(0);
const topTableLoading = ref(false);
//清空
function resetSearch() {
  pageParams.level = "";
  pageParams.message = "";
  pageParams.sourceIp = "";
  pageParams.sourceName = "";
  pageParams.sourceType = "";
  metadataConditions.value = [];
  pageParams.metadata = {};
}

// 关键词搜索
const fillSearch = (val: string) => {
  pageParams.message = val;
  search();
};
//搜索
function search() {
  loadData();
  loadChart();
}

function handleSizeChange(val: number) {
  pageParams.pageSize = val;
  loadData();
}

function handleCurrentChange(val: number) {
  pageParams.pageNum = val;
  loadData();
}
//列表数据
async function loadData() {
  try {
    tableLoading.value = true;
    const bodyData = {
      level: pageParams.level?.trim() || "",
      message: pageParams.message?.trim() || "",
      sourceIp: pageParams.sourceIp?.trim() || "",
      sourceName: pageParams.sourceName?.trim() || "",
      sourceType: pageParams.sourceType?.trim() || "",
      startTime: pageParams.startTime,
      endTime: pageParams.endTime,
      metadata: Object.keys(pageParams.metadata).reduce(
        (acc, key) => {
          if (pageParams.metadata[key]) {
            acc[key] = pageParams.metadata[key];
          }
          return acc;
        },
        {} as Record<string, string>
      )
    };
    const queryData = {
      query: pageParams.query?.trim() || "*"
    };
    const response = await logSearch(bodyData, queryData, pageParams.pageNum, pageParams.pageSize);
    if (response.code === 0) {
      tableData.value = (response.entity.records || []).map((item: any) => ({
        ...item,
        timestamp: formatTime(item.timestamp)
      }));
      total.value = parseInt(response.entity.total) || 0;
    }
  } catch (error) {
    console.error("请求失败:", error);
  } finally {
    tableLoading.value = false;
  }
}
//图表数据
async function loadChart() {
  try {
    chartLoading.value = true;
    const bodyData = {
      level: pageParams.level?.trim() || "",
      message: pageParams.message?.trim() || "",
      sourceIp: pageParams.sourceIp?.trim() || "",
      sourceName: pageParams.sourceName?.trim() || "",
      sourceType: pageParams.sourceType?.trim() || "",
      metadata: Object.keys(pageParams.metadata).reduce(
        (acc, key) => {
          if (pageParams.metadata[key]) {
            acc[key] = pageParams.metadata[key];
          }
          return acc;
        },
        {} as Record<string, string>
      )
    };
    const queryData = {
      query: pageParams.query?.trim() || "*"
    };
    const response = await logSearchChart(bodyData, queryData);
    if (response.code === 0) {
      const rawData = response.entity.dateHistogramAggregate;
      timestamps.value = rawData.map((item: any) => formatFullTime(parseInt(item.timestamp)));
      logCounts.value = rawData.map((item: any) => parseInt(item.count));
      nextTick(() => initChart());
    }
  } catch (error) {
    console.error("请求失败:", error);
  } finally {
    chartLoading.value = false;
  }
}
const sourceTypes = ref<string[]>([]);
//来源类型下拉框
async function sourceTypeList() {
  try {
    const queryData = {
      query: pageParams.query?.trim() || "*"
    };
    const response = await getSourceTypeList(queryData);
    if (response.code === 0) {
      sourceTypes.value = response.entity.sourceTypeList;
    }
  } catch (error) {
    console.log(error);
  }
}
const level = ref<string[]>([]);
//日志级别下拉框
async function levelList() {
  try {
    const queryData = {
      query: pageParams.query?.trim() || "*"
    };
    const response = await getLevelList(queryData);
    if (response.code === 0) {
      level.value = response.entity.sourceTypeList;
    }
  } catch (error) {
    console.log(error);
  }
}
//复制
const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ElMessage.success("复制成功");
      })
      .catch(() => {
        execCommandCopy(text);
      });
  } else {
    execCommandCopy(text);
  }
};
// 传统复制方法
const execCommandCopy = (text: string) => {
  try {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed";
    textarea.style.opacity = "0";
    document.body.appendChild(textarea);
    textarea.select();
    const success = document.execCommand("copy");
    document.body.removeChild(textarea);
    if (success) {
      ElMessage.success("复制成功");
    } else {
      throw new Error("复制失败，请手动复制");
    }
  } catch (err) {
    ElMessage.error("复制失败，请手动复制");
  }
};

const fillSearchField = (field: string, value: string) => {
  (pageParams as any)[field] = value;
};

// 日志top10数据
const getTopLogData = async () => {
  try {
    topTableLoading.value = true;

    const queryData = {
      query: pageParams.query?.trim() || "*",
      startTime: pageParams.startTime,
      endTime: pageParams.endTime
    };
    const response = await getTop10Logs(queryData);
    if (response.code === 0) {
      topTableData.value = response.entity.keywordCountList;
      topTotal.value = parseInt(response.entity.total) || 0;
    }
  } catch (error) {
    console.error("请求失败:", error);
  } finally {
    topTableLoading.value = false;
  }
};

// 异步日志分析
// const analyzeLogs = async () => {
//   const queryData = {
//     query: pageParams.query?.trim() || "*"
//   };
//   const bodyData = {
//     level: pageParams.level?.trim() || "",
//     message: pageParams.message?.trim() || "",
//     sourceIp: pageParams.sourceIp?.trim() || "",
//     sourceName: pageParams.sourceName?.trim() || "",
//     sourceType: pageParams.sourceType?.trim() || "",
//     startTime: pageParams.startTime,
//     endTime: pageParams.endTime,
//     metadata: Object.keys(pageParams.metadata).reduce(
//       (acc, key) => {
//         if (pageParams.metadata[key]) {
//           acc[key] = pageParams.metadata[key];
//         }
//         return acc;
//       },
//       {} as Record<string, string>
//     )
//   };

//   try {
//     const res = await exportLogAnalysisReport(queryData, bodyData);

//     if (res.code === 0) {
//       ElMessage.success(`${res.entity.message} 请稍后跳转到日志分析页面查看`);
//     }
//   } catch (error) {
//     ElMessage.error("分析请求失败");
//   }
// };
const hoverIndex = ref<number | null>(null);
const colorList = [
  "#409EFF",
  "#67C23A",
  "#E6A23C",
  "#F56C6C",
  "#909399",
  "#13C2C2",
  "#FF85C0",
  "#2F54EB",
  "#52C41A",
  "#FA541C"
];

// 检查是否有时间范围
const hasTimeRange = computed(() => {
  return !!(pageParams.startTime && pageParams.endTime);
});

// 提示词名称变化时的联动
const handlePromptChange = (promptName: string) => {
  if (promptName) {
    const selectedPrompt = promptList.value.find(item => item.name === promptName);
    if (selectedPrompt) {
      analysisForm.promptContent = selectedPrompt.content;
    }
  } else {
    analysisForm.promptContent = "";
  }
};

// 确认分析
const confirmAnalysis = () => {
  if (!analysisForm.promptName) {
    ElMessage.warning("请选择提示词名称");
    return;
  }

  // 跳转到日志分析页面，并传递aiPrompt参数
  router.push({
    path: "/logMonitoring/LogAnalysis",
    query: {
      startSSE: "true",
      startTime: pageParams.startTime,
      endTime: pageParams.endTime,
      aiPrompt: analysisForm.promptContent
    }
  });

  analysisDialogVisible.value = false;
};

function getColor(index: number) {
  return colorList[index % colorList.length];
}
onMounted(() => {
  const { level, sourceType, sourceIp } = route.query;
  if (level) {
    pageParams.level = level as string;
  }
  if (sourceType) {
    pageParams.sourceType = sourceType as string;
  }
  if (sourceIp) {
    pageParams.sourceIp = sourceIp as string;
  }
  loadChart();
  // loadData();
  sourceTypeList();
  levelList();
  // getTopLogData();
});
</script>
<style lang="scss" scoped>
.log-container {
  display: flex;
  .seach-container {
    min-width: 260px;
    // height: 450px;
    height: auto;
    min-height: 200px;
    padding-bottom: 15px;
    border: #eee 1px solid;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }
  .table-wrapper {
    flex: 1;
    min-width: calc(100% - 270px);
    padding: 0 10px;
  }
  .chart-container {
    margin-bottom: 10px;
  }
}
.input-group {
  margin-bottom: 10px;
}
.log-content {
  line-height: 1.5;
  .clickable-field span {
    display: inline-block;
    margin-right: 8px;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
}
.log-label {
  margin-left: 8px;
  padding: 2px 4px;
  background-color: #f0f5ff;
  border-radius: 2px;
  color: #3375f9;
  font-weight: 500;
}
.log-child-label {
  margin-left: 8px;
  padding: 2px 4px;
  background-color: #f0f5ff;
  border-radius: 2px;
  color: #94b6f8;
  font-weight: 500;
}
.label {
  margin-left: 8px;
  padding: 2px 4px;
  font-weight: 500;
}
.action-link {
  margin-left: 10px;
  cursor: pointer;
  color: #3375f9;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
.clickable-field {
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.content-text {
  cursor: pointer;
  transition: all 0.3s;
}

.analysis-result {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin-top: 12px;

  .result-title {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin-bottom: 12px;
  }

  .result-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
    color: #606266;
  }
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.chart-container {
  margin-bottom: 20px;
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.detail-table {
  width: 100%;
  max-width: 1200px;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}
.detail-table tr td {
  padding: 15px;
  border: 1px solid #ebeef5;
}
.detail-table tr {
  width: 200px;
}
.detail-table tr td.label {
  background: #f5f7fa;
  width: 120px;
  min-width: 120px;
  word-break: break-all;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.detail-table .content-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 8px;
}

.detail-table .content-text {
  cursor: pointer;
  flex: 1;
  word-break: break-all;
  min-width: 0;
}

.detail-table .action-link {
  flex-shrink: 0;
  white-space: nowrap;
  color: #0064c8;
  cursor: pointer;
  margin-left: auto;
}
.expanded-content {
  padding: 0 50px;
  .expanded-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
    .message-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .message-content {
      padding-left: 8px;
      word-break: break-all;
      line-height: 1.5;
    }
  }
}
.table-search {
  display: flex;
  justify-content: space-between;
}
</style>
