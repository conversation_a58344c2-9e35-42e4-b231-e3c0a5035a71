<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator :value="totalLogs" :unit="'日志总数量'"></Indicator>
      <Indicator :value="infoLogs" :unit="'INFO日志数量'" :color="'#78bf75'"></Indicator>
      <Indicator :value="errorLogs" :unit="'ERROR日志数量'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="warnLogs" :unit="'WARN日志数量'" :color="'#E6A23C'"></Indicator>
    </div>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts :options="timeHistogramOptions" height="250px" v-loading="timeHistogramOLoading" />
    <BaseEcharts
      :options="timeHistogramInfoOptions"
      height="250px"
      v-loading="timeHistogramOLoading"
    />
    <BaseEcharts
      :options="timeHistogramErrorOptions"
      height="250px"
      v-loading="timeHistogramOLoading"
    />
  </div>
  <div class="flex justify-between mt-10px indicator-wrapper">
    <Ranking
      v-loading="levelStatsLoading"
      class="w-100%"
      :title="'日志级别排行（Top5）'"
      :rankingList="levelRankingList"
      @itemClick="handleLevelClick"
    ></Ranking>
    <Ranking
      v-loading="sourceTypeStatsLoading"
      class="w-100%"
      :title="'来源类型排行（Top5）'"
      :rankingList="sourceTypeRankingList"
      @itemClick="handleSourceTypeClick"
    ></Ranking>
    <Ranking
      v-loading="sourceIpStatsLoading"
      class="w-100%"
      :title="'来源IP排行（Top5）'"
      :rankingList="sourceIpRankingList"
      @itemClick="handleSourceIpClick"
    ></Ranking>
  </div>
</template>
<script setup lang="ts">
import { logsOverview } from "@/api/log/index";
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import Ranking from "@/components/Ranking/index.vue";
import { useRouter } from "vue-router";
import { formatTime } from "@/utils/dateStr";

interface StatsItem {
  key: string;
  count: string;
}

interface TimeHistogramItem {
  time: string;
  timestamp: string;
  count: string;
}

interface IRankItem {
  name: string;
  proportion: number;
  totalScore: number;
  color?: string;
  unit?: string;
}

const loading = ref(false);
const levelStatsOptions = ref({});
const sourceTypeStatsOptions = ref({});
const sourceIpStatsOptions = ref({});
const timeHistogramOptions = ref({});
const timeHistogramInfoOptions = ref({});
const timeHistogramErrorOptions = ref({});
const levelStatsLoading = ref(false);
const sourceTypeStatsLoading = ref(false);
const sourceIpStatsLoading = ref(false);
const timeHistogramOLoading = ref(false);
const totalLogs = ref("0");
const infoLogs = ref("0");
const errorLogs = ref("0");
const warnLogs = ref("0");

const levelRankingList = ref<IRankItem[]>([]);
const sourceTypeRankingList = ref<IRankItem[]>([]);
const sourceIpRankingList = ref<IRankItem[]>([]);

const router = useRouter();

// 处理日志级别统计数据
const processLevelStats = (data: StatsItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => item.key);
  levelStatsOptions.value = getChartOptions({
    color: ["#5470c6"],
    titleType: "日志级别统计",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "bar"
  });

  // 更新各类型日志数量
  data.forEach(item => {
    switch (item.key) {
      case "INFO":
        infoLogs.value = item.count;
        break;
      case "ERROR":
        errorLogs.value = item.count;
        break;
      case "WARN":
        warnLogs.value = item.count;
        break;
    }
  });

  // 计算总日志数量
  totalLogs.value = data.reduce((sum, item) => sum + parseInt(item.count), 0).toString();

  // 处理排行榜数据
  let total = data.reduce((sum, item) => sum + parseInt(item.count), 0);
  levelRankingList.value = data.map(item => ({
    name: item.key,
    proportion: (parseInt(item.count) / total) * 100,
    totalScore: parseInt(item.count),
    color:
      item.key === "INFO"
        ? "#78bf75"
        : item.key === "ERROR"
          ? "#f56c6c"
          : item.key === "WARN"
            ? "#E6A23C"
            : "#5470c6",
    unit: ""
  }));
};

// 处理来源类型统计数据
const processSourceTypeStats = (data: StatsItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => item.key);
  sourceTypeStatsOptions.value = getChartOptions({
    color: ["#5470c6"],
    titleType: "来源类型统计",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "bar"
  });

  let total = data.reduce((sum, item) => sum + parseInt(item.count), 0);
  sourceTypeRankingList.value = data.map(item => ({
    name: item.key,
    proportion: (parseInt(item.count) / total) * 100,
    totalScore: parseInt(item.count),
    color: "#5470c6",
    unit: ""
  }));
};

// 处理来源IP统计数据
const processSourceIpStats = (data: StatsItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => item.key);
  sourceIpStatsOptions.value = getChartOptions({
    color: ["#5470c6"],
    titleType: "来源IP统计",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "bar"
  });

  let total = data.reduce((sum, item) => sum + parseInt(item.count), 0);
  sourceIpRankingList.value = data.map(item => ({
    name: item.key,
    proportion: (parseInt(item.count) / total) * 100,
    totalScore: parseInt(item.count),
    color: "#5470c6",
    unit: ""
  }));
};
// 新增时间戳格式化函数（根据你的逻辑）
const formatTimestamp = (timestampStr: string): string => {
  const timestamp = parseInt(timestampStr, 10);
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  // 判断是否为 00:00:00
  if (hours === 0 && minutes === 0 && seconds === 0) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }
  // 自定义时间格式补零
  const hh = String(hours).padStart(2, "0");
  const mm = String(minutes).padStart(2, "0");
  const ss = String(seconds).padStart(2, "0");
  return `${hh}:${mm}:${ss}`; // 输出时间：HH:MM:SS
};
// 处理时间直方图数据
const processTimeHistogram = (data: TimeHistogramItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => formatTime(item.time));
  timeHistogramOptions.value = getChartOptions({
    color: ["#5470c6"],
    titleType: "日志数量趋势",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "line",
    areaStyle: true,
    name: ""
  });
};

// 处理INFO日志时间直方图数据
const processTimeHistogramInfo = (data: TimeHistogramItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => formatTime(item.time));
  timeHistogramInfoOptions.value = getChartOptions({
    color: ["#78bf75"],
    titleType: "INFO日志数量趋势",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "line",
    areaStyle: true,
    name: ""
  });
};

// 处理ERROR日志时间直方图数据
const processTimeHistogramError = (data: TimeHistogramItem[]) => {
  const seriesData = [data.map(item => parseInt(item.count))];
  const originalTimes = data.map(item => formatTime(item.time));
  timeHistogramErrorOptions.value = getChartOptions({
    color: ["#f56c6c"],
    titleType: "ERROR日志数量趋势",
    originalTimes: originalTimes,
    seriesData: seriesData,
    type: "line",
    areaStyle: true,
    name: ""
  });
};

async function overview() {
  try {
    levelStatsLoading.value = true;
    sourceTypeStatsLoading.value = true;
    sourceIpStatsLoading.value = true;
    timeHistogramOLoading.value = true;
    loading.value = true;
    const response = await logsOverview();
    if (response.code === 0) {
      const {
        levelStats,
        sourceTypeStats,
        sourceIpStats,
        timeHistogram,
        timeHistogramInfo,
        timeHistogramError
      } = response.entity;
      processLevelStats(levelStats);
      processSourceTypeStats(sourceTypeStats);
      processSourceIpStats(sourceIpStats);
      processTimeHistogram(timeHistogram);
      processTimeHistogramInfo(timeHistogramInfo);
      processTimeHistogramError(timeHistogramError);
    }
    levelStatsLoading.value = false;
    sourceTypeStatsLoading.value = false;
    sourceIpStatsLoading.value = false;
    timeHistogramOLoading.value = false;
    loading.value = false;
  } catch (error) {
    levelStatsLoading.value = false;
    sourceTypeStatsLoading.value = false;
    sourceIpStatsLoading.value = false;
    timeHistogramOLoading.value = false;
    loading.value = false;
    console.log(error);
  }
}
// 处理日志级别点击
const handleLevelClick = (item: IRankItem) => {
  router.push({
    path: "/logMonitoring/logOriginal",
    query: {
      level: item.name
    }
  });
};

// 处理来源类型点击
const handleSourceTypeClick = (item: IRankItem) => {
  router.push({
    path: "/logMonitoring/logOriginal",
    query: {
      sourceType: item.name
    }
  });
};

// 处理来源IP点击
const handleSourceIpClick = (item: IRankItem) => {
  router.push({
    path: "/logMonitoring/logOriginal",
    query: {
      sourceIp: item.name
    }
  });
};

onMounted(() => {
  overview();
});
</script>
<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
