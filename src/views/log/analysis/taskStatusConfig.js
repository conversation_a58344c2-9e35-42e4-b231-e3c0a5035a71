// task-status-config.js
// 定义状态常量
export const TASK_STATUS = {
  PENDING: 0,
  RUNNING: 1,
  DONE: 2,
  FAILED: 3
};

export const statusMap = {
  [TASK_STATUS.PENDING]: { type: "warning", text: "PENDING" },
  [TASK_STATUS.RUNNING]: { type: "info", text: "RUNNING" },
  [TASK_STATUS.DONE]: { type: "success", text: "DONE" },
  [TASK_STATUS.FAILED]: { type: "danger", text: "FAILED" }
};

// 生成选择器选项（供el-select使用）
export const statusOptions = Object.keys(TASK_STATUS).map(key => ({
  label: key,
  value: TASK_STATUS[key]
}));
