<template>
  <div class="log-container">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="instanceId">IP地址：</label>
          <el-input
            id="instanceId"
            placeholder="请输入IP地址"
            v-model="pageParams.hostip"
            filterable
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">主机名称：</label>
          <el-input
            id="instanceId"
            placeholder="请输入主机名称"
            v-model="pageParams.hostname"
            filterable
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">日志内容：</label>
          <el-input
            id="instanceId"
            placeholder="请输入日志内容"
            v-model="pageParams.message"
            filterable
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">日志级别：</label>
          <el-input
            id="instanceId"
            placeholder="请输入日志级别"
            v-model="pageParams.severityText"
            filterable
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <MyTable
      :data="list.records"
      :total="list.total"
      pagination-layout="total,prev, next,"
      :pagination-background="true"
      class="tabel-container"
      v-loading="tableLoading"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      @sort-change="handleSortChange"
      :default-sort="{
        prop: 'time'
      }"
    >
      <MyColumn property="hostip" label="IP地址" />
      <MyColumn property="hostname" label="主机名称" />
      <MyColumn property="time" label="时间" sortable="custom" />
      <MyColumn property="type" label="类型">
        <template #default="scope">
          <span>{{ scope.row.type === 1 ? "服务器日志" : "未定义" }}</span>
        </template>
      </MyColumn>
      <MyColumn property="severityText" label="日志级别">
        <template #default="scope">
          <el-tag :type="scope.row.severityText === 'ERROR' ? 'danger' : 'success'">
            {{ scope.row.severityText }}
          </el-tag>
        </template>
      </MyColumn>
      <MyColumn property="message" label="日志内容" />
      <MyColumn label="操作" width="120" fixed="right" align="center" header-align="center">
        <template #default="scope">
          <span class="action-link" @click="shouType(scope.row)">详情</span>
        </template>
      </MyColumn>
    </MyTable>
  </div>
  <div>
    <el-drawer v-model="logVisible" title="日志详情" size="50%">
      <table class="detail-table">
        <tbody>
          <tr>
            <td class="label">应用ID</td>
            <td>{{ detailList.appid }}</td>
          </tr>
          <tr>
            <td class="label">时间</td>
            <td>{{ detailList.time }}</td>
          </tr>
          <tr>
            <td class="label">IP地址</td>
            <td>{{ detailList.hostip }}</td>
          </tr>
          <tr>
            <td class="label">主机名称</td>
            <td>{{ detailList.hostname }}</td>
          </tr>
          <tr>
            <td class="label">日志级别</td>
            <td>
              <el-tag :type="detailList.severityText === 'ERROR' ? 'danger' : 'success'">
                {{ detailList.severityText }}
              </el-tag>
            </td>
          </tr>
          <tr>
            <td class="label">日志内容</td>
            <td>{{ detailList.message }}</td>
          </tr>
        </tbody>
      </table>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { getLogList } from "@/api/log/index";
import { applicationStore } from "@/store/modules/application";
import { useRoute } from "vue-router";
const useApplicationStore = applicationStore();
const detailList = ref({
  appid: "",
  time: "",
  hostip: "",
  hostname: "",
  severityText: "",
  message: ""
});
const logVisible = ref(false);
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  hostip: "",
  hostname: "",
  message: "",
  severityText: "",
  sort: "",
  order: "",
  page: 1,
  rows: 10
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
//清空
function resetSearch() {
  pageParams.hostip = "";
  pageParams.hostname = "";
  pageParams.message = "";
  pageParams.severityText = "";
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  getLogList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
const shouType = (row: any) => {
  detailList.value = row;
  logVisible.value = true;
};
onMounted(() => {
  const route = useRoute();
  const { message } = route.query as {
    message?: string;
  };
  pageParams.message = pageParams.message = message ?? "";
  loadData();
});
</script>
<style lang="scss" scoped>
.log-container {
  display: flex;
  .search-container {
    min-width: 260px;
    height: 400px;
    border: #eee 1px solid;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.action-link {
  color: #0064c8;
  cursor: pointer;
}
.input-group {
  margin-bottom: 10px;
}

.detail-table {
  width: 100%;
  max-width: 1200px;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}
.detail-table tr td {
  padding: 15px;
  border: 1px solid #ebeef5;
  word-break: break-all;
}
.detail-table tr {
  width: 200px;
}
.detail-table tr td.label {
  background: #f5f7fa;
  width: 120px;
  min-width: 120px;
  word-break: break-all;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
</style>
