<template>
  <div class="title">
    <div class="mark"></div>
    <div>设备信息</div>
  </div>
  <div class="tb-header">
    <div>
      <el-select
        style="width: 240px"
        placeholder="请选择设备"
        v-model="pageParams.device"
        clearable
        filterable
      >
        <el-option label="service" value="service" />
        <el-option label="nginx" value="nginx" />
        <el-option label="host" value="host" />
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search">搜索</el-button>
    </div>
    <div>
      <el-button type="primary" @click="dialogFormVisible = true">添加设备</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="device" label="设备" />
    <my-column property="IP" label="IP地址" />
    <my-column property="time" label="最后消息时间" />
    <my-column property="status" label="状态">
      <template #default="scope">
        <el-tag :type="scope.row.status === 0 ? 'danger' : 'success'">
          {{ scope.row.status === 0 ? "未启动" : "运行中" }}
        </el-tag>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
      <template #default="scope">
        <span class="operate" @click="dialogFormVisible = true">编辑 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="deleteVisible = true">删除 </span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="新增设备"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="设备：">
        <el-select placeholder="请选择设备" v-model="state.form.device" clearable filterable>
          <el-option label="service" value="service" />
          <el-option label="nginx" value="nginx" />
          <el-option label="host" value="host" />
        </el-select>
      </el-form-item>
      <el-form-item label="IP地址：">
        <el-input placeholder="请输入IP地址" v-model="state.form.ip" clearable filterable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否启用：">
        <el-radio-group v-model="state.form.status">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="deleteVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
const state = reactive({
  form: {
    device: "",
    ip: "",
    status: ""
  }
});
const dialogFormVisible = ref(false);
const deleteVisible = ref(false);
const pageParams = reactive({
  device: ""
});
const list = reactive({
  records: [
    {
      device: "nginx",
      IP: "************",
      status: 1,
      time: "2025-04-28 14:13:13"
    },
    {
      device: "host",
      IP: "************",
      status: 0,
      time: "2025-4-28 14:51:08"
    },
    {
      device: "service",
      IP: "***********",
      status: 1,
      time: "2025-4-28 14:51:49"
    }
  ],
  total: 3
});
//修改每页条数
const handleSizeChange = (val: number) => {
  console.log(val);
};
//分页
const handleCurrentChange = (val: number) => {
  console.log(val);
};
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
