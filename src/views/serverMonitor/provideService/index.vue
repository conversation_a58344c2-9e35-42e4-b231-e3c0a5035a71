<template>
  <div class="log-container">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label>Endpoint：</label>
          <el-select
            placeholder="请选择Endpoint"
            v-model="pageParams.endpoint"
            clearable
            filterable
          >
            <el-option
              v-for="endpoint in endpointNames"
              :key="endpoint"
              :label="endpoint"
              :value="endpoint"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>请求类型：</label>
          <el-select
            placeholder="请选择请求类型"
            v-model="pageParams.requestType"
            clearable
            filterable
          >
            <el-option v-for="item in requestTypes" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper mb-10px">
        <BaseEcharts :options="options1" height="250px" v-loading="StatRequestCountLoading" />
        <BaseEcharts :options="options2" height="250px" v-loading="StatErrorCountLoading" />
        <BaseEcharts :options="options3" height="250px" v-loading="StatAvgCountLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{
          prop: 'requestCount && errorCount && errorRate && durationMs',
          order: 'descending'
        }"
        @sort-change="handleSortChange"
      >
        <my-column property="endpoint" label="Endpoint">
          <template #default="scope">
            <span
              class="vertical-line"
              :class="{
                'error-bg': scope.row.errorCount > 0,
                'success-bg': scope.row.errorCount === 0
              }"
            ></span>
            <span class="service-name">
              {{ scope.row.endpoint }}
            </span>
          </template>
        </my-column>
        <my-column property="requestType" label="请求类型" width="220" />
        <my-column property="requestCount" label="请求数（次）" sortable="custom" width="170">
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.requestCount).fixValue
              }}{{ formatNums(row.requestCount).unit || "" }}
            </span>
          </template>
        </my-column>
        <my-column property="errorCount" label="错误数（次）" sortable="custom" width="170" />
        <my-column property="errorRate" label="错误率" sortable="custom" width="170">
          <template v-slot="{ row }">
            <span> {{ row.errorRate }}% </span>
          </template>
        </my-column>
        <my-column property="durationMs" label="平均耗时" sortable="custom" width="170">
          <template v-slot="{ row }">
            <span> {{ row.durationMs }}ms </span>
          </template>
        </my-column>
        <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
          <template #default="scope">
            <!-- <span class="operate">详情 </span>
            <span class="divider"> / </span>
            <span class="operate">SQL分析 </span>
            <span class="divider"> / </span>
            <span class="operate">NoSQL分析</span>
            <span class="divider"> / </span> -->
            <span class="operate" @click="handleCallChain(scope.row)">调用链</span>
          </template>
        </my-column>
      </MyTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { formatNums } from "@/utils/formatStr";
import {
  getServiceTraces,
  getRequestTypes,
  getEndpointNames,
  getStatRequestCount,
  getStatErrorCount,
  getStatAvgDuration
} from "@/api/service/serviceList";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const router = useRouter();
import { useRouter } from "vue-router";
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
const StatRequestCountLoading = ref(false); //请求统计loading
const StatErrorCountLoading = ref(false); //错误统计loading
const StatAvgCountLoading = ref(false); //耗时统计loading
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  serviceName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  endpoint: "",
  requestType: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//跳转到调用链
const handleCallChain = (row: any) => {
  router.push({
    path: "/dashboard/overview/Call-chain",
    query: {
      endpoint: row.endpoint
    }
  });
};
//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
}
//清空
function resetSearch() {
  pageParams.endpoint = "";
  pageParams.requestType = "";
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.serviceName = useServiceNameStore.serviceName;
  pageParams.appid = useApplicationStore.appId;
  getServiceTraces(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const requestTypes = ref([]);
const dataToSend = reactive({
  appid: "",
  serviceName: ""
});
//请求类型
function getRequestType() {
  dataToSend.serviceName = useServiceNameStore.serviceName;
  dataToSend.appid = useApplicationStore.appId;
  getRequestTypes(dataToSend).then(response => {
    if (response.code === 0) {
      requestTypes.value = response.records;
    }
  });
}
const endpointNames = ref([]);
const params = reactive({
  appid: "",
  serviceName: ""
});
//接口名称
function getEndpointName() {
  params.serviceName = useServiceNameStore.serviceName;
  params.appid = useApplicationStore.appId;
  getEndpointNames(params).then(response => {
    if (response.code === 0) {
      endpointNames.value = response.records;
    }
  });
}
const options1 = ref({});
const options2 = ref({});
const options3 = ref({});
function StatRequestCount() {
  StatRequestCountLoading.value = true;
  getStatRequestCount({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName,
    requestType: pageParams.requestType,
    endpoint: pageParams.endpoint
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "请求数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options1.value = getChartOptions(params);
        StatRequestCountLoading.value = false;
      }
    })
    .catch(error => {
      StatRequestCountLoading.value = false;
      console.log(error);
    });
}
function StatErrorCount() {
  StatErrorCountLoading.value = true;
  getStatErrorCount({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName,
    requestType: pageParams.requestType,
    endpoint: pageParams.endpoint
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#f56c6c"],
          titleType: "错误数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options2.value = getChartOptions(params);
        StatErrorCountLoading.value = false;
      }
    })
    .catch(error => {
      StatErrorCountLoading.value = false;
      console.log(error);
    });
}
function StatAvgCount() {
  StatAvgCountLoading.value = true;
  getStatAvgDuration({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName,
    requestType: pageParams.requestType,
    endpoint: pageParams.endpoint
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " ms",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "平均耗时",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        options3.value = getChartOptions(params);
        StatAvgCountLoading.value = false;
      }
    })
    .catch(error => {
      StatAvgCountLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  const route = useRoute();
  const { endpoint } = route.query as {
    endpoint?: string;
  };
  pageParams.endpoint = pageParams.endpoint = endpoint ?? "";
  loadData();
  getRequestType();
  getEndpointName();
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
});
</script>

<style lang="scss" scoped>
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  // cursor: pointer;
  // color: #0064c8;
  vertical-align: middle;
}
</style>
