<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="pageParams.serviceName"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'requestCount && errorCount && errorRate && avgDuration',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="serviceName" label="服务名称">
      <template #default="scope">
        <span
          class="vertical-line"
          :class="{
            'error-bg': scope.row.errorCount > 0,
            'success-bg': scope.row.errorCount === 0
          }"
        ></span>
        <span class="service-name" @click="handleServiceTop(scope)">
          {{ scope.row.serviceName }} {{ scope.row.alias ? ` （${scope.row.alias}）` : "" }}
        </span>
      </template>
    </my-column>
    <my-column property="requestCount" label="请求数（次）" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.requestCount).fixValue }}{{ formatNums(row.requestCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="errorCount" label="错误数（次）" sortable="custom" />
    <my-column property="errorRate" label="错误率" sortable="custom">
      <template v-slot="{ row }">
        <span> {{ row.errorRate }}% </span>
      </template>
    </my-column>
    <my-column property="avgDuration" label="平均耗时" sortable="custom">
      <template v-slot="{ row }">
        <span> {{ row.avgDuration }}ms </span>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="250">
      <template #default="scope">
        <span class="operate" @click="handleServiceTop(scope)">详情 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="handleCallChain(scope.row)">调用链 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="handlequeryLog(scope.row)">日志分析</span>
        <span class="divider"> / </span>
        <span class="operate" @click="editAlias(scope.row)">编辑别名</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="编辑别名"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="服务名称：">
        <el-input v-model="state.form.serviceName" disabled></el-input>
      </el-form-item>
      <el-form-item label="别名：">
        <el-input v-model="state.form.alias" placeholder="请输入别名" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAlias">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { formatNums } from "@/utils/formatStr";
import { getServiceList, saveServiceAlias } from "@/api/service/serviceList";
import { serviceNameStore } from "@/store/modules/service";
import { useRouter } from "vue-router";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const router = useRouter();
const useServiceNameStore = serviceNameStore();
const dialogFormVisible = ref(false);
const state = reactive({
  form: {
    serviceName: "",
    alias: ""
  }
});
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  serviceName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.serviceName = pageParams.serviceName.trim();
  pageParams.appid = useApplicationStore.appId;
  getServiceList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//页面跳转
function handleServiceTop(scope: any) {
  useServiceNameStore.setServiceName(scope.row.serviceName);
  useServiceNameStore.setAlias(scope.row.alias || "");
  router.push({
    path: "/dashboard/overview/service-overview"
  });
}
//跳转到调用链
const handleCallChain = (row: any) => {
  router.push({
    path: "/dashboard/CallChain",
    query: {
      serviceName: row.serviceName
    }
  });
};
//跳转到日志分析
const handlequeryLog = (row: any) => {
  router.push({
    path: "/dashboard/querylog",
    query: {
      serviceName: row.serviceName
    }
  });
};
const editAlias = (row: any) => {
  dialogFormVisible.value = true;
  state.form.serviceName = row.serviceName;
  state.form.alias = row.alias || "";
};

const handleSaveAlias = () => {
  saveServiceAlias({
    serviceName: state.form.serviceName,
    alias: state.form.alias
  }).then(res => {
    if (res.code === 0) {
      ElMessage.success("保存成功");
      dialogFormVisible.value = false;
      loadData();
    } else {
      ElMessage.error(res.desc);
    }
  });
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
