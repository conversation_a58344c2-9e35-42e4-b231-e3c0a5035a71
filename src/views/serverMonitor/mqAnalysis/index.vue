<template>
  <div class="log-container">
    <div class="search-container">
      <el-form :model="filterForm">
        <el-form-item class="input-group">
          <label for="requestType">请求类型：</label>
          <el-select
            id="requestType"
            placeholder="请选择请求类型"
            v-model="filterForm.requestType"
            clearable
            filterable
          >
            <el-option
              v-for="item in requestTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="queueName">队列名称：</label>
          <el-select
            id="queueName"
            placeholder="请选择队列名称"
            v-model="filterForm.queueName"
            clearable
            filterable
          >
            <el-option
              v-for="item in queueNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            @confirm="resetSearch"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" @click="executeSearch" :icon="Search" style="flex: 1">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper mb-10px">
        <BaseEcharts :options="MQStatRequestData" height="250px" v-loading="statRequestLoading" />
        <BaseEcharts :options="MQStatErrorData" height="250px" v-loading="statErrorLoading" />
        <BaseEcharts :options="MQStatAvgData" height="250px" v-loading="statAvgLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{
          prop: 'requestCount && errorCount && errorRate && durationMs',
          order: 'descending'
        }"
        @sort-change="handleSortChange"
      >
        <my-column property="queueName" label="队列名称" width="300">
          <template v-slot="{ row }">
            <span
              class="vertical-line"
              :class="{
                'error-bg': row.errorCount > 0,
                'success-bg': row.errorCount === 0
              }"
            ></span>
            <span class="service-name">
              {{ row.queueName }}
            </span>
          </template>
        </my-column>
        <my-column property="type" label="生产者 / 消费者">
          <template v-slot="{ row }">
            <span>{{ row.type === 0 ? "生产者" : "消费者" }}</span>
          </template>
        </my-column>
        <my-column property="requestType" label="请求类型" />
        <my-column property="requestCount" label="请求数（次）" sortable="custom">
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.requestCount).fixValue
              }}{{ formatNums(row.requestCount).unit || "" }}
            </span>
          </template>
        </my-column>
        <my-column property="errorCount" label="错误数（次）" sortable="custom" />
        <my-column property="errorRate" label="错误率" sortable="custom">
          <template v-slot="{ row }">
            <span> {{ row.errorRate }}% </span>
          </template>
        </my-column>
        <my-column property="durationMs" label="平均耗时" sortable="custom">
          <template v-slot="{ row }">
            <span> {{ row.durationMs }}ms </span>
          </template>
        </my-column>
        <!-- <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
          <template v-slot:default>
            <span class="operate" @click="handleCallChainClick(row)">调用链</span>
          </template>
        </my-column> -->
      </MyTable>
    </div>
  </div>
</template>
<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { formatNums } from "@/utils/formatStr";
import {
  getMqList,
  getMqTypes,
  getMqQueues,
  getMQStatRequestCount,
  getMQStatErrorCount,
  getMQStatAvgDuration
} from "@/api/service/sql";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { useRouter } from "vue-router";
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
//loading动画
const tableLoading = ref(true);
const pageParams = reactive({
  appid: "",
  serviceName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  queueName: "",
  requestType: ""
});
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.serviceName = useServiceNameStore.serviceName;
  pageParams.queueName = filterForm.queueName;
  pageParams.requestType = filterForm.requestType;
  pageParams.appid = useApplicationStore.appId;
  getMqList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const requestTypeOptions = ref([]);
const queueNameOptions = ref([]);
// 队列下拉框选项
const fetchgetMqQueues = async () => {
  const queue = {
    queueName: filterForm.queueName,
    requestType: filterForm.requestType,
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  };
  try {
    const res = await getMqQueues(queue);
    if (res.code === 0 && res.records) {
      queueNameOptions.value = res.records.map((item: any) => {
        return { value: item, label: item };
      });
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
// 请求类型下拉框选项
const fetchgetMqTypes = async () => {
  const queue = {
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  };
  try {
    const res = await getMqTypes(queue);
    if (res.code === 0 && res.records) {
      requestTypeOptions.value = res.records.map((item: any) => {
        return { value: item, label: item };
      });
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const filterForm = reactive({
  queueName: "",
  requestType: ""
});
const appliedFilterForm = reactive({
  queueName: "",
  requestType: ""
});
const MQStatRequestData = ref({});
const MQStatErrorData = ref({});
const MQStatAvgData = ref({});
const statRequestLoading = ref(false);
const statErrorLoading = ref(false);
const statAvgLoading = ref(false);
const fetchgetStatRequestCount = async () => {
  statRequestLoading.value = true;
  const queue = {
    queueName: filterForm.queueName,
    requestType: filterForm.requestType,
    serviceName: useServiceNameStore.serviceName,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getMQStatRequestCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        typ: res.entity.granularity,
        color: ["#78bf75"],
        titleType: "请求数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      MQStatRequestData.value = getChartOptions(params);
      statRequestLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statRequestLoading.value = false;
  }
};
const fetchgetStatErrorCount = async () => {
  statErrorLoading.value = true;
  const queue = {
    queueName: filterForm.queueName,
    requestType: filterForm.requestType,
    serviceName: useServiceNameStore.serviceName,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getMQStatErrorCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        typ: res.entity.granularity,
        color: ["#f56c6c"],
        titleType: "错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      MQStatErrorData.value = getChartOptions(params);
      statErrorLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statErrorLoading.value = false;
  }
};

const fetchgetStatAvgDuration = async () => {
  statAvgLoading.value = true;
  const queue = {
    queueName: filterForm.queueName,
    requestType: filterForm.requestType,
    serviceName: useServiceNameStore.serviceName,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getMQStatAvgDuration(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const names = ["平均耗时"];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        names: names,
        titleType: "平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      MQStatAvgData.value = getChartOptions(params);
      statAvgLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statAvgLoading.value = false;
  }
};

// 跳转
const router = useRouter();
const handleCallChainClick = (row: any) => {
  const { sql } = row;
  // router.push({
  //   name: "callChainNoServie",
  //   query: {
  //     sql
  //   }
  // });
};

// 清空条件查询
const resetSearch = () => {
  for (const key in filterForm) {
    filterForm[key] = "";
  }
  loadData();
};
const executeSearch = () => {
  pageParams.sort = "";
  pageParams.order = "";
  pageParams.page = 1;
  Object.assign(appliedFilterForm, filterForm);
  loadData();
  fetchgetStatRequestCount();
  fetchgetStatErrorCount();
  fetchgetStatAvgDuration();
};
onMounted(() => {
  loadData();
  fetchgetMqQueues();
  fetchgetMqTypes();
  fetchgetStatRequestCount();
  fetchgetStatErrorCount();
  fetchgetStatAvgDuration();
});
</script>
<style lang="scss" scoped>
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  // cursor: pointer;
  // color: #0064c8;
  vertical-align: middle;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
