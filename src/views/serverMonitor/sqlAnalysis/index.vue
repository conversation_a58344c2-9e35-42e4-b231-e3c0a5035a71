<template>
  <div class="log-container">
    <div class="search-container">
      <el-form :model="pageParams">
        <el-form-item class="input-group">
          <label>数据库名称：</label>
          <el-select
            placeholder="请选择数据库名称"
            v-model="pageParams.dbName"
            clearable
            filterable
          >
            <el-option v-for="item in dbName" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>数据库类型：</label>
          <el-select
            placeholder="请选择数据库类型"
            v-model="pageParams.dbSystem"
            clearable
            filterable
          >
            <el-option v-for="item in dbType" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>SQL语句：</label>
          <el-input placeholder="请输入SQL语句" v-model="pageParams.sql" clearable></el-input>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            @confirm="resetSearch"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" @click="search" :icon="Search" style="flex: 1">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper mb-10px">
        <BaseEcharts :options="StatRequestData" height="250px" v-loading="statRequestLoading" />
        <BaseEcharts :options="StatErrorData" height="250px" v-loading="statErrorLoading" />
        <BaseEcharts :options="StatAvgData" height="250px" v-loading="statAvgLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{
          prop: 'requestCount && errorCount && errorRate && durationMs && slowCount',
          order: 'descending'
        }"
        @sort-change="handleSortChange"
      >
        <my-column property="sql" label="SQL语句" width="300">
          <template #default="{ row }">
            <span
              class="vertical-line"
              :class="{
                'error-bg': row.errorCount > 0,
                'success-bg': row.errorCount === 0
              }"
            ></span>
            <span style="min-width: 300px">
              {{ row.sql }}
            </span>
          </template>
        </my-column>
        <my-column property="dbName" label="数据库名称" width="250" />
        <my-column property="dbSystem" label="数据库类型" width="150" />
        <my-column property="requestCount" label="请求数（次）" sortable="custom" width="150">
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.requestCount).fixValue
              }}{{ formatNums(row.requestCount).unit || "" }}
            </span>
          </template>
        </my-column>
        <my-column property="slowCount" label="慢SQL数（次）" sortable="custom" width="150" />
        <my-column property="errorCount" label="错误数（次）" sortable="custom" width="150" />
        <my-column property="errorRate" label="错误率" sortable="custom" width="150">
          <template v-slot="{ row }">
            <span> {{ row.errorRate }}% </span>
          </template>
        </my-column>
        <my-column property="durationMs" label="平均耗时" sortable="custom" width="150">
          <template v-slot="{ row }">
            <span> {{ row.durationMs }}ms </span>
          </template>
        </my-column>
        <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
          <template #default="{ row }">
            <span class="operate" @click="handleCallChainClick(row)">调用链</span>
          </template>
        </my-column>
      </MyTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseEcharts from "@/components/baseEcharts/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { formatNums } from "@/utils/formatStr";
import {
  getSqlList,
  getSqlNames,
  getSqlTypes,
  getStatRequestCount,
  getStatErrorCount,
  getStatAvgDuration
} from "@/api/service/sql";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { useRouter } from "vue-router";

const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  serviceName: "",
  page: 1,
  rows: 10,
  type: 1,
  sort: "",
  order: "",
  dbName: "",
  dbSystem: "",
  sql: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
  fetchgetStatRequestCount();
  fetchgetStatErrorCount();
  fetchgetStatAvgDuration();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.serviceName = useServiceNameStore.serviceName;
  pageParams.sql = pageParams.sql.trim();
  pageParams.appid = useApplicationStore.appId;
  getSqlList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//数据库名称
const dbName = ref([]);
const dataToSend = reactive({
  appid: "",
  serviceName: "",
  type: 1
});
function getSqlName() {
  dataToSend.serviceName = useServiceNameStore.serviceName;
  dataToSend.appid = useApplicationStore.appId;
  getSqlNames(dataToSend).then(response => {
    if (response.code === 0) {
      dbName.value = response.records;
    }
  });
}
//数据库类型
const dbType = ref([]);
const params = reactive({
  appid: "",
  serviceName: "",
  type: 1
});
function getSqlType() {
  params.serviceName = useServiceNameStore.serviceName;
  params.appid = useApplicationStore.appId;
  getSqlTypes(params).then(response => {
    if (response.code === 0) {
      dbType.value = response.records;
    }
  });
}

// 图表
const StatRequestData = ref({});
const StatErrorData = ref({});
const StatAvgData = ref({});
const statRequestLoading = ref(false);
const statErrorLoading = ref(false);
const statAvgLoading = ref(false);
const fetchgetStatRequestCount = async () => {
  statRequestLoading.value = true;
  const queue = {
    type: 1,
    sql: pageParams.sql.trim(),
    appid: useApplicationStore.appId,
    dbSystem: pageParams.dbSystem,
    dbName: pageParams.dbName,
    serviceName: useServiceNameStore.serviceName
  };
  try {
    const res = await getStatRequestCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        typ: res.entity.granularity,
        color: ["#78bf75"],
        titleType: "请求数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      StatRequestData.value = getChartOptions(params);
      statRequestLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statRequestLoading.value = false;
  }
};
const fetchgetStatErrorCount = async () => {
  statErrorLoading.value = true;
  const queue = {
    type: 1,
    sql: pageParams.sql.trim(),
    appid: useApplicationStore.appId,
    dbSystem: pageParams.dbSystem,
    dbName: pageParams.dbName,
    serviceName: useServiceNameStore.serviceName
  };
  try {
    const res = await getStatErrorCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        typ: res.entity.granularity,
        color: ["#f56c6c"],
        titleType: "错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      StatErrorData.value = getChartOptions(params);
      statErrorLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statErrorLoading.value = false;
  }
};
const fetchgetStatAvgDuration = async () => {
  statAvgLoading.value = true;
  const queue = {
    type: 1,
    sql: pageParams.sql.trim(),
    appid: useApplicationStore.appId,
    dbSystem: pageParams.dbSystem,
    dbName: pageParams.dbName,
    serviceName: useServiceNameStore.serviceName
  };
  try {
    const res = await getStatAvgDuration(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        name: " ms",
        typ: res.entity.granularity,
        color: ["#78bf75"],
        titleType: "平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      StatAvgData.value = getChartOptions(params);
      statAvgLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    statAvgLoading.value = false;
  }
};
// 跳转
const router = useRouter();
const handleCallChainClick = (row: any) => {
  const { sql } = row;
  router.push({
    name: "callChainNoServie",
    query: {
      sql
    }
  });
};
// 清空条件查询
const resetSearch = () => {
  pageParams.dbName = "";
  pageParams.dbSystem = "";
  pageParams.sql = "";
  loadData();
};

onMounted(() => {
  loadData();
  getSqlName();
  getSqlType();
  fetchgetStatRequestCount();
  fetchgetStatErrorCount();
  fetchgetStatAvgDuration();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  // cursor: pointer;
  // color: #0064c8;
  vertical-align: middle;
}

// 新增
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    max-height: 320px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
