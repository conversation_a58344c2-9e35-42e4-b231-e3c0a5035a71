<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator :value="getRequestCountData.value" :unit="'请求数（次）'"></Indicator>
      <Indicator
        :value="errorCountData.value"
        :unit="'错误数（次）'"
        :color="errorCountData.color"
      ></Indicator>
      <Indicator
        :value="errorLogCountData.value"
        :unit="'错误日志数'"
        :color="errorLogCountData.color"
      ></Indicator>
      <Indicator :value="avgUdrationData.value" :unit="'平均耗时（ms）'"></Indicator>
    </div>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts :options="options1" height="250px" v-loading="StatRequestCountLoading" />
    <BaseEcharts :options="options2" height="250px" v-loading="StatErrorCountLoading" />
    <BaseEcharts :options="options3" height="250px" v-loading="StatAvgCountLoading" />
  </div>
  <div class="flex justify-between mt-10px indicator-wrapper">
    <Ranking
      v-loading="serviceLoading"
      class="w-100%"
      :title="'请求数Endpoint排行（Top5）'"
      @itemClick="handleServiceTop"
      :rankingList="serviceRankingList"
    ></Ranking>
    <Ranking
      v-loading="errorCountLoading"
      class="w-100%"
      :title="'错误数Endpoint排行（Top5）'"
      @itemClick="handleServiceTop"
      :rankingList="errorCountList"
    ></Ranking>
    <Ranking
      v-loading="avgDurationLoading"
      class="w-100%"
      :title="'平均耗时Endpoint排行（Top5）'"
      @itemClick="handleServiceTop"
      :rankingList="avgDurationList"
    ></Ranking>
  </div>
</template>

<script setup lang="ts">
import {
  getEndpointCountTopList,
  getEndpointErrorCountTopList,
  getEndpointAvgDurationTopList,
  getErrorLogCount,
  getOverviewInfo
} from "@/api/service/index";
import { getStatRequestCount, getStatErrorCount, getStatAvgCount } from "@/api/service/chart";
import { useRouter } from "vue-router";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const router = useRouter();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
// 上面的指标信息
const getRequestCountData = ref<IIndicatordata>({}); // 请求次数
const avgUdrationData = ref<IIndicatordata>({}); // 平均耗时（ms）
const errorCountData = ref<IIndicatordata>({}); // 错误次数
const errorLogCountData = ref<IIndicatordata>({}); // 错误日志数
const loading = ref(false); // 顶部loading
const serviceLoading = ref(false); // 服务top5 loading
const errorCountLoading = ref(false); // 错误率top5 loading
const avgDurationLoading = ref(false); // 平均耗时top5 loading
const serviceRankingList = ref<IRankItem[]>([]); // 服务数据
const errorCountList = ref<IRankItem[]>([]); // 错误数据
const avgDurationList = ref<IRankItem[]>([]); // 平均响应时长
const StatRequestCountLoading = ref(false); //请求统计loading
const StatErrorCountLoading = ref(false); //错误统计loading
const StatAvgCountLoading = ref(false); //耗时统计loading
// 处理上面指标数据的总函数
async function handleServiceData() {
  loading.value = true;
  try {
    getErrorLogCount({
      appid: useApplicationStore.appId,
      serviceName: useServiceNameStore.serviceName,
      sourceType: "service"
    }).then(res => {
      errorLogCountData.value = {
        unit: "错误日志数（次）",
        value: res.value || "",
        errorInfo: "error",
        color: "#f56c6c"
      };
    });
    const res = await getOverviewInfo({
      appid: useApplicationStore.appId,
      serviceName: useServiceNameStore.serviceName
    });
    if (res.code === 0) {
      getRequestCountData.value = {
        unit: "请求数（次）",
        value: res.entity.requestCount || 0,
        errorInfo: "error"
      };
      avgUdrationData.value = {
        unit: "平均耗时（ms）",
        value: res.entity.avgDuration || "",
        errorInfo: "error"
      };
      errorCountData.value = {
        unit: "错误数（次）",
        value: res.entity.errorCount || "",
        errorInfo: "error",
        color: "#f56c6c"
      };
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
// 处理服务Top5数据
function handleServiceTop5Data() {
  let total = 0; // 总数
  serviceLoading.value = true;
  getEndpointCountTopList({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      res.records.forEach(item => {
        total += item.requestCount;
      });
      res.records.map(item => {
        serviceRankingList.value.push({
          name: item.endpoint, // 标题
          proportion: (item.requestCount / total) * 100, // 占比
          totalScore: item.requestCount, // 总数
          color: "#445fde",
          unit: "次"
        });
      });
      serviceLoading.value = false;
    })
    .catch(err => {
      serviceLoading.value = false;
      console.log(err);
    });
}
//处理错误率服务器top5数据
function handleErrorCountData() {
  let total = 0; // 总数
  errorCountLoading.value = true;
  getEndpointErrorCountTopList({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      res.records.forEach(item => {
        total += item.errorCount;
      });
      res.records.map(item => {
        errorCountList.value.push({
          name: item.endpoint, // 标题
          proportion: (item.errorCount / total) * 100, // 占比
          totalScore: item.errorCount, // 总数
          color: "#f56c6c",
          unit: "次"
        });
      });
      errorCountLoading.value = false;
    })
    .catch(err => {
      errorCountLoading.value = false;
      console.log(err);
    });
}
//处理平均耗时排名top5数据
function handleAvgDurationTop5Data() {
  let total = 0; // 总数
  avgDurationLoading.value = true;
  getEndpointAvgDurationTopList({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      res.records.forEach(item => {
        total += item.avgDuration;
      });
      res.records.map(item => {
        avgDurationList.value.push({
          name: item.endpoint, // 标题
          proportion: (item.avgDuration / total) * 100, // 占比
          totalScore: item.avgDuration, // 总数
          color: "#445fde",
          unit: "ms"
        });
      });
      avgDurationLoading.value = false;
    })
    .catch(err => {
      avgDurationLoading.value = false;
      console.log(err);
    });
}
function handleServiceTop(item: IRankItem) {
  router.push({
    path: "/dashboard/overview/service-endponit",
    query: {
      endpoint: item.name
    }
  });
}
//请求数统计
const options1 = ref({});
function StatRequestCount() {
  StatRequestCountLoading.value = true;
  getStatRequestCount({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "请求数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options1.value = getChartOptions(params);
        StatRequestCountLoading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      StatRequestCountLoading.value = false;
    });
}
//错误数统计
const options2 = ref({});
function StatErrorCount() {
  StatErrorCountLoading.value = true;
  getStatErrorCount({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#f56c6c"],
          titleType: "错误数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options2.value = getChartOptions(params);
        StatErrorCountLoading.value = false;
      }
    })
    .catch(error => {
      StatErrorCountLoading.value = false;
      console.log(error);
    });
}
//耗时统计
const options3 = ref({});
function StatAvgCount() {
  StatAvgCountLoading.value = true;
  getStatAvgCount({
    appid: useApplicationStore.appId,
    serviceName: useServiceNameStore.serviceName
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " ms",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "平均耗时",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        options3.value = getChartOptions(params);
        StatAvgCountLoading.value = false;
      }
    })
    .catch(error => {
      StatAvgCountLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  handleServiceData();
  handleServiceTop5Data();
  handleErrorCountData();
  handleAvgDurationTop5Data();
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
