<template>
  <div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <BaseEcharts
        :options="statErrorRateData"
        v-loading="statErrorRateDetailLoading"
        height="250px"
      />
      <BaseEcharts
        :options="statSlowRateData"
        v-loading="statSlowRateDetailLoading"
        height="250px"
      />
      <BaseEcharts
        :options="statAvgDurationData"
        v-loading="statAvgDurationDetailLoading"
        height="250px"
      />
    </div>
    <div class="log-container mt-10px">
      <div class="search-container">
        <el-form>
          <el-form-item class="input-group">
            <label for="traceId">Trace ID</label>
            <el-input
              id="traceId"
              v-model="pageParams.traceId"
              clearable
              placeholder="请输入Trace ID"
            />
          </el-form-item>
          <el-form-item class="input-group">
            <label>APP版本：</label>
            <el-select placeholder="请选择APP版本" v-model="pageParams.appVersion" clearable>
              <el-option v-for="item in appVersionData" :key="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label>HTTP状态码：</label>
            <el-select
              placeholder="请选择HTTP状态码"
              v-model="pageParams.httpStatusCode"
              clearable
              filterable
            >
              <el-option
                v-for="httpstatsu in httpStatusCodeData"
                :key="httpstatsu"
                :value="httpstatsu"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label>设备网络：</label>
            <el-select placeholder="请选择设备网络" v-model="pageParams.netDevice" clearable>
              <el-option v-for="item in netDeviceData" :key="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item style="display: flex; margin-top: 20px">
            <el-popconfirm
              @confirm="resetSearch"
              title="确定清空吗？"
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-warning"
              :hide-after="0"
            >
              <template #reference>
                <el-button type="danger" plain style="flex: 1; margin-right: 10px">
                  清空
                </el-button>
              </template>
            </el-popconfirm>
            <el-button type="primary" style="flex: 1" @click="search"> 查询 </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="tabel-container">
        <MyTable
          v-loading="loading"
          :data="netWorkRequestList"
          :total="netWorkRequestTotal"
          style="width: 100%"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
          @sort-change="handleSortChange"
          :default-sort="{
            prop: 'time',
            order: 'descending'
          }"
        >
          <my-column property="traceId" label="Trace ID" width="320" />
          <my-column property="deviceName" label="设备名称"> </my-column>
          <my-column property="appVersion" label="APP版本"> </my-column>
          <my-column property="net" label="设备网络"> </my-column>
          <my-column property="httpStatusCode" label="HTTP状态码"> </my-column>
          <my-column property="duration" label="耗时(ms)" sortable="custom"> </my-column>
          <my-column property="time" label="请求时间" sortable="custom"> </my-column>
        </MyTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getStatErrorRate,
  getStatAvgDurationRate,
  getStatSlowRate,
  getNetList,
  INetListResponse,
  INetListItem,
  getStatusAppVersion,
  getNetDevice,
  getStatusCodes
} from "@/api/application/netWorkRequest";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useBreadcrumbStore = breadcrumbStore();

const loading = ref(false);

const netWorkRequestList = ref<INetListItem[]>([]);
const netWorkRequestTotal = ref(0);
const statErrorRateData = ref({});
const statSlowRateData = ref({});
const statAvgDurationData = ref({});
const httpStatusCodeData = ref<number[] | string[]>([]);
const netDeviceData = ref<string[]>([]);
const appVersionData = ref<string[]>([]);
const statErrorRateDetailLoading = ref(false);
const statSlowRateDetailLoading = ref(false);
const statAvgDurationDetailLoading = ref(false);
const resetSearch = () => {
  pageParams.value.traceId = "";
  pageParams.value.appVersion = "";
  pageParams.value.httpStatusCode = "";
  pageParams.value.netDevice = "";
  getNetListData();
};
const search = () => {
  getNetListData();
};
// const  getRequestCountData.value = { unit: "请求数（次）", value: res.value, errorInfo: "error" };
onMounted(async () => {
  selectAllData();
  getStatErrorRateData();
  getStatAvgDurationData();
  getStatSlowRateData();
  await getNetListData();
});
//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  httpStatusCode: "",
  traceId: "",
  netDevice: "",
  appVersion: "",
  url: useBreadcrumbStore.breadcrumbTitle,
  method: useBreadcrumbStore.method
});
async function getNetListData() {
  loading.value = true;
  pageParams.value.traceId = pageParams.value.traceId.trim();
  try {
    const res: INetListResponse = await getNetList(pageParams.value);
    if (res.code === 0) {
      netWorkRequestList.value = res.records.map(item => {
        return {
          ...item,
          duration: item.duration + "ms"
        };
      });
      netWorkRequestTotal.value = Number(res.total);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
// 获取错误率统计数据
const getStatErrorRateData = async () => {
  statErrorRateDetailLoading.value = true;
  try {
    const res = await getStatErrorRate({
      url: useBreadcrumbStore.breadcrumbTitle,
      method: useBreadcrumbStore.method
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "错误率统计",
        name: " %",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statErrorRateDetailLoading.value = false;
  } catch (error) {
    statErrorRateDetailLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatSlowRateData = async () => {
  statSlowRateDetailLoading.value = true;
  try {
    const res = await getStatSlowRate({
      url: useBreadcrumbStore.breadcrumbTitle,
      method: useBreadcrumbStore.method
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "慢请求统计",
        name: " %",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statSlowRateDetailLoading.value = false;
  } catch (error) {
    statSlowRateDetailLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getStatAvgDurationData = async () => {
  statAvgDurationDetailLoading.value = true;
  try {
    const res = await getStatAvgDurationRate({
      url: useBreadcrumbStore.breadcrumbTitle,
      method: useBreadcrumbStore.method
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "平均耗时统计",
        name: " ms",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      statAvgDurationData.value = getChartOptions(params);
    }
    statAvgDurationDetailLoading.value = false;
  } catch (error) {
    statAvgDurationDetailLoading.value = false;
    console.error("Error logs:", error);
  }
};

const selectAllData = async function () {
  try {
    const [httpData, netDevice, appVersion] = await Promise.all([
      getStatusCodes({
        url: useBreadcrumbStore.breadcrumbTitle,
        method: useBreadcrumbStore.method
      }),
      getNetDevice({
        url: useBreadcrumbStore.breadcrumbTitle,
        method: useBreadcrumbStore.method
      }),
      getStatusAppVersion({
        url: useBreadcrumbStore.breadcrumbTitle,
        method: useBreadcrumbStore.method
      })
    ]);
    httpStatusCodeData.value = httpData.records;
    netDeviceData.value = netDevice.records as string[];
    appVersionData.value = appVersion.records as string[];
  } catch (error) {
    console.log(error);
  }
};

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getNetListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getNetListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getNetListData();
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}

.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 400px;

  .search-container {
    width: 260px;
    height: 400px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
</style>
