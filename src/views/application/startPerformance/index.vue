<template>
  <div v-loading="loading" class="flex justify-between indicator-wrapper">
    <Indicator :value="launchCountData.value" :unit="'启动总数'"></Indicator>
    <Indicator :value="launchSlowCountData.value" :unit="'慢启动数'"></Indicator>
    <Indicator :value="launchCrashCountData.value" :unit="'启动崩溃数'"></Indicator>
    <Indicator :value="launchAvgDurationData.value" :unit="'启动平均耗时（ms）'"></Indicator>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts
      v-loading="statLaunchSlowCountLoading"
      :options="statErrorRateData"
      height="250px"
    />
    <BaseEcharts
      v-loading="statLaunchCrashCountLoading"
      :options="statSlowRateData"
      height="250px"
    />
    <BaseEcharts :options="averageStartTimeChartData" height="250px" />
  </div>

  <MyTable
    :data="launchStatList"
    class="mt-10px"
    v-loading="tableLoading"
    :total="launchStatTotal"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'count && slowCount && avgDuration',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="device" label="设备名称" width="350">
      <template #default="scope">
        <span class="url-name" @click="viewDetail(scope)">
          {{ scope.row.device }}
        </span>
      </template>
    </my-column>
    <my-column property="count" label="启动总数" sortable="custom" />
    <my-column property="slowCount" label="慢启动数" sortable="custom"> </my-column>
    <my-column property="crashCount" label="启动崩溃数"></my-column>
    <my-column property="avgDuration" label="启动平均耗时" sortable="custom"> </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import {
  getLaunchCount,
  getLaunchSlowCount,
  getLaunchCrashCount,
  getStatLaunchSlowCount,
  getStatLaunchCrashCount,
  getLaunchStatList,
  ILaunchStatListResponse,
  ILaunchStatItem,
  getLaunchAvgDuration
} from "@/api/application/startPerformance";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { getAverageLaunchDurationChart } from "@/api/application/index";
import { applicationStore } from "@/store/modules/application";
const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false);
const tableLoading = ref(false);
const useApplicationStore = applicationStore();
const statLaunchSlowCountLoading = ref(false);
const statLaunchCrashCountLoading = ref(false);
const avgDurationLoading = ref(false);
const launchCountData = ref<IIndicatordata>({});
const launchSlowCountData = ref<IIndicatordata>({});
const launchCrashCountData = ref<IIndicatordata>({});
const launchAvgDurationData = ref<IIndicatordata>({});
const launchStatList = ref<ILaunchStatItem[]>([]);
const launchStatTotal = ref(0);

const statErrorRateData = ref({});
const statSlowRateData = ref({});
const averageStartTimeChartData = ref({});

onMounted(() => {
  getLaunchStatListData();
  getStatSlowCountData();
  getAvgDurationData();
  getStatCtashCountData();
  getLaunchCountData();
});
// 上面的启动总数，慢启动数，启动崩溃数
async function getLaunchCountData() {
  try {
    loading.value = true;
    const [launchCount, launchSlowCount, launchCrashCount, launchAvgDuration] = await Promise.all([
      getLaunchCount({}),
      getLaunchSlowCount({}),
      getLaunchCrashCount({}),
      getLaunchAvgDuration({})
    ]);

    launchCountData.value = {
      unit: "启动总数",
      value: launchCount.value,
      errorInfo: "error"
    };
    launchSlowCountData.value = {
      unit: "慢启动数",
      value: launchSlowCount.value,
      errorInfo: "error"
    };
    launchCrashCountData.value = {
      unit: "启动崩溃数",
      value: launchCrashCount.value,
      errorInfo: "error"
    };
    launchAvgDurationData.value = {
      unit: "启动平均耗时",
      value: launchAvgDuration.value,
      errorInfo: "error"
    };
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getLaunchStatListData() {
  tableLoading.value = true;
  try {
    const res: ILaunchStatListResponse = await getLaunchStatList(pageParams.value);
    if (res.code === 0) {
      launchStatList.value = res.records.map(item => {
        return { ...item, avgDuration: item.avgDuration + "ms" };
      });
      launchStatTotal.value = Number(res.total);
    }
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
    console.log(error);
  }
}
const getAvgDurationData = async () => {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    avgDurationLoading.value = true;
    const res = await getAverageLaunchDurationChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "启动平均耗时",
        name: " ms",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      averageStartTimeChartData.value = getChartOptions(params);
    }
    avgDurationLoading.value = false;
  } catch (error) {
    avgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};

// 获取错误率统计数据
const getStatSlowCountData = async () => {
  statLaunchSlowCountLoading.value = true;
  try {
    const res = await getStatLaunchSlowCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "慢启动统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statLaunchSlowCountLoading.value = false;
  } catch (error) {
    statLaunchSlowCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatCtashCountData = async () => {
  try {
    statLaunchCrashCountLoading.value = true;
    const res = await getStatLaunchCrashCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "启动崩溃统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statLaunchCrashCountLoading.value = false;
  } catch (error) {
    statLaunchCrashCountLoading.value = false;
    console.error("Error logs:", error);
  }
};

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getLaunchStatListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getLaunchStatListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getLaunchStatListData();
};
// 查看详情
const viewDetail = (scope: any) => {
  router.push("/appmonitor/start-performance/detail");
  useBreadcrumbStore.setBreadcrumb(scope.row.device);
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.url-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
