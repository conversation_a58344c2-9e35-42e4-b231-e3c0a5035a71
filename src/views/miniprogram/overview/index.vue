<template>
  <div class="flex justify-between indicator-container" v-loading="loading">
    <Indicator :value="totalErrorCount.value" :unit="totalErrorCount.unit"></Indicator>
    <Indicator :value="activeVisitorCount.value" :unit="activeVisitorCount.unit"></Indicator>
    <Indicator :value="averageStartTime.value" :unit="averageStartTime.unit"></Indicator>
    <Indicator :value="crashCount.value" :unit="crashCount.unit"></Indicator>
    <Indicator
      :value="stutterCount.value"
      :unit="stutterCount.unit"
      :color="stutterCount.color"
    ></Indicator>
    <Indicator
      :value="networkErrorRate.value"
      :unit="networkErrorRate.unit"
      :color="networkErrorRate.color"
    ></Indicator>
  </div>
  <div class="flex justify-between mt-10px indicator-container">
    <BaseEcharts :options="pageViewChartData" height="250px" v-loading="chartLoading" />
    <BaseEcharts :options="errorCountChartData" height="250px" v-loading="chartLoading" />
    <BaseEcharts :options="apiDurationChartData" height="250px" v-loading="chartLoading" />
  </div>
  <div class="flex justify-between mt-10px indicator-container">
    <Ranking
      class="w-100%"
      :title="'页面访问量排行（Top5）'"
      v-loading="chartLoading"
      :rankingList="pageViewList"
    ></Ranking>
    <Ranking
      class="w-100%"
      :title="'页面报错排行（Top5）'"
      v-loading="chartLoading"
      :rankingList="errorLogsList"
    ></Ranking>
    <Ranking
      class="w-100%"
      :title="'接口平均耗时排行（Top5）'"
      :rankingList="apiDurationList"
      v-loading="chartLoading"
    ></Ranking>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import {
  getPVCount,
  getUVCount,
  getLoadCount,
  getAJAXCount,
  getAJAXErrorCount,
  getErrorCount,
  getPageViewTop5,
  getErrorLogsTop5,
  getApiDurationTop5,
  getPageViewStats,
  getApiDurationStats,
  getErrorLogsStats
} from "@/api/mini/overview";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";

interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
const loading = ref(false);
const chartLoading = ref(false);
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const apiDurationChartData = ref({});
const errorCountChartData = ref({});
const pageViewChartData = ref({});
const activeVisitorCount = ref<IIndicatorData>({});
const averageStartTime = ref<IIndicatorData>({});
const crashCount = ref<IIndicatorData>({});
const stutterCount = ref<IIndicatorData>({});
const networkErrorRate = ref<IIndicatorData>({});
const totalErrorCount = ref<IIndicatorData>({});

const handleServiceData = async () => {
  loading.value = true;
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;

  type FetchDataFunction = (data: { appid: string }) => Promise<{ value: number }>;
  type FetchDataResult = { unit: string; value: number; errorInfo: string };

  const fetchData = async (
    getDataFunction: FetchDataFunction,
    unit: string
  ): Promise<FetchDataResult> => {
    try {
      const res = await getDataFunction({ appid: appId, mpAppid: mpAppid });
      loading.value = false;
      return { unit, value: res.value, errorInfo: "error" };
    } catch (error) {
      loading.value = false;
      console.error(error);
      return { unit, value: 0, errorInfo: "error" };
    }
  };

  try {
    const [
      activeVisitorCountData,
      averageStartTimeData,
      crashCountData,
      stutterCountData,
      networkErrorRateData,
      totalErrorCountData
    ] = await Promise.all([
      fetchData(getUVCount, "访问人数（UV）"),
      fetchData(getLoadCount, "页面加载平均耗时（ms）"),
      fetchData(getAJAXCount, "接口请求平均耗时（ms）"),
      fetchData(getAJAXErrorCount, "接口失败率（%）"),
      fetchData(getErrorCount, "错误数"),
      fetchData(getPVCount, "访问量（PV）")
    ]);

    activeVisitorCount.value = activeVisitorCountData;
    averageStartTime.value = averageStartTimeData;
    crashCount.value = crashCountData;
    totalErrorCount.value = totalErrorCountData;

    const setCountValue = (data: any) => {
      const countValue = { ...data };
      if (Number(data.value) > 0) {
        countValue.color = "#f56c6c";
      } else {
        countValue.color = undefined;
      }
      return countValue;
    };

    networkErrorRate.value = setCountValue(networkErrorRateData);
    stutterCount.value = setCountValue(stutterCountData);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};

const initPageViewChart = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getPageViewStats(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "访问量",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      pageViewChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initApiDurationChart = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getApiDurationStats(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "接口平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      apiDurationChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initErrorLogsChart = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getErrorLogsStats(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      errorCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

// 处理服务Top5数据
const pageViewList = ref<IRankItem[]>([]); // 服务数据
function handlepageViewTop5Data() {
  let total = 0; // 总数
  getPageViewTop5({
    appid: useApplicationStore.appId,
    mpAppid: usebreadcrumbStore.appOption
  })
    .then(res => {
      if (res.code === 0) {
        res.records.forEach(item => {
          total += item.count;
        });
        res.records.map(item => {
          pageViewList.value.push({
            name: item.pageUrl, // 标题
            proportion: (item.count / total) * 100, // 占比
            totalScore: item.count, // 总数
            color: "#445fde",
            unit: "次"
          });
        });
      }
    })
    .catch(err => {
      console.log(err);
    });
}
const errorLogsList = ref<IRankItem[]>([]); // 服务数据
function handleerrorLogsTop5Data() {
  let total = 0; // 总数
  getErrorLogsTop5({
    appid: useApplicationStore.appId,
    mpAppid: usebreadcrumbStore.appOption
  })
    .then(res => {
      if (res.code === 0) {
        res.records.forEach(item => {
          total += item.count;
        });
        res.records.map(item => {
          errorLogsList.value.push({
            name: item.url, // 标题
            proportion: (item.count / total) * 100, // 占比
            totalScore: item.count, // 总数
            color: "#f56c6c",
            unit: "次"
          });
        });
      }
    })
    .catch(err => {
      console.log(err);
    });
}
const apiDurationList = ref<IRankItem[]>([]); // 服务数据
function handleapiDurationTop5Data() {
  let total = 0; // 总数
  getApiDurationTop5({
    appid: useApplicationStore.appId,
    mpAppid: usebreadcrumbStore.appOption
  })
    .then(res => {
      if (res.code === 0) {
        res.records.forEach(item => {
          total += item.avgDuration;
        });
        res.records.map(item => {
          apiDurationList.value.push({
            name: item.url, // 标题
            proportion: (item.avgDuration / total) * 100, // 占比
            totalScore: item.avgDuration, // 总数
            color: "#445fde",
            unit: " ms"
          });
        });
      }
    })
    .catch(err => {
      console.log(err);
    });
}

onMounted(async () => {
  handleServiceData();
  chartLoading.value = true;
  try {
    await Promise.all([
      initPageViewChart(),
      initApiDurationChart(),
      initErrorLogsChart(),
      handlepageViewTop5Data(),
      handleerrorLogsTop5Data(),
      handleapiDurationTop5Data()
    ]);
  } finally {
    chartLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
