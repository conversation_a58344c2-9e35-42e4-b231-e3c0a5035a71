<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator :value="avgLoadToReady.value" :unit="avgLoadToReady.unit"></Indicator>
      <Indicator :value="avgLoadToDom.value" :unit="avgLoadToDom.unit"></Indicator>
      <Indicator :value="avgRouteToLoad.value" :unit="avgRouteToLoad.unit"></Indicator>
      <Indicator :value="avgDomToReady.value" :unit="avgDomToReady.unit"></Indicator>
      <Indicator :value="avgTotalTime.value" :unit="avgTotalTime.unit"></Indicator>
    </div>
  </div>

  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts v-loading="requestCountLoading" :options="statRequestCountData" height="250px" />
    <BaseEcharts v-loading="statErrorCountsLoading" :options="statErrorCountsData" height="250px" />
    <BaseEcharts
      v-loading="h5statAvgDurationLoading"
      :options="h5statAvgDurationData"
      height="250px"
    />
  </div>
  <MyTable
    class="mt-10px"
    :data="interPerforList"
    :total="interPerforTotal"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'avgLoadToReady && avgLoadToDom && avgRouteToLoad && count ion && avgDomToReady && avgTotalTime',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="pageUrl" label="页面路径">
      <template #default="scope">
        <span class="url-name">
          {{ scope.row.pageUrl }}
        </span>
      </template>
    </my-column>
    <my-column property="count" label="访问次数" sortable="custom" width="200"> </my-column>
    <my-column property="avgLoadToReady" label="内容加载总耗时" sortable="custom" width="200" />
    <my-column property="avgLoadToDom" label="页面加载耗时" sortable="custom" width="200">
    </my-column>
    <my-column property="avgRouteToLoad" label="页面导航耗时" sortable="custom" width="200">
    </my-column>
    <my-column property="avgDomToReady" label="页面渲染完成耗时" sortable="custom" width="200" />
    <my-column property="avgTotalTime" label="页面加载总耗时" sortable="custom" width="200">
    </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";

import {
  getOverviewCount,
  getMiniPagePerList,
  IPageItem,
  IPageList,
  getMiniTotalTimeCount,
  getMiniTotalPageLoadTime,
  getMiniPageRenderTime
} from "@/api/mini/pagePer";

import { useRouter } from "vue-router";
const router = useRouter();

interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}

const avgLoadToReady = ref<IIndicatorData>({});
const avgLoadToDom = ref<IIndicatorData>({});
const avgRouteToLoad = ref<IIndicatorData>({});
const avgDomToReady = ref<IIndicatorData>({});
const avgTotalTime = ref<IIndicatorData>({});
const interPerforList = ref<IPageItem[]>([]);
const interPerforTotal = ref(0);

const statErrorCountsData = ref({});
const statRequestCountData = ref({});
const h5statAvgDurationData = ref({});
const statErrorCountsLoading = ref(false);
const requestCountLoading = ref(false);
const h5statAvgDurationLoading = ref(false);

const loading = ref(false); //
const tableLoading = ref(false);

async function getInterCountData() {
  try {
    loading.value = true;
    const res = await getOverviewCount({});
    if (res.code === 0) {
      avgLoadToReady.value = {
        unit: "内容加载总耗时(ms)",
        value: res.entity.avgLoadToReady || 0,
        errorInfo: "error"
      };
      avgLoadToDom.value = {
        unit: "页面加载耗时(ms)",
        value: res.entity.avgLoadToDom || 0,
        errorInfo: "error"
      };
      avgRouteToLoad.value = {
        unit: "页面导航耗时(ms)",
        value: res.entity.avgRouteToLoad || 0,
        errorInfo: "error"
      };
      avgDomToReady.value = {
        unit: "页面渲染完成耗时(ms)",
        value: res.entity.avgDomToReady || 0,
        errorInfo: "error"
      };
      avgTotalTime.value = {
        unit: "页面加载总耗时(ms)",
        value: res.entity.avgTotalTime || 0,
        errorInfo: "error"
      };
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}

const getTotalTimeData = async () => {
  statErrorCountsLoading.value = true;
  try {
    const res = await getMiniTotalTimeCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "总耗时统计",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        name: "ms"
      };
      statErrorCountsData.value = getChartOptions(params);
    }
    statErrorCountsLoading.value = false;
  } catch (error) {
    statErrorCountsLoading.value = false;
    console.error("Error logs:", error);
  }
};

const getMiniPageRenderData = async () => {
  try {
    requestCountLoading.value = true;
    const res = await getMiniPageRenderTime({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "页面渲染耗时统计",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        name: "ms"
      };
      statRequestCountData.value = getChartOptions(params);
    }
    requestCountLoading.value = false;
  } catch (error) {
    requestCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getMiniTotalPageTimeData = async () => {
  try {
    h5statAvgDurationLoading.value = true;
    const res = await getMiniTotalPageLoadTime({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        name: " ms",
        typ: res.entity.granularity,
        color: color,
        titleType: "统计页面加载耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      h5statAvgDurationData.value = getChartOptions(params);
    }
    h5statAvgDurationLoading.value = false;
  } catch (error) {
    h5statAvgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};
//列表数据
//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getMiniPageListData() {
  tableLoading.value = true;
  try {
    const res: IPageList = await getMiniPagePerList(pageParams.value);
    if (res.code === 0) {
      interPerforList.value = res?.records.map(item => {
        return {
          ...item,
          avgDomToReady: item.avgDomToReady + "ms",
          avgLoadToReady: item.avgLoadToReady + "ms",
          avgLoadToDom: item.avgLoadToDom + "ms",
          avgRouteToLoad: item.avgRouteToLoad + "ms",
          avgTotalTime: item.avgTotalTime + "ms"
        };
      });
      interPerforTotal.value = Number(res.total);
    }
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
    console.log(error);
  }
}

//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getMiniPageListData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getMiniPageListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getMiniPageListData();
};
onMounted(() => {
  // handleServiceData();
  getInterCountData();
  getTotalTimeData();
  getMiniPageRenderData();
  getMiniTotalPageTimeData();
  getMiniPageListData();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
