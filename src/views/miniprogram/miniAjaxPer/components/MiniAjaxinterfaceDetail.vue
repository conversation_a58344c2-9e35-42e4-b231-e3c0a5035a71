<template>
  <div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <BaseEcharts :options="statSlowRateData" v-loading="statSlowRateLoading" height="250px" />
      <BaseEcharts :options="statErrorRateData" v-loading="statErrorRateLoading" height="250px" />
      <BaseEcharts
        :options="statAvgDurationData"
        v-loading="statAvgDurationLoading"
        height="250px"
      />
    </div>

    <div class="log-container mt-10px">
      <div class="search-container">
        <el-form>
          <el-form-item class="input-group">
            <label for="traceId">唯一标识：</label>
            <el-input id="traceId" v-model="pageParams.id" clearable placeholder="请输入唯一标识" />
          </el-form-item>
          <el-form-item class="input-group">
            <label for="appVersion">操作系统：</label>
            <el-select
              placeholder="请选择操作系统"
              v-model="pageParams.osName"
              clearable
              filterable
            >
              <el-option
                v-for="appVersion in selectOs"
                :key="appVersion"
                :label="appVersion"
                :value="appVersion"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label for="appVersion">省份：</label>
            <el-select placeholder="请选择省份" v-model="pageParams.province" clearable filterable>
              <el-option
                v-for="appVersion in selectProvinces"
                :key="appVersion"
                :label="appVersion"
                :value="appVersion"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label for="appVersion">设备：</label>
            <el-select placeholder="请选择设备" v-model="pageParams.model" clearable filterable>
              <el-option
                v-for="appVersion in deviceParams"
                :key="appVersion"
                :label="appVersion"
                :value="appVersion"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label for="appVersion">结果：</label>
            <el-select placeholder="请选择结果" v-model="pageParams.result" clearable filterable>
              <el-option
                v-for="item in resultParams"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="display: flex; margin-top: 28px">
            <el-popconfirm
              @confirm="resetSearch"
              title="确定清空吗？"
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-warning"
              :hide-after="0"
            >
              <template #reference>
                <el-button type="danger" plain style="flex: 1; margin-right: 10px">
                  清空
                </el-button>
              </template>
            </el-popconfirm>
            <el-button type="primary" style="flex: 1" @click="search"> 查询 </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="tabel-container">
        <MyTable
          v-loading="loading"
          :data="ajaxRequestList"
          :total="ajaxRequestTotal"
          style="width: 100%"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
          @sort-change="handleSortChange"
          :default-sort="{
            prop: 'duration'
          }"
        >
          <my-column property="id" label="唯一标识" width="320"> </my-column>
          <my-column property="os" label="用户操作系统"> </my-column>
          <my-column property="ip" label="IP地址"> </my-column>
          <my-column property="result" label="HTTP状态码" align="center" header-align="center">
            <template #default="{ row }">
              <el-tag :type="getTagType(row.statusCode)">
                {{ row.statusCode }}
              </el-tag>
            </template>
          </my-column>
          <!-- <my-column property="userId" label="用户Id"> </my-column> -->
          <my-column property="model" label="用户设备"> </my-column>
          <my-column property="duration" label="请求耗时" sortable="custom"> </my-column>
          <my-column property="time" label="请求时间"> </my-column>
          <my-column property="errMsg" label="错误信息"> </my-column>
        </MyTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getMiniRequestCount,
  getMiniAvgDurationRate,
  getMiniErrorCount,
  getMiniAjaxList,
  IAjaxListResponse,
  IAjaxListItem,
  getProvinces,
  getOs,
  getDevice
} from "@/api/mini/ajaxPer";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { breadcrumbStore } from "@/store/modules/breadcurmb";

interface IResultParams {
  label: string;
  value: number;
}
const useBreadcrumbStore = breadcrumbStore();
const loading = ref(false);
const ajaxRequestList = ref<IAjaxListItem[]>([]);
const ajaxRequestTotal = ref(0);

const statErrorRateData = ref({});
const statSlowRateData = ref({});
const statAvgDurationData = ref({});

const statErrorRateLoading = ref(false);
const statSlowRateLoading = ref(false);
const statAvgDurationLoading = ref(false);

const selectProvinces = ref<string[]>([]);
const selectOs = ref<string[]>([]);
const deviceParams = ref<string[]>([]);
const resultParams = ref<IResultParams[]>([
  {
    label: "成功",
    value: 0
  },
  {
    label: "失败",
    value: 1
  }
]);

function getTagType(statusCode: number) {
  return statusCode === 200 ? "success" : "danger";
}

onMounted(async () => {
  getStatErrorRateData();
  getStatAvgDurationData();
  getStatSlowRateData();
  getSelectAll();
  await getAjaxListData();
});
//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  url: useBreadcrumbStore.breadcrumbTitle,
  id: "",
  osName: "",
  province: "",
  model: "",
  result: ""
});
function resetSearch() {
  pageParams.value.osName = "";
  pageParams.value.province = "";
  pageParams.value.id = "";
  pageParams.value.model = "";
  pageParams.value.result = "";
}
function search() {
  pageParams.value.page = 1;
  getAjaxListData();
}
async function getAjaxListData() {
  loading.value = true;
  pageParams.value.id = pageParams.value.id.trim();
  try {
    const res: IAjaxListResponse = await getMiniAjaxList(pageParams.value);
    if (res.code === 0) {
      ajaxRequestList.value = res.records.map(item => {
        return {
          ...item,
          duration: item.duration + "ms"
        };
      });
      ajaxRequestTotal.value = Number(res.total);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}

async function getSelectAll() {
  try {
    const url = useBreadcrumbStore.breadcrumbTitle;
    const [device, provinces, os] = await Promise.all([
      getDevice({ url: url }),
      getProvinces({ url: url }),
      getOs({ url: url })
    ]);
    deviceParams.value = device?.records || [];
    selectProvinces.value = provinces?.records || [];
    selectOs.value = os?.records || [];
  } catch (error) {
    console.log(error);
  }
}
// 获取错误率统计数据
const getStatErrorRateData = async () => {
  statErrorRateLoading.value = true;
  try {
    const res = await getMiniErrorCount({
      url: useBreadcrumbStore.breadcrumbTitle
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求失败数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statErrorRateLoading.value = false;
  } catch (error) {
    statErrorRateLoading.value = false;
    console.log("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatSlowRateData = async () => {
  statSlowRateLoading.value = true;
  try {
    const res = await getMiniRequestCount({
      url: useBreadcrumbStore.breadcrumbTitle
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statSlowRateLoading.value = false;
  } catch (error) {
    statSlowRateLoading.value = false;
    console.log("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getStatAvgDurationData = async () => {
  statAvgDurationLoading.value = true;
  try {
    const res = await getMiniAvgDurationRate({
      url: useBreadcrumbStore.breadcrumbTitle
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        name: " ms",
        typ: res.entity.granularity,
        color: color,
        titleType: "平均响应时长统计",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      statAvgDurationData.value = getChartOptions(params);
    }
    statAvgDurationLoading.value = false;
  } catch (error) {
    statAvgDurationLoading.value = false;
    console.log("Error logs:", error);
  }
};

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getAjaxListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getAjaxListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getAjaxListData();
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}

.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 440px;

  .search-container {
    width: 260px;
    height: 460px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.input-group {
  margin-bottom: 10px;
}
</style>
