<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入用户ID"
        v-model="pageParams.userId"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'pageVeiwCount && ipCount && loadPageTime && resTime && firstPackTime && latestVisitTime',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="userId" label="用户ID">
      <template #default="scope">
        <!-- <span class="service-name" @click="shouDetail(scope.row)"> -->
        {{ scope.row.userId }}
        <!-- </span> -->
      </template>
    </my-column>
    <my-column property="pageVeiwCount" label="页面访问量" sortable="custom" />
    <my-column property="ipCount" label="IP数" sortable="custom" />
    <my-column property="loadPageTime" label="页面加载耗时" sortable="custom">
      <template v-slot="{ row }">
        <span> {{ row.loadPageTime }}ms </span>
      </template>
    </my-column>
    <my-column property="resTime" label="资源加载耗时" sortable="custom">
      <template v-slot="{ row }">
        <span> {{ row.resTime }}ms </span>
      </template>
    </my-column>
    <my-column property="firstPackTime" label="首字节耗时" sortable="custom">
      <template v-slot="{ row }">
        <span> {{ row.firstPackTime }}ms </span>
      </template>
    </my-column>
    <my-column property="latestVisitTime" label="最近访问时间" sortable="custom" />
  </MyTable>
  <!-- <div>
    <el-drawer v-model="logVisible" :title="title" size="50%">
      <TitlecCom :title="collapseDetailtitle"></TitlecCom>
      <div class="attr-panel">
        <div class="tab-content">
          <table class="detail-table">
            <tbody>
              <tr>
                <td class="label">发生时间</td>
                <td width="35%"></td>
                <td class="label">设备型号</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">内存占用</td>
                <td width="35%"></td>
                <td class="label">内存空闲</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">可用磁盘</td>
                <td width="35%"></td>
                <td class="label">已用磁盘</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">CPU使用</td>
                <td width="35%"></td>
                <td class="label">操作系统</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">设备网络</td>
                <td width="35%"></td>
                <td class="label">应用包名</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">APP版本</td>
                <td width="35%"></td>
                <td class="label">CPU架构</td>
                <td width="35%"></td>
              </tr>
              <tr>
                <td class="label">摘要</td>
                <td colspan="3"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-drawer>
  </div> -->
</template>

<script setup lang="ts">
// import TitlecCom from "@/components/TitleCom/index.vue";
import { Search } from "@element-plus/icons-vue";
import { getUserList } from "@/api/h5/user";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const usebreadcrumbStore = breadcrumbStore();
const useApplicationStore = applicationStore();
//loading动画
const tableLoading = ref(true);
// const title = ref(""); //抽屉标题
// const collapseDetailtitle = ref("基础信息"); //抽屉标题
// const logVisible = ref(false); //抽屉是否显示
//列表参数
const pageParams = reactive({
  appid: "",
  appName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  userId: ""
});
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  pageParams.appName = usebreadcrumbStore.appOption;
  pageParams.userId = pageParams.userId.trim();
  getUserList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
// const shouDetail = (row: any) => {
//   logVisible.value = true;
//   title.value = "用户详情 【" + row.userId + "】";
// };
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
// .service-name {
//   cursor: pointer;
//   color: #0064c8;
//   vertical-align: middle;
// }
// .attr-panel {
//   flex: 1;
//   margin-left: 10px;

//   .span-panel {
//     width: 750px;
//     height: 875px;
//     overflow-x: hidden;
//     overflow-y: auto;
//     table {
//       border: 1px solid #ebeef5;
//       border-collapse: collapse;

//       tbody {
//         tr {
//           transition: background 0.5s;

//           &:hover {
//             background: #f5f7fa;
//           }
//         }
//       }

//       td {
//         padding: 12px;
//         position: relative;

//         .duration {
//           position: absolute;
//           align-items: center;
//           padding-left: 15px;
//           display: flex;
//           left: 5px;
//           right: 5px;
//           top: 5px;
//           bottom: 5px;

//           &.success {
//             background: #c1e488;
//           }

//           &.error {
//             background: #ff9393;
//           }
//         }

//         .bar {
//           width: 3px;
//           height: 18px;
//           margin-right: 10px;
//           display: inline-block;
//           vertical-align: middle;

//           &.success {
//             background: #009431;
//           }

//           &.error {
//             background: #e00000;
//           }
//         }

//         .name {
//           vertical-align: middle;
//           display: inline-block;
//           overflow: hidden;
//           white-space: nowrap;
//           text-overflow: ellipsis;
//           max-width: 280px;
//         }
//       }

//       thead {
//         tr {
//           td {
//             background: #f9f9f9;
//             border-bottom: 1px solid #ebeef5;
//           }
//         }
//       }
//     }
//   }
// }

// .detail-table {
//   width: 100%;
//   color: #606266;
//   font-size: 14px;
//   border: 1px solid #ebeef5;
//   border-collapse: collapse;

//   tr {
//     td {
//       border: 1px solid #ebeef5;
//       word-break: break-all;
//       padding: 15px;
//     }

//     td.label {
//       background: #f5f7fa;
//       width: 120px;
//       overflow: hidden;
//       white-space: nowrap;
//       text-overflow: ellipsis;
//     }
//   }
// }
// :deep(.el-drawer__header) {
//   margin: 5px !important;
// }
// :deep(.attr-panel) {
//   margin: 0 !important;
// }
</style>
