export const addTestRecord = (ip: string, list) => {
  const record = {
    id: Date.now(),
    hostName: ip,
    ip,
    sent: 0,
    recv: 0,
    packetLoss: "0%",
    statusCode: "--",
    minTime: 0,
    maxTime: 0,
    avgTime: 0,
    responseTimes: [] as number[],
    dnsStatus: "--",
    // 用于存放 Ping 模拟输出
    pingOutput: ""
  };
  list.records.unshift(record);
  list.total++;
  runPingSimulation(record);
};

const runPingSimulation = (record: any) => {
  record.sent = 0;
  record.recv = 0;
  record.responseTimes = [];
  record.minTime = 9999;
  record.maxTime = 0;
  record.avgTime = 0;
  // 清空 ping 输出信息，并输出第一行提示信息
  record.pingOutput = `正在 Ping ${record.ip} 具有 32 字节的数据:\n`;

  const pingCount = 4; // 模拟 ping 4 次
  let currentPing = 0;

  const simulateOnePing = () => {
    currentPing++;
    record.sent++;
    // 模拟 80% 成功率
    let isSuccess = Math.random() < 0.8;
    let delay = 0;
    if (isSuccess) {
      record.recv++;
      // 模拟延时，如果小于 1 则显示 "<1ms"
      delay = Math.floor(Math.random() * 1);
      // 0 表示小于 1ms
      record.responseTimes.push(delay);
      record.minTime = Math.min(record.minTime, delay);
      record.maxTime = Math.max(record.maxTime, delay);
      const sum = record.responseTimes.reduce((acc: number, cur: number) => acc + cur, 0);
      record.avgTime = record.responseTimes.length
        ? Math.floor(sum / record.responseTimes.length)
        : 0;
    }
    // 拼接回复信息
    const replyLine = isSuccess
      ? `来自 ${record.ip} 的回复: 字节=32 时间${delay < 1 ? "<1ms" : "=" + delay + "ms"} TTL=128\n`
      : `来自 ${record.ip} 的回复: 请求超时\n`;
    record.pingOutput += replyLine;
    // 更新丢包率
    const lost = record.sent - record.recv;
    record.packetLoss = record.sent ? ((lost / record.sent) * 100).toFixed(2) : "0%";
    // 模拟 DNS 检测（成功率90%）
    record.dnsStatus = Math.random() < 0.9 ? "解析成功" : "解析失败";
    // 模拟 HTTP 状态码（成功返回200，否则500）
    record.statusCode = isSuccess ? 200 : 500;

    if (currentPing < pingCount) {
      setTimeout(simulateOnePing, 1000);
    } else {
      // 输出统计信息
      record.pingOutput += `\n${record.ip} 的 Ping 统计信息:\n`;
      record.pingOutput += `    数据包: 已发送 = ${record.sent}，已接收 = ${record.recv}，丢失 = ${lost} (${((lost / record.sent) * 100).toFixed(0)}% 丢失)，\n`;
      record.pingOutput += `往返行程的估计时间(以毫秒为单位):\n`;
      record.pingOutput += `    最短 = ${record.minTime}ms，最长 = ${record.maxTime}ms，平均 = ${record.avgTime}ms\n`;
    }
  };

  simulateOnePing();
};
export const descriptionItems = [
  { label: "检测点", field: "hostName" },
  { label: "IP地址", field: "ip" },
  { label: "发送次数", field: "sent" },
  { label: "接收次数", field: "recv" },
  { label: "丢包率", field: "packetLoss", unit: "%" },
  { label: "HTTP状态码", field: "statusCode" },
  { label: "最小响应时间", field: "minTime", unit: "ms" },
  { label: "最大响应时间", field: "maxTime", unit: "ms" },
  { label: "平均响应时间", field: "avgTime", unit: "ms" },
  { label: "DNS检测结果", field: "dnsStatus" },
  { label: "详细信息", field: "pingOutput", special: true }
];

export const getStatusTag = (status: number) => {
  const map = {
    1: { label: "创建中", type: "info" },
    2: { label: "运行中", type: "success" },
    3: { label: "运行异常", type: "danger" },
    4: { label: "暂停中", type: "warning" },
    5: { label: "暂停异常", type: "danger" },
    6: { label: "任务暂停", type: "warning" },
    7: { label: "任务删除中", type: "info" },
    8: { label: "任务删除异常", type: "danger" },
    9: { label: "任务已删除", type: "default" },
    10: { label: "定时任务暂停中", type: "warning" }
  };
  return map[status] || { label: "未知状态", type: "default" };
};

export const formatNodeIpType = (type: number | null | undefined): string => {
  const map = {
    0: "不限",
    1: "IPv4",
    2: "IPv6"
  };
  return map[type ?? 0] || "未知类型";
};

export const formatTaskType = (type: number): string => {
  const map: Record<number, string> = {
    1: "页面浏览",
    2: "文件上传",
    3: "文件下载",
    4: "端口性能",
    5: "网络质量",
    6: "流媒体"
  };
  return map[type] || "未知类型";
};

/**
 * 将数字任务类型映射为 TencentCloud 要求的字符串类型。
 * 1 → 页面性能，2/3 → 文件传输，4 → 端口性能，5 → 网络质量，6 → 音视频体验
 * @param {string | number} type - 数字任务类型（1~6）。
 * @returns {string} - 云 API 所需的任务类型字符串。
 */
export const mapTaskTypeToApiValue = (type: string | number): string => {
  const map: Record<string, string> = {
    "1": "AnalyzeTaskType_Browse",
    "2": "AnalyzeTaskType_UploadDownload",
    "3": "AnalyzeTaskType_UploadDownload",
    "4": "AnalyzeTaskType_Transport",
    "5": "AnalyzeTaskType_Network",
    "6": "AnalyzeTaskType_MediaStream"
  };
  return map[type.toString()] || "";
};

export const TaskTypeMap = [
  { value: 1, label: "页面浏览" },
  { value: 2, label: "文件上传" },
  { value: 3, label: "文件下载" },
  { value: 4, label: "端口性能" },
  { value: 5, label: "网络质量" },
  { value: 6, label: "音视频体验" },
  { value: 7, label: "域名 whois" }
];
