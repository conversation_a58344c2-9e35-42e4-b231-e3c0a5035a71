<template>
  <div class="flex justify-between indicator-container" v-loading="loading">
    <Indicator :value="averageStartTime.value" :unit="averageStartTime.unit"></Indicator>
    <Indicator :value="networkErrorRate.value" :unit="networkErrorRate.unit"></Indicator>
    <Indicator :value="stutterCount.value" :unit="stutterCount.unit"></Indicator>
    <Indicator :value="crashCount.value" :unit="crashCount.unit"></Indicator>
    <Indicator :value="activeVisitorCount.value" :unit="activeVisitorCount.unit"></Indicator>
  </div>
  <div class="flex justify-between mt-10px mb-10px indicator-container">
    <BaseEcharts :options="pageViewChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="errorCountChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="apiDurationChartData" height="275px" v-loading="chartLoading" />
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{ prop: 'count', order: 'descending' }"
    @sort-change="handleSortChange"
  >
    <MyColumn property="url" label="页面路径">
      <template #default="{ row }">
        <span class="service-name" @click="toDetail(row)">
          {{ row.url }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="loadPageTime" label="页面加载耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.loadPageTime + " ms" }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="ttfbTime" label="TTFB耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.ttfbTime + " ms" }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="resTime" label="资源加载耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.resTime + " ms" }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="firstPackTime" label="首字节耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.firstPackTime + " ms" }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="domAnalysisTime" label="DOM解析耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.domAnalysisTime + " ms" }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="tcpTime" label="TCP连接耗时" sortable="custom" width="190">
      <template #default="{ row }">
        <span>
          {{ row.tcpTime + " ms" }}
        </span>
      </template>
    </MyColumn>
  </MyTable>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import {
  getAverageTTFB,
  getAverageDOMParsing,
  getAveragePageLoad,
  getAverageFirstByte,
  getAverageResourceLoad,
  getStartList,
  getStatAveragePageLoad,
  getStatAverageFirstByte,
  getStatAverageTTFB
} from "@/api/h5/PagePerformance";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { useRouter } from "vue-router";
const router = useRouter();

interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
const loading = ref(false);
const chartLoading = ref(false);
const useBreadcrumbStore = breadcrumbStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const apiDurationChartData = ref({});
const errorCountChartData = ref({});
const pageViewChartData = ref({});
const activeVisitorCount = ref<IIndicatorData>({});
const averageStartTime = ref<IIndicatorData>({});
const crashCount = ref<IIndicatorData>({});
const stutterCount = ref<IIndicatorData>({});
const networkErrorRate = ref<IIndicatorData>({});
const tableLoading = ref(false); //表格loading
const list = reactive({
  records: [],
  total: 0
});
const handleServiceData = async () => {
  loading.value = true;
  const appId = useApplicationStore.appId;
  const appName = usebreadcrumbStore.appOption;

  type FetchDataFunction = (data: { appid: string }) => Promise<{ value: number }>;
  type FetchDataResult = { unit: string; value: number; errorInfo: string };

  const fetchData = async (
    getDataFunction: FetchDataFunction,
    unit: string
  ): Promise<FetchDataResult> => {
    try {
      const res = await getDataFunction({ appid: appId, appName: appName });
      loading.value = false;
      return { unit, value: res.value, errorInfo: "error" };
    } catch (error) {
      loading.value = false;
      console.error(error);
      return { unit, value: 0, errorInfo: "error" };
    }
  };

  try {
    const [
      activeVisitorCountData,
      averageStartTimeData,
      crashCountData,
      stutterCountData,
      networkErrorRateData
    ] = await Promise.all([
      fetchData(getAverageDOMParsing, "DOM解析平均耗时（ms）"),
      fetchData(getAveragePageLoad, "页面加载平均耗时（ms）"),
      fetchData(getAverageFirstByte, "首字节平均耗时（ms）"),
      fetchData(getAverageResourceLoad, "资源加载平均耗时（ms）"),
      fetchData(getAverageTTFB, "TTFB平均耗时（ms）")
    ]);

    activeVisitorCount.value = activeVisitorCountData;
    averageStartTime.value = averageStartTimeData;
    crashCount.value = crashCountData;

    const setCountValue = (data: any) => {
      const countValue = { ...data };
      if (Number(data.value) > 0) {
        countValue.color = "#f56c6c";
      } else {
        countValue.color = undefined;
      }
      return countValue;
    };

    networkErrorRate.value = setCountValue(networkErrorRateData);
    stutterCount.value = setCountValue(stutterCountData);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};

const initPageViewChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getStatAveragePageLoad(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "页面加载平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      pageViewChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initApiDurationChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getStatAverageFirstByte(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "首字节平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      apiDurationChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initErrorLogsChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getStatAverageTTFB(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "TTFB平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      errorCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  getStartList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//列表参数
const pageParams = reactive({
  appid: "",
  appName: useBreadcrumbStore.appOption,
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//跳转详情
const toDetail = (row: any) => {
  router.push("/H5monitor/page-performance/detail");
  useBreadcrumbStore.setBreadcrumb(row.url);
  // useBreadcrumbStore.setMethod(row.type);
};
onMounted(async () => {
  handleServiceData();
  loadData();
  chartLoading.value = true;
  try {
    await Promise.all([initPageViewChart(), initApiDurationChart(), initErrorLogsChart()]);
  } finally {
    chartLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
