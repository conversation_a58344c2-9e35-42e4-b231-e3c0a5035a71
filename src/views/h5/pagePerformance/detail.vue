<template>
  <div class="flex justify-between indicator-wrapper mt-10px mb-10px">
    <BaseEcharts :options="stutterCountChartData" height="250px" />
    <BaseEcharts :options="AppVersionChartData" height="250px" />
    <BaseEcharts :options="AppDeviceChartData" height="250px" />
  </div>
  <div class="log-container">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="traceId">唯一标识：</label>
          <el-input id="traceId" v-model="pageParams.id" clearable placeholder="请输入唯一标识" />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="traceId">用户ID：</label>
          <el-input
            id="traceId"
            v-model="pageParams.userId"
            clearable
            placeholder="请输入用户ID："
          />
        </el-form-item>
        <el-form-item class="input-group">
          <label>浏览器：</label>
          <el-select placeholder="请选择浏览器" v-model="pageParams.browser" clearable filterable>
            <el-option v-for="item in browserOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>操作系统：</label>
          <el-select placeholder="请选择操作系统" v-model="pageParams.os" clearable filterable>
            <el-option v-for="item in osOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>省份：</label>
          <el-select placeholder="请选择省份" v-model="pageParams.provinces" clearable filterable>
            <el-option v-for="item in provincesOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label>运营商：</label>
          <el-select placeholder="请选择运营商" v-model="pageParams.operators" clearable filterable>
            <el-option v-for="item in operatorsOptions" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{ prop: 'count', order: 'descending' }"
        @sort-change="handleSortChange"
      >
        <my-column property="id" label="唯一标识">
          <template #default="{ row }">
            <span class="service-name" @click="shouDetail(row)">
              {{ row.id }}
            </span>
          </template>
        </my-column>

        <MyColumn property="loadPageTime" label="页面加载耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.loadPageTime + " ms" }}
            </span>
          </template>
        </MyColumn>
        <MyColumn property="ttfbTime" label="TTFB耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.ttfbTime + " ms" }}
            </span>
          </template>
        </MyColumn>
        <MyColumn property="resTime" label="资源加载耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.resTime + " ms" }}
            </span>
          </template>
        </MyColumn>
        <MyColumn property="firstPackTime" label="首字节耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.firstPackTime + " ms" }}
            </span>
          </template>
        </MyColumn>
        <MyColumn property="domAnalysisTime" label="DOM解析耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.domAnalysisTime + " ms" }}
            </span>
          </template>
        </MyColumn>
        <MyColumn property="tcpTime" label="TCP连接耗时" sortable="custom" width="190">
          <template #default="{ row }">
            <span>
              {{ row.tcpTime + " ms" }}
            </span>
          </template>
        </MyColumn>
      </MyTable>
    </div>
  </div>
  <div>
    <el-drawer v-model="logVisible" :title="title" size="50%">
      <TitlecCom :title="collapseDetailtitle"></TitlecCom>
      <div class="attr-panel">
        <div class="tab-content">
          <table class="detail-table">
            <tbody>
              <tr>
                <td class="label">浏览器</td>
                <td width="35%">{{ detailList.browser }}</td>
                <td class="label">操作系统</td>
                <td width="35%">{{ detailList.os }}</td>
              </tr>
              <tr>
                <td class="label">IP地址</td>
                <td width="35%">{{ detailList.ip }}</td>
                <td class="label">用户ID</td>
                <td width="35%">{{ detailList.userId }}</td>
              </tr>
              <tr>
                <td class="label">国家</td>
                <td width="35%">{{ detailList.country }}</td>
                <td class="label">省份</td>
                <td width="35%">{{ detailList.province }}</td>
              </tr>
              <tr>
                <td class="label">页面加载耗时</td>
                <td width="35%">{{ detailList.loadPageTime + " ms" }}</td>
                <td class="label">TTFB耗时</td>
                <td width="35%">{{ detailList.ttfbTime + " ms" }}</td>
              </tr>
              <tr>
                <td class="label">资源加载耗时</td>
                <td width="35%">{{ detailList.resTime + " ms" }}</td>
                <td class="label">首字节耗时</td>
                <td width="35%">{{ detailList.firstPackTime + " ms" }}</td>
              </tr>
              <tr>
                <td class="label">DOM解析耗时</td>
                <td width="35%">{{ detailList.domAnalysisTime + " ms" }}</td>
                <td class="label">DOM准备耗时</td>
                <td width="35%">{{ detailList.domReadyTime + " ms" }}</td>
              </tr>
              <tr>
                <td class="label">重定向耗时</td>
                <td width="35%">{{ detailList.redirectTime + " ms" }}</td>
                <td class="label">DNS查询耗时</td>
                <td width="35%">{{ detailList.dnsTime + " ms" }}</td>
              </tr>
              <tr>
                <td class="label">TTL耗时</td>
                <td width="35%">{{ detailList.ttlTime + " ms" }}</td>
                <td class="label">内容传输耗时</td>
                <td width="35%">{{ detailList.transTime + " ms" }}</td>
              </tr>
              <tr>
                <td class="label">TCP连接耗时</td>
                <td width="35%">{{ detailList.tcpTime + " ms" }}</td>
                <td class="label">首次绘制耗时</td>
                <td width="35%">{{ detailList.fptTime + " ms" }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  getDetailList,
  getOperators,
  getBrowserNames,
  getOS,
  getProvinces,
  getStatAveragePageLoad,
  getStatAverageFirstByte,
  getStatAverageTTFB
} from "@/api/h5/PagePerformance";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const useApplicationStore = applicationStore();
const useBreadcrumbStore = breadcrumbStore();
const stutterCountLoading = ref(false); //崩溃次数loading
const tableLoading = ref(false); //表格loading
const logVisible = ref(false); //抽屉是否显示
const stutterCountChartData = ref({}); //崩溃图表
const AppVersionChartData = ref({});
const AppDeviceChartData = ref({});
import TitlecCom from "@/components/TitleCom/index.vue";
const title = ref(""); //抽屉标题
const collapseDetailtitle = ref("基础信息"); //抽屉标题

//列表参数
const pageParams = reactive({
  appid: "",
  url: "",
  appName: useBreadcrumbStore.appOption,
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  operators: "",
  browser: "",
  os: "",
  id: "",
  userId: "",
  provinces: "",
  type: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  pageParams.url = useBreadcrumbStore.breadcrumbTitle;
  pageParams.type = useBreadcrumbStore.method;
  pageParams.id = pageParams.id.trim();
  pageParams.userId = pageParams.userId.trim();
  getDetailList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//网络下拉框
const operatorsOptions = ref([]);
const browserOptions = ref([]);
const osOptions = ref([]);
const provincesOptions = ref([]);
const dataToSend = reactive({
  appid: "",
  appName: useBreadcrumbStore.appOption,
  url: "",
  type: ""
});
// 运营商（下拉框）
function getOperatorsOption() {
  dataToSend.appid = useApplicationStore.appId;
  dataToSend.url = useBreadcrumbStore.breadcrumbTitle;
  dataToSend.type = useBreadcrumbStore.method;
  getOperators(dataToSend).then(response => {
    operatorsOptions.value = response.records;
  });
}
const params = reactive({
  appid: "",
  appName: useBreadcrumbStore.appOption,
  url: "",
  type: ""
});
// 浏览器（下拉框）
function getBrowserOption() {
  params.appid = useApplicationStore.appId;
  params.url = useBreadcrumbStore.breadcrumbTitle;
  params.type = useBreadcrumbStore.method;
  getBrowserNames(params).then(respone => {
    browserOptions.value = respone.records;
  });
}
// 操作系统（下拉框）
function getOSOption() {
  params.appid = useApplicationStore.appId;
  params.url = useBreadcrumbStore.breadcrumbTitle;
  params.type = useBreadcrumbStore.method;
  getOS(params).then(respone => {
    osOptions.value = respone.records;
  });
}
// 省份（下拉框）
function getProvincesOption() {
  params.appid = useApplicationStore.appId;
  params.url = useBreadcrumbStore.breadcrumbTitle;
  params.type = useBreadcrumbStore.method;
  getProvinces(params).then(respone => {
    provincesOptions.value = respone.records;
  });
}
//详情字段
const detailList = ref({
  browser: "",
  os: "",
  ip: "",
  userId: "",
  country: "",
  province: "",
  loadPageTime: "",
  ttfbTime: "",
  resTime: "",
  firstPackTime: "",
  domAnalysisTime: "",
  domReadyTime: "",
  redirectTime: "",
  dnsTime: "",
  ttlTime: "",
  transTime: "",
  tcpTime: "",
  fptTime: ""
});
//详情
const shouDetail = (row: any) => {
  logVisible.value = true;
  title.value = "页面性能详情 【" + row.id + "】";
  detailList.value = row;
};
// 页面加载平均耗时
const initStutterCountChart = async () => {
  stutterCountLoading.value = true;
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId,
    url: useBreadcrumbStore.breadcrumbTitle
  };
  try {
    const res = await getStatAveragePageLoad(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "页面加载平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      AppVersionChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

function resetSearch() {
  pageParams.id = "";
  pageParams.os = "";
  pageParams.userId = "";
  pageParams.operators = "";
  pageParams.browser = "";
  pageParams.provinces = "";
}
// 首字节平均耗时
const handleAppVersionTop5 = async () => {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId,
    url: useBreadcrumbStore.breadcrumbTitle
  };
  try {
    const res = await getStatAverageFirstByte(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "首字节平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      AppDeviceChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
// TTFB平均耗时
async function handleAppDeviceTop5() {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId,
    url: useBreadcrumbStore.breadcrumbTitle
  };
  try {
    const res = await getStatAverageTTFB(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "TTFB平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      stutterCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
}
onMounted(() => {
  loadData();
  getBrowserOption();
  getOperatorsOption();
  getOSOption();
  getProvincesOption();
  initStutterCountChart();
  handleAppVersionTop5();
  handleAppDeviceTop5();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}

.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.event-detail {
  padding: 16px 8px 20px 8px;
  // border-bottom: 1px solid #dedede;
  // border-top: 1px solid #dedede;
  margin-top: 10px;
}
.user-desc-container {
  font-size: 14px;
  color: #757575;
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  color: #757575;
  .desc {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 20px;
    line-height: 24px;
    margin-bottom: 10px;
  }
}
.head-title {
  color: #020202;
  font-weight: 600;
  font-size: 18px;
  margin-right: 10px;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    width: 260px;
    height: 550px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.attr-panel {
  flex: 1;
  margin-left: 10px;

  .span-panel {
    width: 750px;
    height: 875px;
    overflow-x: hidden;
    overflow-y: auto;
    table {
      border: 1px solid #ebeef5;
      border-collapse: collapse;

      tbody {
        tr {
          transition: background 0.5s;

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      td {
        padding: 12px;
        position: relative;

        .duration {
          position: absolute;
          align-items: center;
          padding-left: 15px;
          display: flex;
          left: 5px;
          right: 5px;
          top: 5px;
          bottom: 5px;

          &.success {
            background: #c1e488;
          }

          &.error {
            background: #ff9393;
          }
        }

        .bar {
          width: 3px;
          height: 18px;
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;

          &.success {
            background: #009431;
          }

          &.error {
            background: #e00000;
          }
        }

        .name {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 280px;
        }
      }

      thead {
        tr {
          td {
            background: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
