<template>
  <div class="flex justify-between">
    <div class="w-50%">
      <BaseEcharts :options="chartOptions" height="730px" v-loading="chartOptionsLoading" />
    </div>
    <div class="w-22%">
      <MyTable
        :data="tableData"
        :total="tableData.length"
        pagination-layout=""
        style="width: 100%"
        v-loading="tableLoading"
      >
        <my-column
          type="index"
          property="versionCount"
          label="排名"
          align="center"
          header-align="center"
          width="120"
          :show-overflow-tooltip="false"
        />
        <my-column property="province" align="center" header-align="center" label="省份" />
        <my-column
          property="count"
          align="center"
          header-align="center"
          label="用户数"
          width="120"
          :show-overflow-tooltip="false"
        >
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.count).fixValue }}{{ formatNums(row.count).unit || "" }}
            </span>
          </template>
        </my-column>
      </MyTable>
    </div>
    <div class="w-27% h-743px">
      <BaseEcharts
        :options="statOperatorData"
        height="280px"
        class="mb-10px"
        v-loading="StatOperatorLoading"
      />
      <Ranking
        v-loading="browserTop5Loading"
        class="w-100% mb-10px"
        style="height: 240px"
        :title="'用户浏览器（Top5）'"
        :rankingList="browserRankingList"
      ></Ranking>
      <Ranking
        v-loading="osTop5Loading"
        class="w-100% mb-10px"
        style="height: 240px"
        :title="'用户操作系统（Top5）'"
        :ranking-list="osRankingList"
      ></Ranking>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getStatProvince, getStatOperator, getBrowserTop5, getOsTop5 } from "@/api/h5/characters";
import * as echarts from "echarts";
import chaina from "@/components/china/china.json";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatNums } from "@/utils/formatStr";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
const tableLoading = ref(false);
const chartOptionsLoading = ref(false); //用户省份loading
const StatOperatorLoading = ref(false); //用户运营商top5 loading
const browserRankingList = ref<IRankItem[]>([]); //浏览器TOP5数据
const browserTop5Loading = ref(false); //浏览器TOP5 loading
function handleBrowserTop5Data() {
  let total = 0;
  browserTop5Loading.value = true;
  getBrowserTop5({ appid: useApplicationStore.appId, appName: usebreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0 && res.records) {
        res.records.forEach((item: { count: number }) => {
          total += item.count;
        });
        res.records.map((item: { browser: string; count: number }) => {
          browserRankingList.value.push({
            name: item.browser,
            proportion: (item.count / total) * 100,
            totalScore: item.count,
            color: "#445fde",
            unit: "人"
          });
        });
      }
      browserTop5Loading.value = false;
    })
    .catch(err => {
      console.log(err);
      browserTop5Loading.value = false;
    });
}
//操作系统TOP5
const osTop5Loading = ref(false); //操作系统TOP5 loading
const osRankingList = ref<IRankItem[]>([]); //操作系统TOP5
function handleOsTop5Data() {
  osTop5Loading.value = true;
  let total = 0;
  getOsTop5({ appid: useApplicationStore.appId, appName: usebreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0 && res.records) {
        res.records.forEach((item: { count: number }) => {
          total += item.count;
        });
        res.records.map((item: { os: string; count: number }) => {
          osRankingList.value.push({
            name: item.os,
            proportion: (item.count / total) * 100,
            totalScore: item.count,
            color: "#445fde",
            unit: "人"
          });
        });
      }
      osTop5Loading.value = false;
    })
    .catch(err => {
      osTop5Loading.value = false;
      console.log(err);
    });
}
const tableData = ref([]);
//! 通用配置
const setting = {
  title: {
    // text: "",
    x: "10px",
    y: "1px",
    textStyle: {
      fontSize: 16,
      fontWeight: "400",
      color: "#000000"
    }
  },
  graphic: [
    {
      type: "rect",
      shape: { x: 0, y: 5, width: 4, height: 15 },
      style: { fill: "#3375f9" },
      z: 100
    }
  ],
  grid: {
    left: "1px",
    right: "1%",
    bottom: "1px",
    top: "60px",
    containLabel: true
  }
};
echarts.registerMap("china", chaina as any);
const chartOptions = ref({
  ...setting,
  title: {
    ...setting.title,
    text: "用户省份分布"
  },
  tooltip: {
    trigger: "item"
  },
  visualMap: {
    left: "right",
    top: "bottom",
    min: 0,
    max: 0,
    inRange: {
      color: ["#f2e2a0", "#d88273", "#bf444c"]
    },
    text: ["High", "Low"],
    calculable: true
  },
  series: [
    {
      name: "用户省份分布",
      type: "map",
      map: "china",
      roam: false,
      emphasis: {
        label: {
          show: true
        }
      },
      data: []
    }
  ]
});
function statProvince() {
  chartOptionsLoading.value = true;
  tableLoading.value = true;
}
getStatProvince({
  appid: useApplicationStore.appId,
  appName: usebreadcrumbStore.appOption
})
  .then(res => {
    if (res.code === 0) {
      tableData.value = res.records;
      tableLoading.value = false;
      const processedRecords = res.records.map((record: { province: string; count: number }) => ({
        name: record.province.replace(/省$/, ""),
        value: record.count
      }));

      if (processedRecords.length > 0) {
        let maxValue = Math.max(...processedRecords.map((record: { value: any }) => record.value));
        chartOptions.value = {
          ...chartOptions.value,
          series: [
            {
              ...chartOptions.value.series[0],
              data: processedRecords
            }
          ],
          visualMap: {
            ...chartOptions.value.visualMap,
            max: maxValue
          }
        };
      } else {
        chartOptions.value = {
          ...chartOptions.value,
          series: [
            {
              ...chartOptions.value.series[0],
              data: processedRecords
            }
          ],
          visualMap: {
            ...chartOptions.value.visualMap,
            max: 1
          }
        };
      }
    }
    chartOptionsLoading.value = false;
  })
  .catch(err => {
    console.log(err);
    chartOptionsLoading.value = false;
    tableLoading.value = false;
  });
const statOperatorData = ref({
  ...setting,
  title: {
    ...setting.title,
    text: "用户运营商"
  },
  tooltip: {
    trigger: "item"
  },
  legend: {
    bottom: "1%",
    left: "center",
    type: "scroll"
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "65%"],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 15
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
});
const initstatOperatorData = async () => {
  StatOperatorLoading.value = true;
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getStatOperator(queue);
    if (res.code === 0 && res.records) {
      const processedRecords = res.records.map((item: { count: number; operator: string }) => ({
        value: item.count,
        name: item.operator
      }));
      statOperatorData.value = {
        ...statOperatorData.value,
        series: [
          {
            ...statOperatorData.value.series[0],
            data: processedRecords
          }
        ]
      };
    }
    StatOperatorLoading.value = false;
  } catch (error) {
    StatOperatorLoading.value = false;
    console.error(error);
  }
};
onMounted(() => {
  statProvince();
  handleBrowserTop5Data();
  handleOsTop5Data();
  initstatOperatorData();
});
</script>

<style lang="scss" scoped>
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
}
</style>
