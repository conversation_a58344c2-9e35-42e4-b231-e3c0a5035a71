<template>
  <div v-loading="loading" class="flex justify-between indicator-wrapper">
    <Indicator :value="allErrorCount.value" :unit="'错误总数'" :color="'#f56c6c'"></Indicator>
    <Indicator :value="jsErrorCount.value" :unit="'JS错误数'" :color="'#f56c6c'"></Indicator>
    <Indicator :value="ajaxErrorCount.value" :unit="'AJAX错误数'" :color="'#f56c6c'"></Indicator>
    <Indicator :value="otherErrorCount.value" :unit="'其他错误数'" :color="'#f56c6c'"></Indicator>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts :options="statJsErrorData" v-loading="statJsErrorLoading" height="250px" />
    <BaseEcharts :options="statAjaxErrorData" v-loading="statAjaxErrorLoading" height="250px" />
    <BaseEcharts :options="statOtherErrorData" v-loading="statOtherErrorLoading" height="250px" />
  </div>
  <div class="log-container mt-10px">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="traceId">用户ID：</label>
          <el-input id="traceId" v-model="pageParams.userId" clearable placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="appVersion">错误类型：</label>
          <el-select
            placeholder="请选择错误类型"
            v-model="pageParams.category"
            clearable
            filterable
          >
            <el-option
              v-for="appVersion in selectProvinces"
              :key="appVersion"
              :label="appVersion"
              :value="appVersion"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="appVersion">浏览器：</label>
          <el-select
            placeholder="请选择浏览器"
            v-model="pageParams.browserName"
            clearable
            filterable
          >
            <el-option
              v-for="appVersion in selectBrowserNames"
              :key="appVersion"
              :label="appVersion"
              :value="appVersion"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="appVersion">操作系统：</label>
          <el-select placeholder="请选择操作系统" v-model="pageParams.os" clearable filterable>
            <el-option
              v-for="appVersion in selectOs"
              :key="appVersion"
              :label="appVersion"
              :value="appVersion"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="input-group">
          <label for="appVersion">运营商：</label>
          <el-select placeholder="请选择运营商" v-model="pageParams.operator" clearable filterable>
            <el-option
              v-for="appVersion in selectOperators"
              :key="appVersion"
              :label="appVersion"
              :value="appVersion"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" style="flex: 1" @click="search"> 查询 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <MyTable
        v-loading="tableLoading"
        :data="errorCountList"
        :total="errorCountTotal"
        style="width: 100%"
        class="cursor-pointer"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        @sort-change="handleSortChange"
      >
        <MyColumn property="url" label="错误页面" width="350">
          <template #default="{ row }">
            <span class="service-name" @click="viewDetail(row)">
              {{ row.url }}
            </span>
          </template>
        </MyColumn>
        <my-column property="category" label="错误类型">
          <template #default="{ row }">
            <el-tag :type="'danger'">{{ row.category }} </el-tag>
          </template>
        </my-column>
        <my-column property="userId" label="用户ID"> </my-column>
        <my-column property="os" label="用户操作系统"> </my-column>
        <my-column property="browser" label="用户浏览器"> </my-column>
        <my-column property="time" label="错误时间"> </my-column>
        <my-column property="message" label="错误信息"> </my-column>
      </MyTable>
    </div>
    <div>
      <el-drawer v-model="logVisible" :title="topTitle" size="50%">
        <TitleCom :title="'基础信息'"></TitleCom>
        <div class="attr-panel">
          <div class="tab-content">
            <table class="detail-table">
              <tbody>
                <tr>
                  <td class="label">id</td>
                  <td width="35%">{{ errorDetailData.id }}</td>
                  <td class="label">ip</td>
                  <td width="35%">{{ errorDetailData.ip }}</td>
                </tr>
                <tr>
                  <td class="label">发生时间</td>
                  <td width="35%">{{ errorDetailData.timestamp }}</td>
                  <td class="label">错误类型</td>
                  <td width="35%">{{ errorDetailData.category }}</td>
                </tr>
                <tr>
                  <td class="label">浏览器</td>
                  <td width="35%">
                    {{ errorDetailData.browserName }} {{ errorDetailData.browserVersion }}
                  </td>
                  <td class="label">操作系统</td>
                  <td width="35%">{{ errorDetailData.os }}</td>
                </tr>
                <tr>
                  <td class="label">错误页面</td>
                  <td colspan="3">{{ errorDetailData.url }}</td>
                </tr>
                <tr>
                  <td class="label">错误地址</td>
                  <td colspan="3">{{ errorDetailData.errorUrl }}</td>
                </tr>
                <tr>
                  <td class="label">错误信息</td>
                  <td colspan="3">{{ errorDetailData.message }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="event-detail mt-20px">
          <TitleCom :title="'错误堆栈'"></TitleCom>
          <div style="margin-top: 10px">
            <div style="white-space: pre-wrap; font-size: 14px">
              {{ errorDetailData.stack }}
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getAllErrorCount,
  getJsErrorCount,
  getAjaxErrorCount,
  getOtherErrorCount,
  getStatJsErrorCount,
  getStatAjaxErrorCount,
  getStatOtherErrorCount,
  getErrorCategory,
  getOperators,
  getBrowserNames,
  getOs,
  IErrorListResponse,
  getErrorLogsList,
  IErrorItem,
  getErrorDetail,
  IErrorDetail,
  IErrorDetailItem
} from "@/api/h5/errorAnalysis";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { breadcrumbStore } from "@/store/modules/breadcurmb";

interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const useBreadcrumbStore = breadcrumbStore();
const loading = ref(false);
const tableLoading = ref(false);
const errorCountList = ref<IErrorItem[]>([]);
const errorCountTotal = ref(0);

const selectOperators = ref<string[]>([]);
const selectProvinces = ref<string[]>([]);
const selectOs = ref<string[]>([]);
const selectBrowserNames = ref<string[]>([]);

const allErrorCount = ref<IIndicatordata>({});
const jsErrorCount = ref<IIndicatordata>({});
const ajaxErrorCount = ref<IIndicatordata>({});
const otherErrorCount = ref<IIndicatordata>({});

const statOtherErrorData = ref({});
const statJsErrorData = ref({});
const statAjaxErrorData = ref({});

const statOtherErrorLoading = ref(false);
const statJsErrorLoading = ref(false);
const statAjaxErrorLoading = ref(false);

const logVisible = ref(false); //抽屉是否显示
const topTitle = ref("");
const errorDetailData = ref({
  id: "",
  timestamp: "",
  ip: "",
  country: "",
  province: "",
  city: "",
  operator: "",
  os: "",
  browserName: "",
  browserVersion: "",
  userAgent: "",
  sessionId: "",
  userId: "",
  url: "",
  category: "",
  grade: "",
  errorUrl: "",
  message: "",
  stack: ""
});

// 上面的请求数，平均耗时，错误率，慢请求率
async function getInterErrorCountData() {
  try {
    loading.value = true;
    const [allErrorCountdata, jsErrorCountData, ajaxErrorCountData, otherErrorCountData] =
      await Promise.all([
        getAllErrorCount({}),
        getJsErrorCount({}),
        getAjaxErrorCount({}),
        getOtherErrorCount({})
      ]);
    allErrorCount.value = {
      value: allErrorCountdata.value,
      errorInfo: "error"
    };
    jsErrorCount.value = {
      value: jsErrorCountData.value,
      errorInfo: "error"
    };
    ajaxErrorCount.value = {
      value: ajaxErrorCountData.value,
      errorInfo: "error"
    };
    otherErrorCount.value = {
      value: otherErrorCountData.value,
      errorInfo: "error"
    };
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}

function viewDetail(row: any) {
  try {
    getErrorDetail(row.id).then(res => {
      if (res.code === 0) {
        errorDetailData.value = res.entity || {};
      }
      topTitle.value = "错误详情 【" + row.id + "】";
      logVisible.value = true;
    });
  } catch (error) {
    console.log(error);
  }

  // title.value = "卡顿详情 【" + row.deviceId + "】";
}

onMounted(async () => {
  getStatOtherErrorData();
  getStatAjaxErrorData();
  getStatJsErrorData();
  getSelectAll();
  getInterErrorCountData();
  await getAllErrorData();
});

//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  id: "",
  userId: "",
  appVersion: "",
  os: "",
  browserName: "",
  category: "",
  operator: ""
});
function resetSearch() {
  pageParams.value.os = "";
  pageParams.value.browserName = "";
  pageParams.value.category = "";
  pageParams.value.operator = "";
  pageParams.value.userId = "";
  pageParams.value.id = "";
}
function search() {
  pageParams.value.page = 1;
  getAllErrorData();
}

async function getAllErrorData() {
  tableLoading.value = true;
  pageParams.value.userId = pageParams.value.userId.trim();
  try {
    const res: IErrorListResponse = await getErrorLogsList(pageParams.value);
    if (res.code === 0) {
      errorCountList.value = res.records.map(item => {
        return {
          ...item
        };
      });
      errorCountTotal.value = Number(res.total);
    }
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
    console.log(error);
  }
}

async function getSelectAll() {
  try {
    const [browserNames, provinces, os, operators] = await Promise.all([
      getBrowserNames({}),
      getErrorCategory({}),
      getOs({}),
      getOperators({})
    ]);
    selectBrowserNames.value = browserNames?.records || [];
    selectProvinces.value = provinces?.records || [];
    selectOs.value = os?.records || [];
    selectOperators.value = operators?.records || [];
  } catch (error) {
    console.log(error);
  }
}
// 获取错误率统计数据
const getStatOtherErrorData = async () => {
  statOtherErrorLoading.value = true;
  try {
    const res = await getStatOtherErrorCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "其他错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statOtherErrorData.value = getChartOptions(params);
    }
    statOtherErrorLoading.value = false;
  } catch (error) {
    statOtherErrorLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatJsErrorData = async () => {
  statJsErrorLoading.value = true;
  try {
    const res = await getStatJsErrorCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "JS错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statJsErrorData.value = getChartOptions(params);
    }
    statJsErrorLoading.value = false;
  } catch (error) {
    statJsErrorLoading.value = false;
    console.log("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getStatAjaxErrorData = async () => {
  statAjaxErrorLoading.value = true;
  try {
    const res = await getStatAjaxErrorCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "AJAX错误数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statAjaxErrorData.value = getChartOptions(params);
    }
    statAjaxErrorLoading.value = false;
  } catch (error) {
    statAjaxErrorLoading.value = false;
    console.log("Error logs:", error);
  }
};

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getAllErrorData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getAllErrorData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getAllErrorData();
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}

.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 400px;

  .search-container {
    width: 260px;
    height: 450px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.input-group {
  margin-bottom: 10px;
}
.attr-panel {
  flex: 1;
  margin-left: 10px;

  .span-panel {
    width: 750px;
    height: 875px;
    overflow-x: hidden;
    overflow-y: auto;
    table {
      border: 1px solid #ebeef5;
      border-collapse: collapse;

      tbody {
        tr {
          transition: background 0.5s;

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      td {
        padding: 12px;
        position: relative;

        .duration {
          position: absolute;
          align-items: center;
          padding-left: 15px;
          display: flex;
          left: 5px;
          right: 5px;
          top: 5px;
          bottom: 5px;

          &.success {
            background: #c1e488;
          }

          &.error {
            background: #ff9393;
          }
        }

        .bar {
          width: 3px;
          height: 18px;
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;

          &.success {
            background: #009431;
          }

          &.error {
            background: #e00000;
          }
        }

        .name {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 280px;
        }
      }

      thead {
        tr {
          td {
            background: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
