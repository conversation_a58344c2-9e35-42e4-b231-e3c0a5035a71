<template>
  <div>
    <div v-loading="loading" class="flex justify-between indicator-wrapper">
      <Indicator :value="requestCountData.value" :unit="'请求总数'"></Indicator>
      <Indicator :value="errorCountData.value" :unit="'请求失败数'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="errorRate.value" :unit="'请求失败率（%）'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="interfaceAvtDutation.value" :unit="'平均响应时长（ms）'"></Indicator>
    </div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <BaseEcharts v-loading="requestCountLoading" :options="statRequestCountData" height="250px" />
      <BaseEcharts
        v-loading="statErrorCountsLoading"
        :options="statErrorCountsData"
        height="250px"
      />
      <BaseEcharts
        v-loading="h5statAvgDurationLoading"
        :options="h5statAvgDurationData"
        height="250px"
      />
    </div>
  </div>

  <MyTable
    class="mt-10px"
    :data="interPerforList"
    :total="interPerforTotal"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'count && slowCount && errorCount && avgDuration && slowRate && errorRate',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="summary" label="请求路径">
      <template #default="scope">
        <span class="url-name" @click="viewDetail(scope)">
          {{ scope.row.url }}
        </span>
      </template>
    </my-column>
    <my-column property="count" label="请求总数" sortable="custom" width="200" />
    <my-column property="errorCount" label="请求错误数" sortable="custom" width="200"> </my-column>
    <my-column property="errorRate" label="请求错误率" sortable="custom" width="200"> </my-column>
    <my-column property="avgDuration" label="平均响应时长" sortable="custom" width="200">
    </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import {
  getInterCount,
  getInterAvgDuration,
  getInterErrorRate,
  getInterNums,
  getH5ErrorCount,
  getH5AvgDurationRate,
  getH5RequestCount,
  getInterPerformanceList,
  IInterPerforList,
  IInterPerforItem
} from "@/api/h5/interPerformance";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";

const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false);
const tableLoading = ref(false);
const statErrorCountsLoading = ref(false);
const requestCountLoading = ref(false);
const h5statAvgDurationLoading = ref(false);

const requestCountData = ref<IIndicatordata>({});
const interfaceAvtDutation = ref<IIndicatordata>({});
const errorRate = ref<IIndicatordata>({});
const errorCountData = ref<IIndicatordata>({});

const interPerforList = ref<IInterPerforItem[]>([]);
const interPerforTotal = ref(0);
const statErrorCountsData = ref({});
const statRequestCountData = ref({});
const h5statAvgDurationData = ref({});
onMounted(() => {
  getH5InterPerforListData();
  getStatH5ErrorCountData();
  getH5AvgDurationData();
  getStatEequestCountData();
  getInterCountData();
});
// 上面的请求数，平均耗时，错误率，慢请求率
async function getInterCountData() {
  try {
    loading.value = true;
    const [interfaceCountdata, interAvtDutationData, errorRateData, interErrorData] =
      await Promise.all([
        getInterCount({}),
        getInterAvgDuration({}),
        getInterErrorRate({}),
        getInterNums({})
      ]);
    requestCountData.value = {
      unit: "请求总数",
      value: interfaceCountdata.value,
      errorInfo: "error"
    };
    interfaceAvtDutation.value = {
      unit: "平均响应时长",
      value: interAvtDutationData.value,
      errorInfo: "error"
    };
    errorRate.value = {
      unit: "错误率",
      value: errorRateData.value,
      errorInfo: "error"
    };
    errorCountData.value = {
      unit: "错误数",
      value: interErrorData.value,
      errorInfo: "error"
    };
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}

//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getH5InterPerforListData() {
  tableLoading.value = true;
  try {
    const res: IInterPerforList = await getInterPerformanceList(pageParams.value);
    if (res.code === 0) {
      interPerforList.value = res?.records.map(item => {
        return {
          ...item,
          avgDuration: item.avgDuration + "ms"
        };
      });
      interPerforTotal.value = Number(res.total);
    }
    tableLoading.value = false;
  } catch (error) {
    tableLoading.value = false;
    console.log(error);
  }
}
// 获取错误率统计数据

const getStatH5ErrorCountData = async () => {
  statErrorCountsLoading.value = true;
  try {
    const res = await getH5ErrorCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求失败数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorCountsData.value = getChartOptions(params);
    }
    statErrorCountsLoading.value = false;
  } catch (error) {
    statErrorCountsLoading.value = false;
    console.log("Error logs:", error);
  }
};

const getStatEequestCountData = async () => {
  try {
    requestCountLoading.value = true;
    const res = await getH5RequestCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statRequestCountData.value = getChartOptions(params);
    }
    requestCountLoading.value = false;
  } catch (error) {
    requestCountLoading.value = false;
    console.log("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getH5AvgDurationData = async () => {
  try {
    h5statAvgDurationLoading.value = true;
    const res = await getH5AvgDurationRate({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "平均响应时长统计",
        name: " ms",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      h5statAvgDurationData.value = getChartOptions(params);
    }
    h5statAvgDurationLoading.value = false;
  } catch (error) {
    h5statAvgDurationLoading.value = false;
    console.log("Error logs:", error);
  }
};
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getH5InterPerforListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getH5InterPerforListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getH5InterPerforListData();
};
// 查看详情
const viewDetail = (scope: any) => {
  router.push("/H5monitor/ajax-performance/detail");
  useBreadcrumbStore.setBreadcrumb(scope.row.url);
  useBreadcrumbStore.setMethod(scope.row.method);
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.url-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
