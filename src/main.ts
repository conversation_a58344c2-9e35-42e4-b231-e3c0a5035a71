// import "./assets/main.css";
import "@/styles/index.scss";

import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
// SVG插件配置
import "virtual:svg-icons-register";
// 引入ElementPlus
import ElementPlus from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
// 引入ElementPlus所有图标
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
// 引入ElementPlus的暗黑模式css
import "element-plus/theme-chalk/dark/css-vars.css";
// 引入vxe-table表格
// import VxeTable from "vxe-table";
// import "vxe-table/lib/style.css";
// uncoss防止覆盖ElementPlus 的 el-button的css，所有放置最下方
import "uno.css";
// 引入styles
import "@/styles/index.scss";
import APM_AgentMointor from "apmagent-monitor";

// 创建app
const app = createApp(App);

// 注册ElementPlus所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

setupStore(app);
app.use(router);
app.use(ElementPlus, { locale: zhCn });
// app.use(VxeTable);
app.mount("#app");

const debounce = (callback: (...args: any[]) => void, delay: number) => {
  let tid: any;
  return function (...args: any[]) {
    const ctx = self;
    tid && clearTimeout(tid);
    tid = setTimeout(() => {
      callback.apply(ctx, args);
    }, delay);
  };
};

const _ = (window as any).ResizeObserver;
(window as any).ResizeObserver = class ResizeObserver extends _ {
  constructor(callback: (...args: any[]) => void) {
    callback = debounce(callback, 20);
    super(callback);
  }
};
const APMConfig = {
  collector: "http://139.9.94.201:4318/h5/041925",
  serviceName: "test_config1",
  serviceVersion: "2.0.0",
  userId: "456219",
  sessionId: "18151551555",
  vueInstance: app
};
// const apm = new APM_AgentMointor(APMConfig);
// apm.initMonitorAPM();
// app.config.globalProperties.$APM_AgentMonitor = apm;
// app.config.errorHandler = (err, vm) => {
//   console.log(err);
//   apm.handleErrorAPM(err);
// };
// apm.handlePerformanceAPM();
export default app;
