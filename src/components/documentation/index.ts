// 定义完整的操作指南内容
export const guideContents = {
  TopologyView: {
    title: "拓扑图",
    content: `
      <p><strong>拓扑图页说明：</strong></p>
      <ul>
        <li>可视化展示服务、主机间调用关系和依赖拓扑</li>
        <li>图标边框颜色反映健康状态（蓝色：正常，红色：异常，灰色：断开）</li>
        <li>连接线展示调用关系和响应时间</li>
        <li>首次加载没有拓扑图数据，需要在系统管理>功能管理>拓扑设置中进行拓扑图绘制</li>
          <strong>操作说明</strong>：
          <li>当前页仅支持拖拽和缩放查看细节</li>
        <li>鼠标悬浮至图标时显示对应概览数据</li>
        <li>双击可查看节点详细信息</li>
        <li>右侧操作栏可对拓扑图整体进行操作</li>
      </ul>
    `
  },
  // ==================== 服务监测模块 ====================
  dashboard: {
    title: "服务监测",
    content: `
      <p><strong>服务监测模块说明：</strong></p>
      <ul>
        <li>监控所有服务的运行状态和性能指标</li>
        <li>支持按时间范围筛选数据</li>
        <li>点击图表可查看详细数据</li>
        <li>支持告警阈值设置</li>
      </ul>
    `
  },
  overview: {
    title: "全局概览",
    content: `
      <ul>
        <li>
        <strong>全局概览页说明</strong>：
        <br>
        <li>对单个应用中的所有服务进行监测后得到的数据概览</li>
        <li>多种方式展示了监测的服务数量和状态（请求数，错误数，错误日志数，平均耗时）</li>
        <li>针对所有服务的请求数，错误数，平均耗时进行排名，展示对应次数最多的前五名</li>
        <li>
          <strong>数据概览操作</strong>：
          <span class="highlight">
          <br>
          点击服务数</span> → 跳转至<el-tag type="success">服务列表</el-tag>页面
          <br>
          <span class="highlight">点击错误日志数</span> → 跳转至<el-tag type="danger">服务日志</el-tag>分析
        </li>
        <li>
          <strong>数据图表展示</strong>：
          <br>
          鼠标悬停图表数据点 → 显示具体数值和时间点
          <br>
          鼠标悬停在图表上时右上角出现放大按钮，点击可放大图表查看
        </li>
          <strong>Top5排行操作</strong>：
        <li>点击任意服务名称 → 跳转至单个服务分析页</li>
      </ul>
    `
  },
  topology: {
    title: "链路图",
    content: `
      <p><strong>链路图页说明：</strong></p>
           <ul>
        <li>可视化展示服务间调用关系和依赖拓扑</li>
        <li>图标边框颜色反映健康状态（蓝色：正常，红色：异常）</li>
        <li>连接线展示调用关系和响应时间</li>
        <li>鼠标悬浮至图标或连接线时显示对应概览数据</li>
        <li>支持拖拽布局和缩放查看细节</li>
      </ul>
      <p><strong>操作说明：</strong></p>
      <ul>
        <li>左键点击节点可查看对应服务详情，也可以点击实例监测跳转到服务详情内的实例监测快速查看</li>
        <li>支持下载当前链路视图</li>
      </ul>
    `
  },
  serviceList: {
    title: "服务列表",
    content: `
      <p><strong>服务列表页说明：</strong></p>
      <ul>
        <li>查看所有已注册服务以列表的方式进行展示</li>
        <li>展示单个服务的请求数，错误数，错误率，平均耗时信息</li>
        <li>通过服务名称左侧的色条快速找到有错误的服务</li>
      </ul>
      <p><strong>操作说明：</strong></p>
      <ul>
        <li>支持按关键字搜索服务</li>
        <li>点击服务名称或详情进入单个服务分析页查看监控指标</li>
        <li>点击"调用链"跳转到调用链分析页，并自动带上当前服务名称作为条件查询</li>
        <li>点击"日志分析"跳转到服务日志页，并自动带上当前服务名称作为条件查询</li>
        <li>点击"编辑别名"可以对当前服务添加或编辑别名，添加成功后会在服务名称中以XXXX[XXX]方式展示</li>
      </ul>
    `
  },
  CallChain: {
    title: "调用链分析",
    content: `
      <p><strong>调用链分析页说明：</strong></p>
      <ul>
        <li>追踪分布式请求的完整调用链路</li>
        <li>查看每个环节的耗时、状态和异常信息</li>
        <li>对单个调用链的跨度进行分析查看</li>
        </ul>
        <p><strong>操作说明：</strong></p>
        <ul>
        <li>支持服务名、状态码、耗时等多维度条件查询</li>
        <li>点击Trace ID或详情可查看详细调用参数和堆栈信息</li>
        <li>详情展开后可通过点击左侧关系列表，查看对应跨度信息详情</li>
        <li>点击左下角跨度属性，资源属性，事件，日志查看对应信息</li>
      </ul>
    `
  },
  querylog: {
    title: "服务日志",
    content: `
      <p><strong>服务日志页说明：</strong></p>
      <ul>
        <li>展示所有服务上报的日志，可通过条件筛选精确查询</li>
      <p><strong>操作说明：</strong></p>
        <li>支持按服务名、日志级别、日志域等多维度条件查询</li>
        <li>点击详情查看对应日志详细信息</li>
        <li>可以点击复制按钮复制相应内容，在单个服务分析页中的对应页面粘贴搜索条件</li>
      </ul>
    `
  },
  hostList: {
    title: "主机列表",
    content: `
      <p><strong>主机列表页说明：</strong></p>
      <ul>
        <li>查看所有主机的资源使用情况、健康状态、设备信息</li>
        <li>以图表的形式展示对应主机磁盘的核心指标</li>
        <li>实时监控CPU、内存、磁盘、网络等核心指标</li>
      <p><strong>操作说明：</strong></p>
        <li>可以通过搜索主机ID关键字快速找到对应主机</li>
        <li>点击主机ID或查看详情展开磁盘详情，查看磁盘，内存，网络的详细数据图表</li>
        <li>点击处理器（CPU）下的详情，查看CPU详细数据</li>
        <li>点击存储下的详情，查看存储详细数据</li>
      </ul>
    `
  },
  serviceOverview: {
    title: "服务概览",
    content: `
          <ul>
        <strong>服务概览页说明</strong>：
        <br>
        <li>对单个服务进行监测后得到的数据概览</li>
        <li>多种方式展示了监测的服务数量和状态（请求数，错误数，错误日志数，平均耗时）</li>
        <li>针对服务的Endpoint进行排名，展示对应次数最多的前五名</li>
        <li>
          <strong>数据图表展示</strong>：
          <br>
          鼠标悬停图表数据点 → 显示具体数值和时间点
          <br>
          鼠标悬停在图表上时右上角出现放大按钮，点击可放大图表查看
        </li>
          <strong>Top5排行操作</strong>：
        <li>点击任意排行名称 → 跳转至Endpoint监测页</li>
      </ul>
    `
  },
  "service-topology": {
    title: "服务链路",
    content: `
      <p><strong>链路图页说明：</strong></p>
           <ul>
        <li>可视化展示服务间调用关系和依赖拓扑</li>
        <li>图标边框颜色反映健康状态（蓝色：正常，红色：异常）</li>
        <li>连接线展示调用关系和响应时间</li>
        <li>鼠标悬浮至图标或连接线时显示对应概览数据</li>
        <li>支持拖拽布局和缩放查看细节</li>
      </ul>
      <p><strong>操作说明：</strong></p>
      <ul>
        <li>左键点击节点可查看对应服务详情，也可以点击实例监测跳转到服务详情内的实例监测快速查看</li>
        <li>支持下载当前链路视图</li>
      </ul>
    `
  },
  "instance-monitor": {
    title: "实例监测",
    content: `
      <p><strong>实例监测页说明：</strong></p>
      <ul>
        <li>监测单个服务实例的运行状态和性能指标</li>
        <li>支持查看JVM、线程池、内存等详细指标</li>
        <li>可对比不同实例的性能差异</li>
        <li>支持实例标签管理</li>
      </ul>
      <strong>数据图表展示</strong>：
          <br>
          鼠标悬停图表数据点 → 显示具体数值和时间点
          <br>
          鼠标悬停在图表上时右上角出现放大按钮，点击可放大图表查看
      <p><strong>实例监测操作说明：</strong></p>
      <ul>
        <li>支持按条件查询，可通过在之前的服务日志页快速定位实例</li>
        <li>点击实例ID或详情可以查看当前实例的详细信息</li>
        <li>点击"JVM监测"可以查看当前实例的JVM的图标数据</li>
        <li>点击"调用链"可以跳转到调用链分析并携带当前实例ID进行条件查询</li>
        <li>表格支持排序功能</li>
      </ul>
    `
  },
  "service-endponit": {
    title: "Endpoint监测",
    content: `
      <p><strong>Endpoint监测操作说明：</strong></p>
      <ul>
        <li>监控服务接口级别的性能数据</li>
        <li>分析慢接口和异常接口</li>
        <li>支持按HTTP方法、状态码筛选</li>
        <li>可查看接口参数样例和文档</li>
        <li>支持接口级别的熔断设置</li>
      </ul>
    `
  },
  "service-dependent": {
    title: "依赖服务",
    content: `
      <p><strong>依赖服务操作说明：</strong></p>
      <ul>
        <li>查看当前服务依赖的外部资源</li>
        <li>包括数据库、缓存、消息队列等</li>
        <li>分析依赖服务的性能影响</li>
        <li>支持配置依赖服务的熔断策略</li>
        <li>可查看依赖调用的历史趋势</li>
      </ul>
    `
  },
  callChainNoServie: {
    title: "调用链分析",
    content: `
      <p><strong>调用链分析操作说明：</strong></p>
      <ul>
        <li>针对无服务注册的调用链分析</li>
        <li>追踪通过网关或直接IP调用的请求</li>
        <li>支持按客户端IP、用户ID等筛选</li>
        <li>可分析网络延迟和重试情况</li>
        <li>支持生成调用链报告</li>
      </ul>
    `
  },
  "sql-analysis": {
    title: "SQL分析",
    content: `
      <p><strong>SQL分析操作说明：</strong></p>
      <ul>
        <li>监控数据库访问性能</li>
        <li>分析慢SQL和执行计划</li>
        <li>支持按数据库类型、表名筛选</li>
        <li>可查看SQL执行频率和耗时分布</li>
        <li>支持SQL优化建议</li>
      </ul>
    `
  },
  "nosql-analysis": {
    title: "NoSQL分析",
    content: `
      <p><strong>NoSQL分析操作说明：</strong></p>
      <ul>
        <li>监控Redis/MongoDB等NoSQL访问</li>
        <li>分析大Key和热Key问题</li>
        <li>支持按命令类型、集群节点筛选</li>
        <li>可查看缓存命中率和序列化耗时</li>
        <li>支持配置缓存策略</li>
      </ul>
    `
  },
  "mq-analysis": {
    title: "MQ分析",
    content: `
      <p><strong>MQ分析操作说明：</strong></p>
      <ul>
        <li>监控消息队列的生产消费情况</li>
        <li>分析消息积压和延迟问题</li>
        <li>支持按Topic、消费者组筛选</li>
        <li>可查看消息轨迹和重试情况</li>
        <li>支持配置消息告警阈值</li>
      </ul>
    `
  },

  // ==================== H5监测模块 ====================
  H5monitor: {
    title: "H5监测",
    content: `
      <p><strong>H5监测模块说明：</strong></p>
      <ul>
        <li>全方位监控H5页面性能</li>
        <li>分析用户访问行为和体验</li>
        <li>追踪前端错误和异常</li>
        <li>支持多维度数据下钻分析</li>
      </ul>
    `
  },
  H5overview: {
    title: "H5概览",
    content: `
      <p><strong>H5概览操作说明：</strong></p>
      <ul>
        <li>查看H5应用整体性能指标</li>
        <li>监控PV、UV、跳出率等访问数据</li>
        <li>支持按时间、版本、渠道等多维度筛选</li>
        <li>可对比不同时段的性能变化</li>
        <li>支持关键指标阈值告警</li>
      </ul>
    `
  },
  pagePerformance: {
    title: "页面性能",
    content: `
      <p><strong>页面性能操作说明：</strong></p>
      <ul>
        <li>分析页面加载各阶段耗时</li>
        <li>监控首屏时间、DOM加载等核心指标</li>
        <li>支持按页面URL、设备类型筛选</li>
        <li>可查看性能瀑布图和资源加载时序</li>
        <li>支持页面性能评分和优化建议</li>
      </ul>
    `
  },
  ajaxPerformance: {
    title: "接口性能",
    content: `
      <p><strong>接口性能操作说明：</strong></p>
      <ul>
        <li>监控H5页面调用的API性能</li>
        <li>分析接口成功率、响应时间分布</li>
        <li>支持按接口路径、状态码筛选</li>
        <li>可查看接口调用链和参数详情</li>
        <li>支持接口Mock和重放测试</li>
      </ul>
    `
  },
  errorAnalysis: {
    title: "错误分析",
    content: `
      <p><strong>错误分析操作说明：</strong></p>
      <ul>
        <li>追踪JavaScript运行时错误</li>
        <li>分析资源加载失败和API异常</li>
        <li>支持按错误类型、页面URL筛选</li>
        <li>可查看错误堆栈和上下文信息</li>
        <li>支持错误自动归并和标记解决</li>
      </ul>
    `
  },
  userAnalysis: {
    title: "用户分析",
    content: `
      <p><strong>用户分析操作说明：</strong></p>
      <ul>
        <li>分析用户访问路径和行为序列</li>
        <li>统计用户地域、设备等分布特征</li>
        <li>支持按用户ID、会话ID筛选</li>
        <li>可查看用户细查和操作回放</li>
        <li>支持自定义用户分群分析</li>
      </ul>
    `
  },
  charactersAnalysis: {
    title: "特征分析",
    content: `
      <p><strong>特征分析操作说明：</strong></p>
      <ul>
        <li>分析用户设备和环境特征</li>
        <li>包括浏览器类型、操作系统等</li>
        <li>支持自定义特征标签</li>
        <li>可对比不同特征群体的性能差异</li>
        <li>支持特征组合分析</li>
      </ul>
    `
  },
  boce: {
    title: "拨测",
    content: `
      <p><strong>拨测操作说明：</strong></p>
      <ul>
        <li>主动模拟用户访问监测</li>
        <li>支持多地域、多网络环境拨测</li>
        <li>可配置定时拨测任务</li>
        <li>查看拨测历史记录和趋势</li>
        <li>支持拨测结果对比分析</li>
      </ul>
    `
  },
  ajaxPerformanceDetail: {
    title: "接口性能详情",
    content: `
      <p><strong>接口性能详情操作说明：</strong></p>
      <ul>
        <li>查看单个接口的性能指标</li>
        <li>支持按时间、版本、用户等维度分析</li>
        <li>可对比不同请求的性能差异</li>
        <li>支持生成性能报告</li>
      </ul>
    `
  },
  "pagePerformance-detail": {
    title: "页面性能详情",
    content: `
      <p><strong>页面性能详情操作说明：</strong></p>
      <ul>
        <li>查看单个页面的性能指标</li>
        <li>支持按时间、版本、用户等维度分析</li>
        <li>可对比不同请求的性能差异</li>
        <li>支持生成性能报告</li>
      </ul>
    `
  },
  // ==================== APP监测模块 ====================
  appmonitor: {
    title: "APP监测",
    content: `
      <p><strong>APP监测模块说明：</strong></p>
      <ul>
        <li>全方位监控移动应用性能</li>
        <li>分析崩溃、ANR等稳定性问题</li>
        <li>追踪网络请求和资源使用</li>
        <li>支持多维度数据下钻分析</li>
      </ul>
    `
  },
  appOverview: {
    title: "APP概览",
    content: `
      <p><strong>APP概览操作说明：</strong></p>
      <ul>
        <li>查看APP整体运行状况</li>
        <li>监控DAU、崩溃率等核心指标</li>
        <li>支持按版本、渠道等多维度筛选</li>
        <li>可对比不同时段的性能变化</li>
        <li>支持关键指标阈值告警</li>
      </ul>
    `
  },
  collapseAnalysis: {
    title: "崩溃分析",
    content: `
      <p><strong>崩溃分析操作说明：</strong></p>
      <ul>
        <li>追踪Native和Java崩溃日志</li>
        <li>分析崩溃堆栈和发生场景</li>
        <li>支持按崩溃类型、版本筛选</li>
        <li>可查看崩溃趋势和影响用户</li>
        <li>支持崩溃自动归并和标记解决</li>
      </ul>
    `
  },
  anrAnalysis: {
    title: "ANR分析",
    content: `
      <p><strong>ANR分析操作说明：</strong></p>
      <ul>
        <li>监控应用无响应事件</li>
        <li>分析主线程阻塞原因</li>
        <li>支持按发生场景、版本筛选</li>
        <li>可查看ANR时的线程状态</li>
        <li>支持ANR趋势对比</li>
      </ul>
    `
  },
  errorAppAnalysis: {
    title: "错误分析",
    content: `
      <p><strong>错误分析操作说明：</strong></p>
      <ul>
        <li>追踪非崩溃的异常和错误</li>
        <li>包括捕获异常和日志错误</li>
        <li>支持按错误类型、页面筛选</li>
        <li>可查看错误堆栈和上下文</li>
        <li>支持错误自动归并</li>
      </ul>
    `
  },
  networkRequest: {
    title: "网络请求",
    content: `
      <p><strong>网络请求操作说明：</strong></p>
      <ul>
        <li>监控APP发起的网络请求</li>
        <li>分析API成功率、响应时间</li>
        <li>支持按接口路径、状态码筛选</li>
        <li>可查看请求参数和返回数据</li>
        <li>支持网络环境模拟测试</li>
      </ul>
    `
  },
  startPerformance: {
    title: "启动性能",
    content: `
      <p><strong>启动性能操作说明：</strong></p>
      <ul>
        <li>监控APP冷启动、热启动耗时</li>
        <li>分析启动阶段各任务耗时</li>
        <li>支持按版本、设备筛选</li>
        <li>可查看启动时间分布</li>
        <li>支持启动优化建议</li>
      </ul>
    `
  },
  "request-detail": {
    title: "网络请求详情",
    content: `
      <p><strong>网络请求详情操作说明：</strong></p>
      <ul>
        <li>查看单次网络请求的完整详情</li>
        <li>包括请求头、参数、响应数据</li>
        <li>分析请求时间线和各阶段耗时</li>
        <li>支持请求重放和对比</li>
        <li>可标记为典型用例</li>
      </ul>
    `
  },
  "performance-detail": {
    title: "启动性能详情",
    content: `
      <p><strong>启动性能详情操作说明：</strong></p>
      <ul>
        <li>查看单次启动的详细时间线</li>
        <li>分析各初始化任务的耗时</li>
        <li>支持与基准版本对比</li>
        <li>可查看设备环境和系统负载</li>
        <li>支持生成优化建议报告</li>
      </ul>
    `
  },
  "anr-detail": {
    title: "ANR详情",
    content: `
      <p><strong>ANR详情操作说明：</strong></p>
      <ul>
        <li>查看单次ANR事件的完整详情</li>
        <li>分析主线程堆栈和阻塞原因</li>
        <li>支持查看系统日志和内存状态</li>
        <li>可对比相似ANR事件</li>
        <li>支持标记为已处理</li>
      </ul>
    `
  },
  "collapse-detail": {
    title: "崩溃详情",
    content: `
      <p><strong>崩溃详情操作说明：</strong></p>
      <ul>
        <li>查看单次崩溃的完整堆栈</li>
        <li>分析崩溃时的线程状态</li>
        <li>支持符号化Native崩溃</li>
        <li>可查看用户操作路径</li>
        <li>支持关联相似崩溃</li>
      </ul>
    `
  },
  "error-detail": {
    title: "错误详情",
    content: `
      <p><strong>错误详情操作说明：</strong></p>
      <ul>
        <li>查看单次错误的完整详情</li>
        <li>分析错误堆栈和上下文</li>
        <li>支持查看用户操作序列</li>
        <li>可关联相关网络请求</li>
        <li>支持标记为已解决</li>
      </ul>
    `
  },

  // ==================== 小程序监测模块 ====================
  miniprogram: {
    title: "小程序监测",
    content: `
      <p><strong>小程序监测模块说明：</strong></p>
      <ul>
        <li>全方位监控小程序性能</li>
        <li>分析页面加载和接口性能</li>
        <li>追踪运行时错误和异常</li>
        <li>支持多维度数据下钻分析</li>
      </ul>
    `
  },
  miniprogramOerview: {
    title: "小程序概览",
    content: `
      <p><strong>小程序概览操作说明：</strong></p>
      <ul>
        <li>查看小程序整体运行状况</li>
        <li>监控PV、UV、错误率等核心指标</li>
        <li>支持按版本、渠道筛选</li>
        <li>可对比不同时段的性能变化</li>
        <li>支持关键指标阈值告警</li>
      </ul>
    `
  },
  miniPagePerformance: {
    title: "页面性能",
    content: `
      <p><strong>页面性能操作说明：</strong></p>
      <ul>
        <li>监控小程序页面加载性能</li>
        <li>分析首屏时间、渲染耗时等指标</li>
        <li>支持按页面路径、场景值筛选</li>
        <li>可查看性能瀑布图</li>
        <li>支持页面性能优化建议</li>
      </ul>
    `
  },
  miniAjaxPerformanc: {
    title: "接口性能",
    content: `
      <p><strong>接口性能操作说明：</strong></p>
      <ul>
        <li>监控小程序发起的API请求</li>
        <li>分析接口成功率、响应时间</li>
        <li>支持按接口路径、状态码筛选</li>
        <li>可查看接口调用链</li>
        <li>支持接口Mock测试</li>
      </ul>
    `
  },
  miniErrorAnalysics: {
    title: "错误分析",
    content: `
      <p><strong>错误分析操作说明：</strong></p>
      <ul>
        <li>追踪小程序运行时错误</li>
        <li>包括JavaScript异常和API失败</li>
        <li>支持按错误类型、页面筛选</li>
        <li>可查看错误堆栈和上下文</li>
        <li>支持错误自动归并</li>
      </ul>
    `
  },
  miniUserAnalysics: {
    title: "用户分析",
    content: `
      <p><strong>用户分析操作说明：</strong></p>
      <ul>
        <li>分析用户访问路径和行为</li>
        <li>统计用户地域、设备等特征</li>
        <li>支持按用户ID、场景值筛选</li>
        <li>可查看用户细查数据</li>
        <li>支持自定义用户分群</li>
      </ul>
    `
  },
  miniFeatureAnalysics: {
    title: "特征分析",
    content: `
      <p><strong>特征分析操作说明：</strong></p>
      <ul>
        <li>分析用户设备和环境特征</li>
        <li>包括微信版本、基础库版本等</li>
        <li>支持自定义特征标签</li>
        <li>可对比不同特征群体的性能</li>
        <li>支持特征组合分析</li>
      </ul>
    `
  },

  // ==================== 流量监测模块 ====================
  flowMonitoring: {
    title: "流量监测",
    content: `
      <p><strong>流量监测模块说明：</strong></p>
      <ul>
        <li>监控网络流量和会话数据</li>
        <li>分析IP、TCP/UDP等网络行为</li>
        <li>支持流量分流和策略配置</li>
        <li>提供网络安全分析能力</li>
      </ul>
    `
  },
  flowMonitoringOverview: {
    title: "流量概览",
    content: `
      <p><strong>流量概览操作说明：</strong></p>
      <ul>
        <li>查看网络流量整体状况</li>
        <li>监控入站/出站流量趋势</li>
        <li>支持按协议、应用筛选</li>
        <li>可对比不同时段的流量变化</li>
        <li>支持异常流量告警</li>
      </ul>
    `
  },
  IpList: {
    title: "IP会话",
    content: `
      <p><strong>IP会话操作说明：</strong></p>
      <ul>
        <li>监控IP级别的网络会话</li>
        <li>分析源IP和目标IP的通信</li>
        <li>支持按IP段、地域筛选</li>
        <li>可查看会话详细流量数据</li>
        <li>支持IP黑白名单管理</li>
      </ul>
    `
  },
  TCP: {
    title: "TCP会话",
    content: `
      <p><strong>TCP会话操作说明：</strong></p>
      <ul>
        <li>监控TCP连接建立和传输</li>
        <li>分析握手耗时、重传率等指标</li>
        <li>支持按端口、服务筛选</li>
        <li>可查看连接时间线和状态</li>
        <li>支持异常连接告警</li>
      </ul>
    `
  },
  UDP: {
    title: "UDP会话",
    content: `
      <p><strong>UDP会话操作说明：</strong></p>
      <ul>
        <li>监控UDP数据包传输</li>
        <li>分析丢包率、延迟等指标</li>
        <li>支持按端口、应用筛选</li>
        <li>可查看流量时序图</li>
        <li>支持异常流量检测</li>
      </ul>
    `
  },
  flowRules: {
    title: "流量分流",
    content: `
      <p><strong>流量分流操作说明：</strong></p>
      <ul>
        <li>配置流量转发和负载均衡规则</li>
        <li>支持按IP、协议、内容等条件分流</li>
        <li>可设置流量镜像和复制</li>
        <li>支持规则优先级调整</li>
        <li>可查看规则命中统计</li>
      </ul>
    `
  },

  // ==================== 日志监测模块 ====================
  log: {
    title: "日志监测",
    content: `
      <p><strong>日志监测模块说明：</strong></p>
      <ul>
        <li>集中式日志采集和分析</li>
        <li>支持多源日志统一管理</li>
        <li>提供实时搜索和统计分析</li>
        <li>支持日志告警和自动化处理</li>
      </ul>
    `
  },
  logOverview: {
    title: "日志概览",
    content: `
      <p><strong>日志概览操作说明：</strong></p>
      <ul>
        <li>查看日志整体统计信息</li>
        <li>监控错误日志趋势和分布</li>
        <li>支持按应用、级别筛选</li>
        <li>可快速跳转到相关日志</li>
        <li>支持日志模式分析</li>
      </ul>
    `
  },
  logOriginal: {
    title: "日志看台",
    content: `
      <p><strong>日志看台操作说明：</strong></p>
      <ul>
        <li>实时查看原始日志内容</li>
        <li>支持全文搜索和高级查询</li>
        <li>可设置日志高亮和过滤</li>
        <li>支持上下文日志查看</li>
        <li>可导出日志为文件</li>
      </ul>
    `
  },
  logHandle: {
    title: "错误处理",
    content: `
      <p><strong>错误处理操作说明：</strong></p>
      <ul>
        <li>集中处理错误和异常日志</li>
        <li>支持日志分类和打标签</li>
        <li>可关联相关工单系统</li>
        <li>支持自动修复建议</li>
        <li>可查看处理历史记录</li>
      </ul>
    `
  },
  records: {
    title: "处理记录",
    content: `
      <p><strong>处理记录操作说明：</strong></p>
      <ul>
        <li>查看日志处理的历史记录</li>
        <li>支持按处理人、状态筛选</li>
        <li>可查看处理详情和备注</li>
        <li>支持导出处理报告</li>
        <li>可重新打开已关闭记录</li>
      </ul>
    `
  },

  // ==================== 系统管理模块 ====================
  system: {
    title: "系统管理",
    content: `
      <p><strong>系统管理模块说明：</strong></p>
      <ul>
        <li>管理系统基础配置和权限</li>
        <li>支持多租户资源隔离</li>
        <li>提供用户角色权限控制</li>
        <li>可配置监控告警策略</li>
      </ul>
    `
  },
  tenant: {
    title: "租户管理",
    content: `
      <p><strong>租户管理操作说明：</strong></p>
      <ul>
        <li>管理多租户信息和配置</li>
        <li>支持租户资源配额设置</li>
        <li>可查看租户使用统计</li>
        <li>支持租户状态控制</li>
        <li>可配置租户级权限</li>
      </ul>
    `
  },
  appList: {
    title: "应用管理",
    content: `
      <p><strong>应用管理操作说明：</strong></p>
      <ul>
        <li>管理监控的应用列表</li>
        <li>支持应用分组和标签</li>
        <li>可配置应用采集策略</li>
        <li>支持应用权限分配</li>
        <li>可查看应用监控状态</li>
      </ul>
    `
  },
  organList: {
    title: "单位管理",
    content: `
      <p><strong>单位管理操作说明：</strong></p>
      <ul>
        <li>管理组织机构树</li>
        <li>支持部门创建和调整</li>
        <li>可配置部门负责人</li>
        <li>支持部门权限继承</li>
        <li>可查看部门关联资源</li>
      </ul>
    `
  },
  user: {
    title: "用户管理",
    content: `
      <p><strong>用户管理操作说明：</strong></p>
      <ul>
        <li>管理系统用户账号</li>
        <li>支持用户增删改查</li>
        <li>可配置用户所属部门</li>
        <li>支持用户状态控制</li>
        <li>可查看用户操作日志</li>
      </ul>
    `
  },
  role: {
    title: "角色管理",
    content: `
      <p><strong>角色管理操作说明：</strong></p>
      <ul>
        <li>定义系统角色和权限</li>
        <li>支持角色权限精细控制</li>
        <li>可配置数据访问范围</li>
        <li>支持角色继承关系</li>
        <li>可查看角色分配情况</li>
      </ul>
    `
  },
  resources: {
    title: "菜单管理",
    content: `
      <p><strong>菜单管理操作说明：</strong></p>
      <ul>
        <li>管理系统菜单和路由</li>
        <li>支持多级菜单配置</li>
        <li>可设置菜单权限可见性</li>
        <li>支持菜单排序和分组</li>
        <li>可查看菜单使用统计</li>
      </ul>
    `
  },
  flow: {
    title: "流量设置",
    content: `
      <p><strong>流量设置操作说明：</strong></p>
      <ul>
        <li>配置流量采集策略</li>
        <li>支持采样率调整</li>
        <li>可设置数据保留时间</li>
        <li>支持流量数据导出</li>
        <li>可查看存储空间使用</li>
      </ul>
    `
  },
  editTopology: {
    title: "拓扑设置",
    content: `
      <p><strong>拓扑设置操作说明：</strong></p>
      <ul>
        <li>配置服务拓扑发现规则</li>
        <li>支持手动调整拓扑关系</li>
        <li>可设置重点关注服务</li>
        <li>支持拓扑图样式定制</li>
        <li>可导出拓扑配置</li>
      </ul>
    `
  },
  warnmanage: {
    title: "告警设置",
    content: `
      <p><strong>告警设置操作说明：</strong></p>
      <ul>
        <li>配置监控告警规则</li>
        <li>支持多条件组合告警</li>
        <li>可设置告警级别和接收人</li>
        <li>支持告警抑制和聚合</li>
        <li>可查看告警历史记录</li>
      </ul>
    `
  },
  boceSetting: {
    title: "拨测设置",
    content: `
      <p><strong>拨测设置操作说明：</strong></p>
      <ul>
        <li>配置主动拨测任务</li>
        <li>支持多地域拨测点管理</li>
        <li>可设置拨测频率和超时</li>
        <li>支持拨测告警阈值</li>
        <li>可查看拨测历史数据</li>
      </ul>
    `
  },
  logDevice: {
    title: "设备信息",
    content: `
      <p><strong>设备信息操作说明：</strong></p>
      <ul>
        <li>管理日志采集设备</li>
        <li>查看设备状态和配置</li>
        <li>支持设备分组管理</li>
        <li>可配置设备采集规则</li>
        <li>支持设备远程控制</li>
      </ul>
    `
  },
  agent: {
    title: "探针说明",
    content: `
      <p><strong>探针说明操作说明：</strong></p>
      <ul>
        <li>查看探针版本和状态</li>
        <li>支持探针远程升级</li>
        <li>可配置探针采集参数</li>
        <li>支持探针日志查看</li>
        <li>可下载探针安装包</li>
      </ul>
    `
  },

  // ==================== 默认指南 ====================
  default: {
    title: "通用指南",
    content: `
      <p><strong>通用操作指南：</strong></p>
      <ul>
        <li>页面右下角为说明按钮</li>
        <li>点击按钮查看当前页操作指引</li>
        <li>支持按时间范围筛选数据</li>
      </ul>
    `
  }
};
