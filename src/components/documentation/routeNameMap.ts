// src/router/routeNameMap.ts

const routeNameMap = {
  // 服务监测
  overview: "全局概览",
  topology: "链路图",
  serviceList: "服务列表",
  CallChain: "调用链分析",
  querylog: "服务日志",
  hostList: "主机列表",

  // H5监测
  H5overview: "概览",
  pagePerformance: "页面性能",
  ajaxPerformance: "接口性能",
  errorAnalysis: "错误分析",
  userAnalysis: "用户分析",
  charactersAnalysis: "特征分析",
  boce: "拨测",

  // APP监测
  appOverview: "概览",
  collapseAnalysis: "崩溃分析",
  anrAnalysis: "ANR分析",
  errorAppAnalysis: "错误分析",
  networkRequest: "网络请求",
  startPerformance: "启动性能",

  // 小程序监测
  miniprogramOerview: "概览",
  miniPagePerformance: "页面性能",
  miniAjaxPerformanc: "接口性能",
  miniErrorAnalysics: "错误分析",
  miniUserAnalysics: "启动性能",
  miniFeatureAnalysics: "特征分析",

  // 流量监测
  flowMonitoringOverview: "流量概览",
  IpList: "IP会话",
  TCP: "TCP会话",
  UDP: "UDP会话",
  flowRules: "流量分流",

  // 日志监测
  logOverview: "日志概览",
  logOriginal: "日志看台",
  logHandle: "错误处理",
  records: "处理记录",

  // 系统管理
  tenant: "租户管理",
  appList: "应用管理",
  organList: "单位管理",
  user: "用户管理",
  role: "角色管理",
  resources: "菜单管理",
  flow: "流量设置",
  editTopology: "拓扑设置",
  warnmanage: "告警设置",
  boceSetting: "拨测设置",
  logDevice: "设备信息",
  agent: "探针说明"
};

export default routeNameMap;
