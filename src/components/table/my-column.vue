<template>
  <el-table-column
    v-if="slots.default"
    v-bind="$attrs"
    align="left"
    header-align="left"
    :show-overflow-tooltip="true"
  >
    <template #default="scope"> <slot name="default" :row="scope.row"></slot> </template>
  </el-table-column>
  <el-table-column
    v-else
    v-bind="$attrs"
    align="left"
    header-align="left"
    :show-overflow-tooltip="true"
  ></el-table-column>
</template>
<script setup lang="ts">
defineProps<{
  [key: string]: any;
}>();
const slots = useSlots();
</script>
