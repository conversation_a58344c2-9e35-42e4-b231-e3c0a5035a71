<template>
  <div>
    <el-table
      ref="tableRef"
      :row-key="rowKey"
      v-bind="$attrs"
      :header-cell-style="{ background: '#f9f9f9', color: '#333', fontWeight: 'normal' }"
      :data="data"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="showSelect" type="selection" width="55"></el-table-column>
      <slot></slot>
    </el-table>
    <div class="pagination" v-if="showElPagination">
      <el-pagination
        :background="paginationBackground"
        v-model:current-page="state.currentPage"
        v-model:page-size="state.pageSize"
        :page-sizes="[10, 20, 30]"
        :layout="paginationLayout"
        :total="total"
        @current-change="(val: number) => emits('currentChange', val)"
        @size-change="(val: number) => emits('sizeChange', val)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, withDefaults } from "vue";

const state = reactive({
  currentPage: 1,
  pageSize: 10
});

const emits = defineEmits(["currentChange", "sizeChange", "change"]);
const props = withDefaults(
  defineProps<{
    total: number;
    data: any[];
    showSelect?: boolean;
    [key: string]: any;
    paginationLayout?: string;
    paginationBackground?: boolean;
    showElPagination?: boolean;
    rowKey?: string;
  }>(),
  {
    rowKey: undefined,
    showSelect: false,
    paginationLayout: "total, sizes, prev, pager, next, jumper",
    paginationBackground: false,
    showElPagination: true
  }
);
// 选中的行
const selectedRows = reactive([]);

const handleSelectionChange = (selection: any[]) => {
  selectedRows.splice(0, selectedRows.length, ...selection);
  emits("change", selectedRows);
};

const tableRef = ref();

const clearSelection = () => {
  tableRef.value?.clearSelection?.();
};

const toggleRowSelection = (row: any, selected: boolean) => {
  tableRef.value?.toggleRowSelection?.(row, selected);
};
defineExpose({
  clearSelection,
  toggleRowSelection
});
</script>

<style>
.el-table {
  border-top: 1px solid #eee;
  border-right: 1px solid #eee;
  border-left: 1px solid #eee;
}
.el-table .el-table__cell {
  padding: 12px 0px;
}
.pagination {
  margin-top: 20px;
  margin-right: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
