<template>
  <div class="ranking-wrapper">
    <TitlecCom :title="title"></TitlecCom>
    <div v-if="rankingList.length" class="ranking-wrapper-progress mt-10px">
      <template v-for="(item, index) in rankingList" :key="item.name">
        <div class="flex cursor-pointer" @click="handleItemClick(item)">
          <div v-if="showNumber" class="ranking-wrapper-num mr-14px mt-6px">
            {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
          </div>
          <div :title="item.name" class="w-100%">
            <div class="flex justify-between mb-2">
              <span :title="item.name" class="ranking-wrapper-serial name">{{ item.name }}</span>
              <div :style="{ color: item.color }" class="ranking-wrapper-serial total-score">
                {{ formatNums(item.totalScore).fixValue }}
                <span class="ml--2px">{{ formatNums(item.totalScore).unit }}</span>
                <span class="total-score-unit ml-3px">{{ item.unit || "" }}</span>
              </div>
            </div>
            <el-progress :percentage="item.proportion" :color="item.color" :show-text="false" />
          </div>
        </div>
      </template>
    </div>
    <div v-else>
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import TitlecCom from "@/components/TitleCom/index.vue";
import { formatNums } from "@/utils/formatStr";
interface IRankingItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总分数
  unit?: string; // 单位
  color?: string; // 进度条颜色
}
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ""
  },
  // 排行列表,最多为5项，外层数据控制处理
  rankingList: {
    type: Array as PropType<IRankingItem[]>,
    default: () => [] as IRankingItem[],
    required: true
  },
  showNumber: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits(["itemClick"]);
function handleItemClick(item: IRankingItem) {
  emit("itemClick", item);
}
</script>
<style lang="scss" scoped>
.ranking-wrapper {
  padding: 20px;
  border: 1px solid rgba(225, 228, 233, 1);
  // max-width: 500px;
  min-height: 310px;
  border-radius: 2px;
  .name {
    // flex: 1;
  }
  &-serial {
    display: inline-block;
    font-size: 13px;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    // max-width: calc(100% - 70px);
    flex-shrink: 0;
    max-width: 348px;
  }

  &-num {
    color: #999;
    font-size: 14px;
  }
  .total-score {
    color: #445fde;
    font-size: 13px;
    &-unit {
      color: #333;
      font-size: 13px;
    }
  }
  .ranking-wrapper-progress .el-progress--line {
    margin-bottom: 15px;
  }
}
:deep(.el-progress-bar__outer) {
  border-radius: 0px;
  height: 9px !important;
}
:deep(.el-progress-bar__inner) {
  border-radius: 0px;
  // background: #445fde !important;
}
@media screen and (min-width: 1800px) {
  .name {
    max-width: 450px; // 当屏幕宽度大于 1480px 时，使用新的 max-width
  }
}

@media screen and (min-width: 1020px) and (max-width: 1400px) {
  .name {
    max-width: 200px; // 当屏幕宽度大于 1480px 时，使用新的 max-width
  }
}

@media screen and (max-width: 1020px) {
  .name {
    max-width: 150px; // 当屏幕宽度大于 1480px 时，使用新的 max-width
  }
}

// @media screen and (min-width: 600px) {
//   .name {
//     max-width: 150px; // 当屏幕宽度大于 1480px 时，使用新的 max-width
//   }
// }
</style>
