import { formatNums } from "@/utils/formatStr";
import { ChartOptions, ChartParams } from "./chartTypes";
import { getTimeValue, revertArray } from "./chartUtils";

/**
 * 获取图表配置选项
 *
 * @param params - 图表配置参数
 * @param params.typ - 时间粒度类型（0: 秒，1: 分钟，2: 小时，3: 天，4: 月）（默认为 undefined）
 * @param params.color - 数据系列颜色数组
 * @param params.titleType - 图表标题
 * @param params.originalTimes - 原始时间数据
 * @param params.seriesData - 数据系列数组
 * @param params.name - 数据单位（默认为 " 次"）
 * @param params.names - 数据系列名称数组
 * @param params.legend - 图例配置
 * @param params.type - 图表类型（默认为 "bar" 或 "line"）
 * @param params.numberType - 是否格式化数字（默认为 true）
 * @param params.areaStyle - 是否启用面积图样式（默认为 false）
 * @param params.legend.show - 是否显示图例（默认为 false）
 * @param params.legend.position - 图例位置
 * @param params.stack - 是否堆叠（默认为 false）
 * @param params.enableDataZoom - 是否显示时间选择条（默认为 false）
 * @param params.setGrid - 自定义网格配置
 * @param params.titleShow - 显示标题（默认为 true）
 * @param params.onClick - 点击事件处理函数
 *
 * @returns 返回 ECharts 图表配置对象
 */
export function getChartOptions(params: ChartParams): ChartOptions {
  const {
    typ = undefined,
    color,
    titleType,
    originalTimes,
    seriesData,
    name = " 次",
    names = [],
    type = "bar",
    numberType = true,
    areaStyle = false,
    legend = { show: false },
    stack = false,
    enableDataZoom = false,
    setGrid = {},
    titleShow = true,
    onClick,
    tooltip
  } = params;
  let timeValue;
  let revertedTimes;
  let interval = 0;

  if (typ >= 0) {
    timeValue = getTimeValue(typ);
    revertedTimes = revertArray(originalTimes);
    const dataLength = revertedTimes.length;
    if (dataLength > 10) interval = 1;
    if (dataLength > 20) interval = 2;
    if (dataLength > 30) interval = 3;
    if (dataLength > 60) interval = 6;
  }

  const defaultGrid = {
    left: "3%",
    right: "4%",
    bottom: "3%",
    top: "60px",
    containLabel: true
  };
  const defaultTitle = {
    text: `${titleType}${typ >= 0 ? `（${timeValue}）` : ""}`,
    x: "10px",
    y: "1px",
    textStyle: {
      fontSize: 16,
      fontWeight: "400",
      color: "#000"
    },
    subTextStyle: {
      fontSize: 16,
      fontWeight: "normal"
    }
  };
  return {
    title: titleShow ? defaultTitle : {},
    tooltip: tooltip || {
      trigger: "axis",
      axisPointer: {
        lineStyle: {
          width: 1,
          color: "#008000"
        }
      },
      formatter: function (params: any) {
        const originalTime = params[0].name;
        let tooltipContent = typ
          ? `<div style="margin-bottom: 5px;">${originalTime}</div>`
          : `<div style="margin-bottom: 5px;font-weight:600">${titleType}</div>`;
        params.forEach((item: any) => {
          const { value, seriesName } = item;
          const seriesColor = item.color || color;
          const displayValue = numberType ? formatNums(value).fixValue : value;
          const unit = numberType ? formatNums(value).unit : "";
          tooltipContent += `  
        <div style="display: flex; align-items: center;">  
          <span style="display: inline-block; width: 8px; height: 8px; background-color: ${seriesColor}; border-radius: 50%; margin-right: 5px;"></span>  
          <span>${typ ? seriesName : originalTime}：${displayValue} ${unit}${name}</span>  
        </div>  
      `;
        });

        return tooltipContent;
      }
    },
    graphic: titleShow
      ? [
          {
            type: "rect",
            shape: { x: 0, y: 5, width: 4, height: 15 },
            style: { fill: "#3375f9" },
            z: 100
          }
        ]
      : [],
    grid: { ...defaultGrid, ...setGrid },
    legend: legend.show
      ? {
          show: true,
          orient: "horizontal",
          align: "left",
          top: "25px",
          type: "scroll"
        }
      : undefined,
    xAxis: {
      type: "category",
      // boundaryGap: false,
      data: revertedTimes || originalTimes,
      axisLabel: typ
        ? {
            formatter: (value: string) => value.split(" ").join("\n"),
            align: "center",
            // interval: interval,
            minInterval: 10
          }
        : {}
    },
    yAxis: {
      type: "value"
    },
    series: seriesData.map((data, index) => ({
      name: names[index] || titleType,
      type: type,
      smooth: type === "line",
      barMaxWidth: 35,
      stack: stack ? "total" : undefined,
      areaStyle: areaStyle && type === "line" ? {} : undefined,
      color: color[index],
      data: data
    })),
    ...(enableDataZoom && {
      dataZoom: [
        {
          type: "inside",
          start: 0,
          end: 100
        },
        {
          type: "slider",
          left: "3%",
          right: "1%",
          start: 0,
          end: 100
        }
      ]
    }),
    ...(onClick && { onClick })
  };
}

/**
 * 获取饼图配置选项
 *
 * @param params - 图表配置参数
 * @param params.color - 数据系列颜色数组
 * @param params.titleType - 图表标题
 * @param params.seriesData - 数据系列数组
 * @param params.names - 数据系列名称数组
 * @param params.numberType - 是否格式化数字（默认为 true）
 *
 * @returns 返回 ECharts 饼图配置对象
 */
export function getPieChartOptions(params: ChartParams): ChartOptions {
  const { typ = undefined, color, titleType, seriesData, names = [], numberType = true } = params;
  let timeValue;
  if (typ) {
    timeValue = getTimeValue(typ);
  }
  console.log("seriesData  ===>>>", seriesData);

  return {
    title: {
      text: `${titleType}${typ ? `（${timeValue}）` : ""}`,
      x: "10px",
      y: "1px",
      textStyle: {
        fontSize: 16,
        fontWeight: "400"
      },
      subTextStyle: {
        fontSize: 16,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: function (params: any) {
        const { name, value } = params;
        const displayValue = numberType ? formatNums(value).fixValue : value;
        const unit = numberType ? formatNums(value).unit : "";
        return `${name}: ${displayValue} ${unit}`;
      }
    },
    graphic: [
      {
        type: "rect",
        shape: { x: 0, y: 5, width: 4, height: 15 },
        style: { fill: "#3375f9" },
        z: 100
      }
    ],
    legend: {
      show: true,
      orient: "horizontal",
      type: "scroll",
      top: "30"
    },
    series: [
      {
        name: titleType,
        type: "pie",
        radius: "50%",
        data: seriesData.map((data, index) => ({
          name: index,
          value: data
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ]
  };
}
