<template>
  <div
    class="ranking-wrapper w-100%"
    @mouseenter="showButton = true"
    @mouseleave="showButton = false"
  >
    <div class="btn_right" v-if="showButton">
      <el-button @click="openDialog" size="small">
        <img src="@/image/big.svg" alt="Full Screen" class="svg-icon" />
      </el-button>
    </div>
    <div
      :style="{
        width: width,
        height: height
      }"
      ref="echartsRef"
    />
    <el-dialog v-model="dialogVisible" width="95vw" top="5vh" :fullscreen="false">
      <div style="width: 94vw; height: 75vh" ref="dialogEchartsRef"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" style="width: 100px" @click="dialogVisible = false"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, PropType } from "vue";
import { useEcharts, EChartsCoreOption } from "./hooks/useEcharts";

const props = defineProps({
  options: { type: Object as PropType<EChartsCoreOption>, required: true },
  height: { type: String, default: "100%" },
  width: { type: String, default: "98%" },
  themeColors: { type: Array as PropType<string[]>, default: () => [] }
});

const echartsRef = ref();
const dialogEchartsRef = ref();
const dialogVisible = ref(false);

const { setOptions: setMainOptions, initCharts: initMainCharts } = useEcharts(
  echartsRef,
  props.options
);
const { setOptions: setDialogOptions, initCharts: initDialogCharts } = useEcharts(
  dialogEchartsRef,
  props.options
);
const showButton = ref(false);

watch(
  () => props.options,
  newOptions => {
    let targetOptions = { ...newOptions };
    if (props.themeColors && props.themeColors.length > 0) {
      targetOptions.color = props.themeColors;
    }
    setMainOptions(targetOptions);
    if (dialogVisible.value) {
      setDialogOptions(targetOptions);
    }
  }
);

onMounted(() => {
  initMainCharts();
});

const openDialog = () => {
  dialogVisible.value = true;

  setTimeout(() => {
    initDialogCharts();
    setDialogOptions(props.options);
  }, 0);
};
</script>

<style lang="scss" scoped>
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #e1e4e9;
  border-radius: 2px;
  position: relative;
  .btn_right {
    position: absolute;
    right: 30px;
    z-index: 9;
    .svg-icon {
      width: 18px;
      height: 18px;
      vertical-align: middle;
    }
  }
}
</style>
