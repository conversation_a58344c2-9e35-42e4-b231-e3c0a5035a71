import { Ref, shallowRef, unref, onMounted, onDeactivated, onBeforeUnmount } from "vue";
import echarts from "@/components/baseEcharts/config";

export type EChartsCoreOption = echarts.EChartsCoreOption;

const useEcharts = (elRef: Ref<HTMLDivElement>, options: EChartsCoreOption) => {
  const charts = shallowRef<echarts.ECharts>();

  const setOptions = (options: EChartsCoreOption) => {
    if (!charts.value) return;

    // 如果有点击事件，先移除旧的事件监听
    if (options.onClick) {
      charts.value.off("click");
      charts.value.on("click", options.onClick as any);
      // 从options中移除onClick，因为这不是ECharts的标准配置项
      const { onClick, ...restOptions } = options;
      charts.value.setOption(restOptions);
    } else {
      charts.value.setOption(options);
    }
  };

  // 初始化
  const initCharts = (themeColor?: Array<string>) => {
    const el = unref(elRef);
    if (!el || !unref(el)) {
      return;
    }
    charts.value = echarts.init(el);
    if (themeColor) {
      options.color = themeColor;
    }
    setOptions(options);
  };

  // 重新窗口变化时，重新计算
  const resize = () => {
    charts.value && charts.value.resize();
  };

  onMounted(() => {
    window.addEventListener("resize", resize);
  });

  // 页面keepAlive时，不监听页面
  onDeactivated(() => {
    window.removeEventListener("resize", resize);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", resize);
  });

  return {
    initCharts,
    setOptions,
    resize
  };
};

export { useEcharts };
