<template>
  <div class="common-title">
    <div class="common-title-mark"></div>
    <div>{{ title }}</div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String, // 文案
    required: true, // 必填
    default: ""
  }
});
</script>
<style lang="scss" scoped>
.common-title {
  font-size: 16px;
  font-weight: 500;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  &-mark {
    margin-right: 10px;
    width: 4px;
    height: 15px;
    background: rgba(50, 117, 249, 1);
  }
}
</style>
