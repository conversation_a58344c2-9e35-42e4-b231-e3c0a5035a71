export default [
  {
    name: "serviceOverview", // 服务概览
    path: "/dashboard/overview/service-overview",
    component: () => import("@/views/serverMonitor/serviceIntroduce/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "服务概览",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "service-topology", // 服务topo
    path: "/dashboard/overview/service-topology",
    component: () => import("@/views/serverMonitor/serviceTopo/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "服务链路",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "instance-monitor", // 实例监测
    path: "/dashboard/overview/instance-monitor",
    component: () => import("@/views/serverMonitor/instanceMonitor/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "实例监测",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "service-endponit", // Endpoint监测
    path: "/dashboard/overview/service-endponit",
    component: () => import("@/views/serverMonitor/provideService/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "Endpoint监测",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "service-dependent", // 依赖服务
    path: "/dashboard/overview/service-dependent",
    component: () => import("@/views/serverMonitor/serviceDependent/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "依赖服务",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "callChainNoServie", // 调用链分析
    path: "/dashboard/overview/Call-chain",
    component: () => import("@/views/serverMonitor/callChain/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "调用链分析",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "sql-analysis", // SQL分析
    path: "/dashboard/overview/sql-analysis",
    component: () => import("@/views/serverMonitor/sqlAnalysis/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "SQL分析",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "nosql-analysis", // NoSQL分析
    path: "/dashboard/overview/nosql-analysis",
    component: () => import("@/views/serverMonitor/nosqlAnalysis/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "NoSQL分析",
      hidden: true,
      detailRoute: true
    }
  },
  {
    name: "mq-analysis", // MQ分析
    path: "/dashboard/overview/mq-analysis",
    component: () => import("@/views/serverMonitor/mqAnalysis/index.vue"),
    meta: {
      icon: "service",
      iconType: "el",
      title: "MQ分析",
      hidden: true,
      detailRoute: true
    }
  }
];
