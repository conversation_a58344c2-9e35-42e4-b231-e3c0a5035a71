export default [
  {
    name: "request-detail",
    path: "/appmonitor/network-request/detail",
    component: () => import("@/views/application/networkRequest/components/NetWorkDetail.vue"),
    meta: {
      icon: "app",
      iconType: "el",
      title: "网络请求详情",
      detailRoute: true,
      hiddenDetailTabs: true // 二级页面不显示tab
    }
  },
  {
    name: "performance-detail",
    path: "/appmonitor/start-performance/detail",
    component: () =>
      import("@/views/application/startPerformance/components/StartPerforDetail.vue"),
    meta: {
      icon: "app",
      iconType: "el",
      title: "详情",
      detailRoute: true,
      hiddenDetailTabs: true
    }
  },
  {
    name: "anr-detail",
    path: "/appmonitor/anr/detail",
    component: () => import("@/views/application/anrAnalysis/anrDetail.vue"),
    meta: {
      icon: "app",
      iconType: "el",
      title: "详情",
      detailRoute: true,
      hiddenDetailTabs: true
    }
  },
  {
    name: "collapse-detail",
    path: "/appmonitor/collapse/detail",
    component: () => import("@/views/application/collapseAnalysis/collapseDetail.vue"),
    meta: {
      icon: "app",
      iconType: "el",
      title: "详情",
      detailRoute: true,
      hiddenDetailTabs: true
    }
  },
  {
    name: "error-detail",
    path: "/appmonitor/error/detail",
    component: () => import("@/views/application/errorAnalysis/errorDetail.vue"),
    meta: {
      icon: "app",
      iconType: "el",
      title: "详情",
      detailRoute: true,
      hiddenDetailTabs: true
    }
  }
];
