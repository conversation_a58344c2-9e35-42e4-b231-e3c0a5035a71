import { defineAsyncComponent } from "vue";
import type { AsyncComponentLoader } from "vue";

export const asyncComponentMap = {
  // ==================== 服务监测模块 ====================
  overview: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  topology: defineAsyncComponent(() => import("@/views/serverMonitor/topology/index.vue")),
  serviceList: defineAsyncComponent(() => import("@/views/serverMonitor/serviceList/index.vue")),
  CallChain: defineAsyncComponent(() => import("@/views/serverMonitor/callChain/index.vue")),
  querylog: defineAsyncComponent(() => import("@/views/serverMonitor/querylog/index.vue")),
  hostList: defineAsyncComponent(() => import("@/views/serverMonitor/hostList/index.vue")),
  serviceOverview: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  serviceTopology: defineAsyncComponent(() => import("@/views/serverMonitor/topology/index.vue")),
  instanceMonitor: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  serviceEndponit: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  serviceDependent: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  // CallChain: defineAsyncComponent(() => import("@/views/serverMonitor/callChain/index.vue")),
  sqlAnalysis: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  nosqlAnalysis: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),
  mqAnalysis: defineAsyncComponent(() => import("@/views/serverMonitor/index.vue")),

  // ==================== H5监测模块 ====================
  H5overview: defineAsyncComponent(() => import("@/views/h5/index.vue")),
  pagePerformance: defineAsyncComponent(() => import("@/views/h5/pagePerformance/index.vue")),
  ajaxPerformance: defineAsyncComponent(() => import("@/views/h5/interfacePerformance/index.vue")),
  errorAnalysis: defineAsyncComponent(() => import("@/views/h5/errorAnalysis/index.vue")),
  userAnalysis: defineAsyncComponent(() => import("@/views/h5/userAnalysis/index.vue")),
  charactersAnalysis: defineAsyncComponent(() => import("@/views/h5/charactersAnalysis/index.vue")),
  boce: defineAsyncComponent(() => import("@/views/h5/boce/index.vue")),

  // ==================== APP监测模块 ====================
  appOverview: defineAsyncComponent(() => import("@/views/application/appOverview/index.vue")),
  collapseAnalysis: defineAsyncComponent(
    () => import("@/views/application/collapseAnalysis/index.vue")
  ),
  anrAnalysis: defineAsyncComponent(() => import("@/views/application/anrAnalysis/index.vue")),
  errorAppAnalysis: defineAsyncComponent(
    () => import("@/views/application/errorAnalysis/index.vue")
  ),
  networkRequest: defineAsyncComponent(
    () => import("@/views/application/networkRequest/index.vue")
  ),
  startPerformance: defineAsyncComponent(
    () => import("@/views/application/startPerformance/index.vue")
  ),
  requestDetail: defineAsyncComponent(() => import("@/views/application/networkRequest/index.vue")),
  performanceDetail: defineAsyncComponent(
    () => import("@/views/application/startPerformance/index.vue")
  ),
  anrDetail: defineAsyncComponent(() => import("@/views/application/anrAnalysis/index.vue")),
  collapseDetail: defineAsyncComponent(
    () => import("@/views/application/collapseAnalysis/index.vue")
  ),
  errorDetail: defineAsyncComponent(() => import("@/views/application/errorAnalysis/index.vue")),

  // ==================== 小程序监测模块 ====================
  miniprogramOerview: defineAsyncComponent(() => import("@/views/miniprogram/overview/index.vue")),
  miniPagePerformance: defineAsyncComponent(
    () => import("@/views/miniprogram/pagePerformance/index.vue")
  ),
  miniAjaxPerformanc: defineAsyncComponent(
    () => import("@/views/miniprogram/miniAjaxPer/index.vue")
  ),
  miniErrorAnalysics: defineAsyncComponent(
    () => import("@/views/miniprogram/errorAnalysics/index.vue")
  ),
  miniUserAnalysics: defineAsyncComponent(
    () => import("@/views/miniprogram/startPerformance/index.vue")
  ),
  miniFeatureAnalysics: defineAsyncComponent(
    () => import("@/views/miniprogram/featureAnalysics/index.vue")
  ),
  // ==================== 流量监测模块 ====================
  flowMonitoringOverview: defineAsyncComponent(
    () => import("@/views/flowMonitoring/overview/index.vue")
  ),
  IpList: defineAsyncComponent(() => import("@/views/flowMonitoring/ipList/index.vue")),
  TCP: defineAsyncComponent(() => import("@/views/flowMonitoring/TCP/index.vue")),
  UDP: defineAsyncComponent(() => import("@/views/flowMonitoring/UDP/index.vue")),
  flowRules: defineAsyncComponent(() => import("@/views/flowMonitoring/flowRules/index.vue")),

  // ==================== 日志监测模块 ====================
  logOverview: defineAsyncComponent(() => import("@/views/log/overview/index.vue")),
  logOriginal: defineAsyncComponent(() => import("@/views/log/original/index.vue")),
  LogAnalysis: defineAsyncComponent(() => import("@/views/log/analysis/index.vue")),
  // logHandle: defineAsyncComponent(() => import("@/views/warnManage/handle/handle.vue")),
  // records: defineAsyncComponent(() => import("@/views/warnManage/records/index.vue")),
  // ==================== 组件监测模块 ====================
  // moduleExternal: defineAsyncComponent(() => import("@/views/module/index.vue")),
  // moduleCommunication: defineAsyncComponent(() => import("@/views/module/communication/index.vue")),
  moduleDatabase: defineAsyncComponent(() => import("@/views/module/database/index.vue")),
  // moduleWebContainer: defineAsyncComponent(() => import("@/views/module/webContainer/index.vue")),
  moduleCache: defineAsyncComponent(() => import("@/views/module/cache/index.vue")),
  moduleQueue: defineAsyncComponent(() => import("@/views/module/queue/index.vue")),
  moduleSnmp: defineAsyncComponent(() => import("@/views/module/snmp/index.vue")),

  // ==================== AI助手模块 ====================
  assistantWorkflowList: defineAsyncComponent(
    () => import("@/views/warnManage/workflow/index.vue")
  ),
  assistantCreateWorkflow: defineAsyncComponent(
    () => import("@/views/warnManage/analysis/index.vue")
  ),
  assistantPrompt: defineAsyncComponent(() => import("@/views/warnManage/prompt/index.vue")),
  assistantModule: defineAsyncComponent(() => import("@/views/warnManage/module/index.vue")),
  assistantLogHandle: defineAsyncComponent(() => import("@/views/warnManage/handle/handle.vue")),
  assistantRecords: defineAsyncComponent(() => import("@/views/warnManage/records/index.vue")),

  // ==================== 系统管理模块 ====================
  tenant: defineAsyncComponent(() => import("@/views/system/tenant/index.vue")),
  appList: defineAsyncComponent(() => import("@/views/system/appList/index.vue")),
  organList: defineAsyncComponent(() => import("@/views/system/organList/index.vue")),
  user: defineAsyncComponent(() => import("@/views/system/user/index.vue")),
  role: defineAsyncComponent(() => import("@/views/system/role/index.vue")),
  resources: defineAsyncComponent(() => import("@/views/system/resources/index.vue")),
  flow: defineAsyncComponent(() => import("@/views/system/flow/index.vue")),
  editTopology: defineAsyncComponent(() => import("@/views/ToPo/Topology.vue")),
  warnmanage: defineAsyncComponent(() => import("@/views/warnManage/warnmanage/index.vue")),
  boceSetting: defineAsyncComponent(() => import("@/views/system/boce/index.vue")),
  // boceClient: defineAsyncComponent(() => import("@/views/system/boceClient/index.vue")),
  logDevice: defineAsyncComponent(() => import("@/views/log/device/index.vue")),
  agent: defineAsyncComponent(() => import("@/views/system/agent/index.vue")),
  syslog: defineAsyncComponent(() => import("@/views/system/syslog/index.vue")),
  snmp: defineAsyncComponent(() => import("@/views/system/snmp/index.vue")),
  aiPrompt: defineAsyncComponent(() => import("@/views/system/aiPrompt/index.vue")),
  // snmpClient: defineAsyncComponent(() => import("@/views/system/snmpClient/index.vue")),
  loginLog: defineAsyncComponent(() => import("@/views/system/log/loginLog.vue")),
  operationLog: defineAsyncComponent(() => import("@/views/system/log/operationLog.vue")),
  warnShortMessage: defineAsyncComponent(
    () => import("@/views/warnManage/method/components/textMessage.vue")
  ),
  warnEmail: defineAsyncComponent(() => import("@/views/warnManage/method/components/Email.vue")),

  // ==================== 系统管理模块 ====================
  coziOverView: defineAsyncComponent(() => import("@/views/cozi/development/index.vue")),
  resourceLibrary: defineAsyncComponent(() => import("@/views/cozi/resourceLibrary/index.vue"))
} as const;

// 导出类型声明
export type AsyncComponentMap = typeof asyncComponentMap;
export default asyncComponentMap;
