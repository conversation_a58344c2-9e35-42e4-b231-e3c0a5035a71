import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//按照省份统计
export const getStatProvince = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_province", data);
};
//按照运营商统计
export const getStatOperator = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_operator", data);
};
//浏览器TOP5
export const getBrowserTop5 = (data: any) => {
  return fetchServiceData("/h5/page_perf/browser_top5", data);
};
//操作系统TOP5
export const getOsTop5 = (data: any) => {
  return fetchServiceData("/h5/page_perf/os_top5", data);
};
