import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
export interface IResponseBase {
  code: number;
  desc: string;
  value: number;
}
export interface IRequsetBase {
  startTime?: number;
  endTime?: number;
  appid?: string;
  appName?: string;
  rows?: number;
  sort?: string;
  order?: string;
  page?: number;
  appVersion?: string;
  httpStatusCode?: string | number;
  traceId?: string;
  net?: string;
  url?: string;
  method?: string;
  httpUrl?: string;
}
export interface IInterPerforList {
  code: number;
  desc: string;
  total: number;
  records: IInterPerforItem[];
}
export interface IErrorListResponse {
  code: number;
  desc: string;
  total: number;
  records: IErrorItem[];
}

export interface IErrorItem {
  country: string;
  province: string;
  os: string;
  ip: string;
  browser: string;
  grade: string;
  id: string;
  time: string;
  category: string;
  message: string;
  userId: string;
  url: string;
}

export interface IInterPerforItem {
  count: number;
  errorRate: number;
  url: string;
  httpUrl: string;
  errorCount: number;
  avgDuration: string;
}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface ISelectAppResponse {
  code: number;
  desc: string;
  records: string[];
}

const fetchInterPerformanceData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  return get<IResponseBase>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  });
};
// 请求总数
export const getAllErrorCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/error_logs/count", data);
};
// 平均耗时
export const getJsErrorCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/error_logs/js_count", data);
};
// 错误率
export const getAjaxErrorCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/error_logs/ajax_count", data);
};
export const getOtherErrorCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/error_logs/other_count", data);
};

// 统计列表
export const getInterPerformanceList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IInterPerforList>({}, "/h5/error_logs/list", requestData);
};

// 统计图
export const getStatJsErrorCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/error_logs/stat_js_count", requestData);
};

export const getStatAjaxErrorCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/error_logs/stat_ajax_count", requestData);
};

export const getStatOtherErrorCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/error_logs/stat_other_count", requestData);
};
// 网络请求列表
// /app/net/stat_list
export const getErrorLogsList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IErrorListResponse>({}, "/h5/error_logs/list", requestData);
};

export const getOperators = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/error_logs/operators", requestData);
};
export const getOs = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/error_logs/os", requestData);
};
export const getErrorCategory = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/error_logs/category", requestData);
};
export const getBrowserNames = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/error_logs/browser_names", requestData);
};

export const getErrorDetail = (id: string) => {
  return get<IErrorDetail>({}, "/h5/error_logs/detail/" + id);
};

export interface IErrorDetail {
  code: number;
  desc: string;
  entity: IErrorDetailItem;
}

export interface IErrorDetailItem {
  id: string;
  timestamp: string;
  appid: string;
  appName: string;
  appVersion: string;
  ip: string;
  country: string;
  province: string;
  city: string;
  operator: string;
  os: string;
  browserName: string;
  browserVersion: string;
  userAgent: string;
  sessionId: string;
  userId: string;
  url: string;
  category: string;
  grade: string;
  errorUrl: string;
  line: number;
  col: number;
  message: string;
  stack: string;
}
