import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
// 访问流量（PV）
export const getPVCount = (data: any) => {
  return fetchServiceData("/h5/overview/pv", data);
};

// 访问人数（UV）
export const getUVCount = (data: any) => {
  return fetchServiceData("/h5/overview/uv", data);
};

// 页面平均加载时间
export const getLoadCount = (data: any) => {
  return fetchServiceData("/h5/overview/page_load_avg_duration", data);
};

// 接口平均耗时
export const getAJAXCount = (data: any) => {
  return fetchServiceData("/h5/overview/ajax_avg_duration", data);
};

// 接口失败率
export const getAJAXErrorCount = (data: any) => {
  return fetchServiceData("/h5/overview/ajax_error_rate", data);
};

// 错误数
export const getErrorCount = (data: any) => {
  return fetchServiceData("/h5/overview/error_logs_count", data);
};

// 访问量页面TOP5
export const getPageViewTop5 = (data: any) => {
  return fetchServiceData("/h5/overview/page_view_top5", data);
};

// 接口耗时TOP5
export const getApiDurationTop5 = (data: any) => {
  return fetchServiceData("/h5/overview/ajax_avg_duration_top5", data);
};

// 页面报错TOP5
export const getErrorLogsTop5 = (data: any) => {
  return fetchServiceData("/h5/overview/error_logs_count_top5", data);
};

// 访问量统计（图表）
export const getPageViewStats = (data: any) => {
  return fetchServiceData("/h5/overview/stat_page_view", data);
};

// 接口耗时统计（图表）
export const getApiDurationStats = (data: any) => {
  return fetchServiceData("/h5/overview/stat_ajax_avg_duration", data);
};

// 错误数统计（图表）
export const getErrorLogsStats = (data: any) => {
  return fetchServiceData("/h5/overview/stat_error_logs_count", data);
};

// H5应用列表
export const getH5AppList = (data: any) => {
  return fetchServiceData("/h5/overview/apps", data);
};
