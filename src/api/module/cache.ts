import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
const fetchServiceDataPOST = (url: string, data: any) => {
  return post({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
// redis节点列表
export const getRedisList = (data: any) => {
  return fetchServiceData("/redis/node_info/list", data);
};
// redis命令统计列表
export const getRedisCommandStats = (data: any) => {
  return fetchServiceData("/redis/command_stats/list", data);
};

// redis命令统计相关图表接口
export const getRedisDashboardData = (data: any) => {
  return fetchServiceDataPOST("/redis/command_stats/chart", data);
};

// redis指标统计结果相关图表接口
export const getRedisMetricsDashboardData = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart", data);
};

// redis指标统计结果全部图表接口
export const getRedisMetricsDashboardDataALL = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/all", data);
};

// redis命令统计全部图表接口
export const getRedisDashboardDataALL = (data: any) => {
  return fetchServiceDataPOST("/redis/command_stats/chart/all", data);
};

// 查询客户端相关聚合结果
export const getRedisClientAggregation = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/client/all", data);
};

// 查询内存相关聚合结果
export const getRedisMemoryAggregation = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/memory/all", data);
};

// 查询键空间相关聚合结果
export const getRedisKeyspaceAggregation = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/keyspace/all", data);
};

// 查询副本指标相关聚合结果
export const getRedisReplicaAggregation = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/replication/all", data);
};

// 查询命令相关聚合结果
export const getRedisCommandAggregation = (data: any) => {
  return fetchServiceDataPOST("/redis/metric_stats/chart/command/all", data);
};
