import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//主机列表
export const getHostList = (data: {}) => {
  return get({}, "/host/system_info/list", data);
};
// CPU监测（折线图）
export function getStatCpuUsage(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_cpu_usage", data);
}
//内存监测（折线图）
export function getStatMemoryUsage(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_memory_usage", data);
}
//TCP连接数监测（折线图）
export function getStatTcpConns(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_tcp_conns", data);
}
//磁盘读速率（折线图）
export function getStatDiskReadSize(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_disk_read_size", data);
}
//磁盘写速率（折线图）
export function getStatDiskWriteSize(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_disk_write_size", data);
}
//磁盘读操作速率（折线图）
export function getStatDiskReadIops(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_disk_read_iops", data);
}
//磁盘写操作速率（折线图）
export function getStatDiskWriteIops(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_disk_write_iops", data);
}
//磁盘剩余空间（折线图）
export function getStatdiskFreeSpace(data: any) {
  return fetchServiceData("/host/perf_metrics/stat_disk_free_space", data);
}
//平均流量统计（图表）
export function getStatAvgBytes(data: any) {
  return fetchServiceData("/host/overview/stat_avg_bytes", data);
}
//设置备注
export const saveRemarks = (data: { hostId: string; alias: string; remarks: string }) => {
  return post({}, "/host/system_info/ext/save", {
    appid: useApplicationStore.appId,
    ...data
  });
};
