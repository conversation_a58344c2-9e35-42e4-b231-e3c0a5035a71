import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//错误概览
export const getErrorOverviewCount = (data: any) => {
  return fetchServiceData("/mini_program/error/overview", data);
};
//设备型号TOP5
export const getErrorModelTop5 = (data: any) => {
  return fetchServiceData("/mini_program/error/model_top5", data);
};
//版本错误TOP5
export const getMiniVersionTop5 = (data: any) => {
  return fetchServiceData("/mini_program/error/mini_version_top5", data);
};
//错误数统计（图表）
export const getStatErrorCount = (data: any) => {
  return fetchServiceData("/mini_program/error/stat_error_count", data);
};
//设备型号（下拉框）
export const getErrorModel = (data: any) => {
  return fetchServiceData("/mini_program/error/model", data);
};
//小程序版本（下拉框）
export const getMiniVersions = (data: any) => {
  return fetchServiceData("/mini_program/error/mini_versions", data);
};
//明细列表
export const getErrorList = (data: any) => {
  return fetchServiceData("/mini_program/error/list", data);
};
//错误详情
export const getErrorDetail = (id: string, appid: string) => {
  return get({}, `/mini_program/error/detail/${id}?appid=${appid}`);
};
//网络类型（下拉框）
export const getNetworkTypes = (data: any) => {
  return fetchServiceData("/mini_program/error/network_type", data);
};
