import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//设备型号TOP5
export const getModelTop5 = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/model_top5", data);
};
//操作系统TOP5
export const getOsTop5 = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/os_top5", data);
};
//基础库版本TOP5
export const getLibVersionTop5 = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/lib_version_top5", data);
};
//省份分布
export const getStatProvince = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/stat_province", data);
};
