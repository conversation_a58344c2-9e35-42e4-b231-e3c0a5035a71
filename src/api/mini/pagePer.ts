import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();

const serTimeStore = serviceTimeStore();
export interface IOverviewResponse {
  code: number;
  desc: string;
  entity: IOverviewItem;
}

export interface IOverviewItem {
  avgLoadToReady: number;
  avgLoadToDom: number;
  avgRouteToLoad: number;
  avgDomToReady: number;
  avgTotalTime: number;
}

export interface IRequsetBase {}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface IPageList {
  code: number;
  desc: string;
  total: number;
  records: IPageItem[];
}

export interface IPageItem {
  avgLoadToReady: number | string;
  avgLoadToDom: number | string;
  avgRouteToLoad: number | string;
  count: number;
  pageUrl: string;
  avgDomToReady: number | string;
  avgTotalTime: number | string;
}

const fetchInterPerformanceData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  return get<IOverviewResponse>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  });
};

export const getOverviewCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/mini_program/page_perf/overview", data);
};
// 统计图
export const getMiniTotalTimeCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/page_perf/stat_total_time", requestData);
};

export const getMiniTotalPageLoadTime = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/page_perf/stat_load_to_dom", requestData);
};

export const getMiniPageRenderTime = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/page_perf/stat_dom_to_ready", requestData);
};

export const getMiniPagePerList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IPageList>({}, "/mini_program/page_perf/list", requestData);
};
