import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//影响设备数
export const getDeviceCount = (data: any) => {
  return fetchServiceData("/app/anr/device_count", data);
};
//影响版本数
export const getVersionCount = (data: any) => {
  return fetchServiceData("/app/anr/version_count", data);
};
//统计列表
export const getAnrStatList = (data: any) => {
  return fetchServiceData("/app/anr/stat_list", data);
};
//卡顿列表
export const getAnrList = (data: any) => {
  return fetchServiceData("/app/anr/list", data);
};
//卡顿详情
export const getAnrDetail = (id: string) => {
  return get({}, "/app/anr/detail/" + id);
};
//网络下拉框
export const getAnrNets = (data: any) => {
  return fetchServiceData("/app/anr/nets", data);
};
//APP版本下拉框
export const getAnrAppVersions = (data: any) => {
  return fetchServiceData("/app/anr/app_versions", data);
};
//APP版本TOP5
export const getAnrAppVersionTop5 = (data: any) => {
  return fetchServiceData("/app/anr/app_version_top5", data);
};
//设备TOP5
export const getAnrAppDeviceTop5 = (data: any) => {
  return fetchServiceData("/app/anr/device_top5", data);
};
