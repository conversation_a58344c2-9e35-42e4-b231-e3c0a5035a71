import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
export interface IUserAppsResponse {
  code: number;
  desc: string;
  total: number;
  records: IUserAppItem[];
}
export interface IUserAppItem {
  id: number;
  appid: string;
  name: string;
  contact: string;
  mobile: string;
  email: string;
  remark: string;
  organId: string;
  organName: string;
  moduleConfig: string;
  createdAt: string;
  updatedAt: string | null;
}
export interface IUserAppRequest {
  page: number;
  rows: number;
  name?: string;
}
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
// 修改密码接口
export const getUserApps = (data: IUserAppRequest) => {
  return get<IUserAppsResponse>({}, "/user_apps", data);
};

// *  APP概览

// 获取App列表
export const getAPPList = (data: any) => {
  return fetchServiceData("/app/overview/apps", data);
};

// 获取设备数
export const getDeviceCount = (data: any) => {
  return fetchServiceData("/app/overview/device_count", data);
};

// 获取平均启动时间
export const getAverageDuration = (data: any) => {
  return fetchServiceData("/app/overview/launch_avg_duration", data);
};

// 获取崩溃次数
export const getCrashCount = (data: any) => {
  return fetchServiceData("/app/overview/crash_count", data);
};

// 获取卡顿次数
export const getAnrCount = (data: any) => {
  return fetchServiceData("/app/overview/anr_count", data);
};

// 获取错误次数
export const getErrorCount = (data: any) => {
  return fetchServiceData("/app/overview/error_count", data);
};

// 获取网络错误率
export const getNetworkErrorRate = (data: any) => {
  return fetchServiceData("/app/overview/net_error_rate", data);
};
// 活跃设备数统计（图表）
export const getActiveDeviceCountChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_device_count", data);
};

// 统计网络错误率（图表）
export const getNetworkErrorStatisticsChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_net_error_rate", data);
};

// 统计启动平均耗时（图表）
export const getAverageLaunchDurationChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_launch_avg_duration", data);
};

// 统计崩溃数（图表）
export const getCrashCountChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_crash_count", data);
};

// 统计卡顿数（图表）
export const getANRCountChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_anr_count", data);
};

// 统计错误数（图表）
export const getErrorCountChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_error_count", data);
};
