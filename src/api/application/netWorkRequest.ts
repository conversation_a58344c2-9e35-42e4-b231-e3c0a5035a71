import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();

export interface IResponseBase {
  code: number;
  desc: string;
  value: number;
}
export interface IRequsetBase {
  startTime?: number;
  endTime?: number;
  appid?: string;
  appName?: string;
  rows?: number;
  sort?: string;
  order?: string;
  page?: number;
  appVersion?: string;
  httpStatusCode?: string | number;
  traceId?: string;
  net?: string;
  url?: string;
  method?: string;
  httpUrl?: string;
}
export interface INetStartlist {
  code: number;
  desc: string;
  total: number;
  records: INetItem[];
}

export interface INetItem {
  method: string;
  slowRate: string;
  count: number;
  slowCount: number;
  errorRate: string;
  url: string;
  errorCount: number;
  avgDuration: string;
}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface INetListResponse {
  code: number;
  desc: string;
  total: number;
  records: INetListItem[];
}

export interface INetListItem {
  traceId: string;
  duration: string;
  appVersion: string;
  method: string;
  id: string;
  time: string;
  net: string;
  deviceId: string;
  deviceName: string;
  url: string;
  httpStatusCode: number;
}

export interface ISelectAppResponse {
  code: number;
  desc: string;
  records: string[] | number[];
}

const fetchApplicationData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  return get<IResponseBase>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  });
};
// 请求总数
export const getNetCount = (data: IRequsetBase) => {
  return fetchApplicationData("/app/net/count", data);
};
// 平均耗时
export const getNetAvgDuration = (data: IRequsetBase) => {
  return fetchApplicationData("/app/net/avg_duration", data);
};
// 错误率
export const getNetErrorRate = (data: IRequsetBase) => {
  return fetchApplicationData("/app/net/error_rate", data);
};
// 慢请求列表
export const getNetSlowRate = (data: IRequsetBase) => {
  return fetchApplicationData("/app/net/slow_rate", data);
};
// 统计列表
export const getNetStartList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<INetStartlist>({}, "/app/net/stat_list", requestData);
};
// 统计图
export const getStatErrorRate = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/app/net/stat_error_rate", requestData);
};

export const getStatAvgDurationRate = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/app/net/stat_avg_duration", requestData);
};

export const getStatSlowRate = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/app/net/stat_slow_rate", requestData);
};
// 网络请求列表
// /app/net/stat_list
export const getNetList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<INetListResponse>({}, "/app/net/list", requestData);
};

export const getStatusCodes = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/app/net/http_status_codes", requestData);
};
export const getNetDevice = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/app/net/nets", requestData);
};
export const getStatusAppVersion = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/app/net/app_versions", requestData);
};
