import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
export interface IResponseBase {
  code: number;
  desc: string;
  value: number;
}
export interface IRequsetBase {
  startTime?: number;
  endTime?: number;
  appid?: string;
  appName?: string;
  rows?: number;
  sort?: string;
  order?: string;
  page?: number;
  appVersion?: string;
  httpStatusCode?: string | number;
  traceId?: string;
  net?: string;
  url?: string;
  method?: string;
  device?: string;
}
export interface INetStartlist {
  code: number;
  desc: string;
  total: number;
  records: INetItem[];
}

export interface INetItem {
  method: string;
  slowRate: number;
  count: number;
  slowCount: number;
  errorRate: number;
  url: string;
  errorCount: number;
  avgDuration: number;
}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface ILaunchStatListResponse {
  code: number;
  desc: string;
  total: number;
  records: ILaunchStatItem[];
}

export interface ILaunchStatItem {
  crashCount: number;
  count: number;
  slowCount: number;
  device: string;
  avgDuration: string;
}

export interface ISelectAppResponse {
  code: number;
  desc: string;
  records: string[] | number[];
}

const fetchApplicationData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  return get<IResponseBase>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  });
};
//  上面的启动总数，慢启动数，启动崩溃数
export const getLaunchCount = (data: IRequsetBase) => {
  return fetchApplicationData("/app/launch/count", data);
};
// 上面的启动总数，慢启动数，启动崩溃数
export const getLaunchSlowCount = (data: IRequsetBase) => {
  return fetchApplicationData("/app/launch/slow_count", data);
};
//上面的启动总数，慢启动数，启动崩溃数
export const getLaunchCrashCount = (data: IRequsetBase) => {
  return fetchApplicationData("/app/launch/crash_count", data);
};

export const getLaunchAvgDuration = (data: IRequsetBase) => {
  return fetchApplicationData("/app/overview/launch_avg_duration", data);
};

// 慢请求列表
export const getNetSlowRate = (data: IRequsetBase) => {
  return fetchApplicationData("/app/net/slow_rate", data);
};
// 统计列表
export const getNetStartList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<INetStartlist>({}, "/app/net/stat_list", requestData);
};

// 统计图
export const getStatLaunchSlowCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/app/launch/stat_slow_count", requestData);
};

export const getStatLaunchCrashCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/app/launch/stat_crash_count", requestData);
};

// 网络请求列表
// /app/net/stat_list
export const getLaunchStatList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ILaunchStatListResponse>({}, "/app/launch/stat_list", requestData);
};

export const getLaunchDevice = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/app/launch/nets", requestData);
};
export const getLaunchAppVersion = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/app/launch/app_versions", requestData);
};

// 详情页面表格
export interface ILaunchDetailListResponse {
  code: number;
  desc: string;
  total: number;
  records: ILaunchDetailItem[];
}

export interface ILaunchDetailItem {
  t4: string;
  appVersion: string;
  type: string;
  osName: string;
  deviceId: string;
  deviceName: string;
  osVersion: string;
  root: number;
  id: string;
  time: string;
  net: string;
  t0: string;
  t1: string;
  t2: string;
  t3: string;
  crf: string | number;
}
export const getLaunchDetailList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ILaunchDetailListResponse>({}, "/app/launch/list", requestData);
};
