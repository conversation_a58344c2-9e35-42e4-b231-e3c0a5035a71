import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//影响设备数
export const getErrorDeviceCount = (data: any) => {
  return fetchServiceData("/app/error/device_count", data);
};
// 影响版本数
export const getErrorVersionCount = (data: any) => {
  return fetchServiceData("/app/error/version_count", data);
};
//统计列表
export const getErrorStatList = (data: any) => {
  return fetchServiceData("/app/error/stat_list", data);
};
//错误列表
export const getAppErrorList = (data: any) => {
  return fetchServiceData("/app/error/list", data);
};
//错误详情
export const getErrorDetail = (id: string) => {
  return get({}, "/app/error/detail/" + id);
};
//网络下拉框
export const getErrorNets = (data: any) => {
  return fetchServiceData("/app/error/nets", data);
};
//APP版本下拉框
export const getErrorAppVersions = (data: any) => {
  return fetchServiceData("/app/error/app_versions", data);
};
//APP版本TOP5
export const getErrorAppVersionTop5 = (data: any) => {
  return fetchServiceData("/app/error/app_version_top5", data);
};
//设备TOP5
export const getErrorAppDeviceTop5 = (data: any) => {
  return fetchServiceData("/app/error/device_top5", data);
};
