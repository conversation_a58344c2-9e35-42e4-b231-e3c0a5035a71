import { get, del, post, put } from "@/http/axios";
import { serviceNameStore } from "@/store/modules/service";
const useServiceNameStore = serviceNameStore();
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
//TCP 详情
export const getDetail = (data: any) => {
  data = {
    ...data,
    appid: useApplicationStore.appId
  };
  return get({}, `/netflow/session/tcp/detail`, data);
};

// 流量跟踪
export const getTraffic = (data: any) => {
  data = {
    ...data,
    appid: useApplicationStore.appId
  };
  return get({}, `/netflow/session/tcp/detail/timeline`, data);
};

// 图表
export const getChartTraffic = (data: any) => {
  return get({}, "/netflow/overview/traffic", data);
};

// 数据包详情
// export const getDetailPacket = (data: any) => {
//   data = {
//     ...data,
//     appid: useApplicationStore.appId
//   };
//   return get({}, `/netflow/session/tcp/detail/packet`, data);
// };

// ip列表
export const getHostInfo = (data: any) => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  data = {
    ...data,
    startTime: start,
    endTime: end
  };
  const { appId } = data;
  return get({}, `/netflow/hostinfo`, data);
};

// 删除
export const deleteValue = (data: any) => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  data = {
    ...data,
    start: start,
    end: end
  };
  const { appid, deviceUuid } = data;
  return del(
    {},
    `/netflow/hostinfo?appid=${appid}&deviceUuid=${deviceUuid}&startTime=${start}&endTime=${end}`,
    {}
  );
};

export const getFlowRulesInfo = (appid: string, data: any) => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  data = {
    ...data,
    start_time: start,
    end_time: end
  };
  return get({}, `/netflow/flow-mirror/groups`, data);
};

export const creatRules = (data: any) => {
  data = {
    ...data
  };
  return post(
    {},
    `/netflow/flow-mirror/rules?appid=${useApplicationStore.appId}`,
    data,
    false,
    true
  );
};

export const delRules = (appid: string, id: number, data?: any) => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  return del(
    {},
    `/netflow/flow-mirror/rules?appid=${useApplicationStore.appId}&ruleId=${id}&startTime=${start}&endTime=${end}`,
    {}
  );
};

export const editStatus = (ruleId: number, data: any) => {
  return put(
    {},
    `/netflow/flow-mirror/rules?appid=${useApplicationStore.appId}&ruleId=${ruleId}&status=${data.status}`,
    data,
    false,
    true
  );
};
