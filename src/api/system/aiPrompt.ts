import { get, post, put, del } from "@/http/axios";
// 查询AI提示词模板列表
export const aiPromptList = (data: any) => {
  return get({}, "/sys/ai/prompt/template/list", data);
};
//获取模板分类列表
export const aiPromptCategories = () => {
  return get({}, "/sys/ai/prompt/template/categories");
};

//新增AI提示词模板
export const aiPromptAdd = (data: any) => {
  return post({}, "/sys/ai/prompt/template/add", data);
};

//获取AI提示词模板详情
export const aiPromptDetail = (id: string) => {
  return get({}, `/sys/ai/prompt/template/detail/${id}`, {});
};

//编辑AI提示词模板
export const aiPromptEdit = (data: any) => {
  return put({}, "/sys/ai/prompt/template/edit", data);
};

//删除AI提示词模板
export const aiPromptDelete = (id: string) => {
  return del({}, `/sys/ai/prompt/template/delete/${id}`, {});
};

//复制AI提示词模板
export const aiPromptCopy = (id: string, newName?: string) => {
  const url = newName
    ? `/sys/ai/prompt/template/copy/${id}?newName=${encodeURIComponent(newName)}`
    : `/sys/ai/prompt/template/copy/${id}`;
  return post({}, url, {});
};
//搜索模板
export const aiPromptSearch = () => {
  return get({}, "/sys/ai/prompt/template/search");
};
