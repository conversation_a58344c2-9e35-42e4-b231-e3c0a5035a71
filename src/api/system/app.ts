import { get, post } from "@/http/axios";

//查询所有单位条目
export const getOrganEntries = (data: {}) => {
  return get({}, "/sys/organ/entries", data);
};
//应用列表
export function listApp(params: any) {
  return get({}, "/sys/app/list", params);
}
//根据ID加载详情
export function getApp(id: number) {
  return get({}, "/sys/app/detail/" + id);
}
//新增应用
export function insertApp(data: any) {
  return post({}, "/sys/app/add", data);
}
//修改应用
export function updateApp(data: any) {
  return post({}, "/sys/app/edit", data);
}
//删除应用
export function delApp(id: any) {
  return get({}, "/sys/app/delete/" + id);
}
//为应用绑定有权限的用户
export function bindUser(id: any, params: any) {
  return post({}, "/sys/app/bind_user/" + id + "?userIds=" + params.userIds, params);
}
//根据APP分页查询有权限的用户
interface GetUserListParams {
  page?: number;
  rows?: number;
  appId?: string;
  name?: string;
  type?: string;
}
export const user = (data: GetUserListParams) => {
  return get({}, "/sys/user/list_by_app", data);
};
//解除绑定
export function unBindUser(userId: any, appId: any) {
  return get({}, "/sys/user_app/unbind?userId=" + userId + "&appId=" + appId);
}
