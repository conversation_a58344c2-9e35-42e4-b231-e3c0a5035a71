import { get, post } from "@/http/axios";

interface GetUserListParams {
  page?: number;
  rows?: number;
  name?: string;
  type?: string;
}

interface AddUserParams {
  name?: string;
  mobile?: string;
  email?: string;
  password?: string;
  remark?: string;
  type?: number;
}

interface UpdateUserParams {
  id: number;
  name?: string;
  mobile?: string;
  email?: string;
  password?: string;
  remark?: string;
  type?: number;
}

interface listByUser {
  page?: number;
  rows?: number;
  userId?: number;
  organId?: string;
  name?: string;
}
// 获取用户列表
export const userList = (data: GetUserListParams) => {
  return get({}, "/sys/user/list", data);
};

// 根据id查询用户
export const detailUser = (data: number) => {
  return get({}, `/sys/user/detail/${data}`);
};

// 新增用户
export const addUser = (data: AddUserParams) => {
  return post({}, "/sys/user/add", data);
};

// 编辑用户
export const updateUser = (data: UpdateUserParams) => {
  return post({}, "/sys/user/edit", data);
};

// 重置密码
export const changePassword = (data: any) => {
  return post({}, `/sys/user/edit_pwd/${data.id}`, data);
};

// 删除用户
export const deleteUser = (data: number) => {
  return get({}, `/sys/user/delete/${data}`);
};

// 锁定用户
export const lockUser = (data: number) => {
  return get({}, `/sys/user/lock/${data}`);
};

// 解锁用户
export const unlockUser = (data: number) => {
  return get({}, `/sys/user/unlock/${data}`);
};

// 获取应用选项
export const entriesApp = () => {
  return get({}, "/sys/app/entries");
};

// 绑定APP权限
export const bindApp = (data: any) => {
  return post({}, `/sys/user/bind_app/${data.id}`, { appIds: data.appIds }, true);
};

// 获取用户已绑定应用
export const getApp = (data: listByUser) => {
  return get({}, `/sys/app/list_by_user`, data);
};

// 查询系统所有的组织树
export const getTrees = (data: listByUser) => {
  return get({}, `/sys/organ/trees`, data);
};

// // 获取用户已绑定应用
// export const getApp = (data: listByUser) => {
//   return get({}, `/sys/app/list_by_user`, data);
// };

// // 获取用户已绑定应用
// export const getApp = (data: listByUser) => {
//   return get({}, `/sys/app/list_by_user`, data);
// };

// // 获取用户已绑定应用
// export const getApp = (data: listByUser) => {
//   return get({}, `/sys/app/list_by_user`, data);
// };
