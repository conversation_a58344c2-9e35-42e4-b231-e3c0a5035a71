import { get, post, put, del } from "@/http/axios";

//列表
export function ipConfigList(params: any) {
  return get({}, "/sys/app/traffic/ip/config/list", params);
}
//删除
export function ipConfigDelete(id: any) {
  return get({}, "/sys/app/traffic/ip/config/delete/" + id);
}
//应用下拉框
export function appName() {
  return get({}, "/sys/app/list/like");
}
//详情
export function ipConfigDetail(id: any) {
  return get({}, "/sys/app/traffic/ip/config/detail/" + id);
}
//新增
export function addIpConfig(data: any) {
  return post({}, "/sys/app/traffic/ip/config/add", data);
}
//编辑
export function editIpConfig(data: any) {
  return post({}, "/sys/app/traffic/ip/config/edit", data);
}
//获取映射列表
export function serviceMapping(data: any) {
  return get({}, "/sys/flow/service/mapping", data);
}
//创建服务映射
export function addServiceMapping(data: any) {
  return post({}, "/sys/flow/service/mapping", data);
}
//更新服务映射
export function editServiceMapping(data: any) {
  return put({}, "/sys/flow/service/mapping", data);
}

// 获取服务详情
export function getServiceDetail(appid: string, ruleId: string) {
  return get({}, `/sys/flow/service/mapping/${appid}/${ruleId}`);
}

// 删除服务
export function deleteService(appid: string, ruleId: string) {
  return del({}, `/sys/flow/service/mapping/${appid}/${ruleId}`, {});
}
