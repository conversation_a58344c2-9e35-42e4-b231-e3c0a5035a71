// 工作流输入参数类型
export interface WorkflowInput {
  name: string;
  type: string;
  description: string;
  required: boolean;
}

// 工作流节点配置类型
export interface WorkflowNodeConfig {
  source_id?: string;
  prompt?: string;
  system_template_name?: string;
  user_template_name?: string;
  adhoc_instruction?: string;
  [key: string]: any;
}

// 工作流节点类型
export interface WorkflowNode {
  id: string;
  type: string;
  config: WorkflowNodeConfig;
  param_mappings: Record<string, string>;
  position: {
    x: number;
    y: number;
  };
}

// 工作流边类型
export interface WorkflowEdge {
  source_node_id: string;
  source_output: string;
  target_node_id: string;
  target_input: string;
}

// 工作流图类型
export interface WorkflowGraph {
  inputs: WorkflowInput[];
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  output_source: string;
  workflow_type: string;
}

// 创建工作流请求类型
export interface CreateWorkflowRequest {
  name: string;
  graph: WorkflowGraph;
}

// 工作流项类型
export interface WorkflowItem {
  id: string;
  name: string;
  graph: WorkflowGraph;
  dify_app_id?: string;
  created_at: string;
  updated_at: string;
}

// 工作流列表响应类型
export interface WorkflowListResponse {
  success: boolean;
  code: number;
  message: string;
  data: {
    items: WorkflowItem[];
    total?: number;
  };
}

// 创建工作流响应类型
export interface CreateWorkflowResponse {
  success: boolean;
  code: number;
  message: string;
  data: WorkflowItem;
}

// 数据源类型
export interface DataType {
  id: string;
  description: string;
  required_params: string[];
}

export interface DataSource {
  name: string;
  data_types: DataType[];
}

// 数据源列表响应类型
export interface DataSourceListResponse {
  success: boolean;
  code: number;
  message: string;
  data: {
    items: DataSource[];
    page: number;
    page_size: number;
    total: number;
  };
}

// 提示模板类型
export interface PromptTemplate {
  id: number;
  template_group_id: string;
  name: string;
  content: string;
  version: number;
  scope: string;
  owner_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 提示模板列表响应类型
export interface PromptTemplateListResponse {
  success: boolean;
  code: number;
  message: string;
  data: PromptTemplate[];
}

// 分页参数类型
export interface PageParams {
  page: number;
  page_size: number;
}
