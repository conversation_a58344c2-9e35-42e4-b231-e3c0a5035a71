import { serviceTimeStore } from "@/store/modules/global";
import { post, get } from "@/http/axios";
const serTimeStore = serviceTimeStore();
export interface ITopoResponse {
  code: number;
  desc: string;
  entity: Entity;
}

export interface Entity {
  nodes: Nodes[];
  links: Links[];
}

export interface Links {
  dst: string;
  src: string;
  totalPack: number;
  retRate: number;
  totalBytes: number;
  rstRate: number;
  errorCount: number;
}

export interface Nodes {
  cpuUsage: number;
  memoryUsage: number;
  os: string;
  ip: string;
  memoryTotal: number;
  isOnline: boolean;
  cpuNum: number;
}

export interface ITopoRequest {
  appid: string;
  ip?: string;
}

const fetchServiceData = (url: string, data: ITopoRequest) => {
  return get<ITopoResponse>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

export const getIpTopoData = (data: ITopoRequest) => {
  return fetchServiceData("/host/overview/topo", data);
};

export const getSessionTopoData = (data: ITopoRequest) => {
  return fetchServiceData("/host/overview/session_topo", data);
};
