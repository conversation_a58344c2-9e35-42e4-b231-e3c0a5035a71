import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// IP流量列表
export const getStatOutsideIp = (data: any) => {
  return fetchServiceData("/host/flow_session/stat_outside_ip", data);
};
//会话列表
export const getFlowSessionList = (data: any) => {
  return fetchServiceData("/host/flow_session/list", data);
};
