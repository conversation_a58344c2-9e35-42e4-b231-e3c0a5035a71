import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// 概览
export const getSummaryType = (data: any) => {
  return fetchServiceData("/flow/overview/summary", data);
};

// 统计组播、单播、广播流量
export const getStatTrafficBytes = (data: any) => {
  return fetchServiceData("/flow/overview/stat_traffic_bytes", data);
};

// 统计重传
export const getStatPackRet = (data: any) => {
  return fetchServiceData("/flow/overview/stat_pack_ret", data);
};

// 平均流量统计（图表）
export const getStatAvgBytes = (data: any) => {
  return fetchServiceData("/flow/overview/stat_avg_bytes", data);
};
