import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//实例列表
export const getInstanceList = (data: any) => {
  return fetchServiceData("/service/instance/list", data);
};
//实例详情
export function getInstanceDetail(params: any) {
  return get({}, "/service/instance/detail", params);
}
//IP地址下拉框数据
export function getInstanceIps(data: any) {
  return fetchServiceData("/service/instance/ips", data);
}
//实例ID下拉框数据
export function getInstanceIds(data: any) {
  return fetchServiceData("/service/instance/ids", data);
}
//按请求数统计（图表）
export function getStatRequestCount(data: any) {
  return fetchServiceData("/service/instance/stat_request_count", data);
}
//按错误数统计（图表）
export function getStatErrorCount(data: any) {
  return fetchServiceData("/service/instance/stat_error_count", data);
}
//按平均耗时统计（图表）
export function getStatAvgDuration(data: any) {
  return fetchServiceData("/service/instance/stat_avg_duration", data);
}
//CPU利用率
export function getJvmStatCpu(data: any) {
  return fetchServiceData("/service/jvm/stat_cpu_utilization", data);
}
//按类型统计内存使用率
export function getJvmStatMemory(data: any) {
  return fetchServiceData("/service/jvm/stat_memory_type", data);
}
//按内存池统计内存使用率
export function getJvmStatMemoryPool(data: any) {
  return fetchServiceData("/service/jvm/stat_memory_pool", data);
}
//统计GC时间
export function getJvmStatGc(data: any) {
  return fetchServiceData("/service/jvm/stat_gc_time", data);
}
//统计GC数量
export function getJvmStatGcCount(data: any) {
  return fetchServiceData("/service/jvm/stat_gc_count", data);
}
//按照线程类型统计
export function getJvmStatThreadType(data: any) {
  return fetchServiceData("/service/jvm/stat_thread_type", data);
}
//按照线程状态统计
export function getJvmStatThreadState(data: any) {
  return fetchServiceData("/service/jvm/stat_thread_state", data);
}
//统计类加载数量
export function getJvmStatClassCount(data: any) {
  return fetchServiceData("/service/jvm/stat_class_count", data);
}
