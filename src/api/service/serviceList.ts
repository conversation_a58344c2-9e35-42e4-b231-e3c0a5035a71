import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//服务列表
export const getServiceList = (data: any) => {
  return fetchServiceData("/service/overview/list", data);
};
//提供服务列表
export const getServiceTraces = (data: any) => {
  return fetchServiceData("/service/endpoint/list", data);
};
//请求类型
export const getRequestTypes = (data: any) => {
  return fetchServiceData("/service/endpoint/request_types", data);
};
//接口名称下拉框数据
export const getEndpointNames = (data: any) => {
  return fetchServiceData("/service/endpoint/names", data);
};
//按请求数统计（图表）
export const getStatRequestCount = (data: any) => {
  return fetchServiceData("/service/endpoint/stat_request_count", data);
};
//按错误数统计（图表）
export const getStatErrorCount = (data: any) => {
  return fetchServiceData("/service/endpoint/stat_error_count", data);
};
//按平均耗时统计（图表）
export const getStatAvgDuration = (data: any) => {
  return fetchServiceData("/service/endpoint/stat_avg_duration", data);
};
// 保存服务别名
export const saveServiceAlias = (data: { serviceName: string; alias: string }) => {
  return post({}, "/sys/service-ext-info/save", {
    appid: useApplicationStore.appId,
    ...data
  });
};
