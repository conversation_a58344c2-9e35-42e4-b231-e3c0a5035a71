import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
export interface ITopologyResponse {
  code: number;
  desc: string;
  entity: Entity;
}

export interface Entity {
  nodes: Nodes[];
  links: Links[];
}

export interface Links {
  requestCount: number;
  dst: string;
  src: string;
  errorRate: number;
  errorCount: number;
  avgDuration: number;
}

export interface Nodes {
  requestCount: number;
  product: string;
  name: string;
  alias: string;
  errorRate: number;
  errorCount: number;
  avgDuration: number;
}

export interface ITopologyRequest {
  appid: string;
  serviceName?: string;
}
const fetchServiceData = (url: string, data: ITopologyRequest) => {
  return get<ITopologyResponse>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

export const getTopology = (data: ITopologyRequest) => {
  return fetchServiceData("/service/overview/topo", data);
};
