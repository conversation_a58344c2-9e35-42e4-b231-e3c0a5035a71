export interface IBaseResult {
  desc: string; // 描述
  code: number; // 状态码
}
export interface IServiceIndicatorRequest {
  appid?: string;
  serviceName?: string;
  sourceType?: string;
}
export interface IServiceIndicatorResponse extends IBaseResult {
  value: number;
}

export interface IRequestCountTopResponse extends IBaseResult {
  records: IRequestCountTopItem[];
}
export interface IErrorCountTopResponse extends IBaseResult {
  records: IErrorCountTopItem[];
}
export interface IAvgDurationTopResponse extends IBaseResult {
  records: IAvgDurationCountTopItem[];
}

export interface IRequestCountTopItem {
  alias: string;
  count: number;
  requestCount: number;
  serviceName: string;
  endpoint: string;
}
export interface IErrorCountTopItem {
  alias: string;
  errorCount: number;
  serviceName: string;
  endpoint: string;
}

export interface IAvgDurationCountTopItem {
  alias: string;
  avgDuration: number;
  serviceName: string;
  endpoint: string;
}
