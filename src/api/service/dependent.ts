import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//依赖服务列表
export const getDependentList = (data: any) => {
  return fetchServiceData("/service/dependent/list", data);
};
//请求类型
export const getDependentTypes = (data: any) => {
  return fetchServiceData("/service/dependent/request_types", data);
};
//目标服务
export const getDependentTargetAddress = (data: any) => {
  return fetchServiceData("/service/dependent/target_address", data);
};
//按请求数统计（图表）
export const getStatRequestCount = (data: any) => {
  return fetchServiceData("/service/dependent/stat_request_count", data);
};
//按错误数统计（图表）
export const getStatErrorCount = (data: any) => {
  return fetchServiceData("/service/dependent/stat_error_count", data);
};
//按平均耗时统计（图表）
export const getStatAvgDuration = (data: any) => {
  return fetchServiceData("/service/dependent/stat_avg_duration", data);
};
