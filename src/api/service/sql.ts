import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//服务列表
export const getSqlList = (data: any) => {
  return fetchServiceData("/service/sql/list", data);
};
//数据库名称
export const getSqlNames = (data: any) => {
  return fetchServiceData("/service/sql/db_names", data);
};
//数据库类型
export const getSqlTypes = (data: any) => {
  return fetchServiceData("/service/sql/db_systems", data);
};
//MQ列表
export const getMqList = (data: any) => {
  return fetchServiceData("/service/mq/list", data);
};
//MQ请求类型下拉框选项
export const getMqTypes = (data: any) => {
  return fetchServiceData("/service/mq/request_types", data);
};
//MQ队列下拉框选项
export const getMqQueues = (data: any) => {
  return fetchServiceData("/service/mq/queues", data);
};
//数据库名称下拉框选项
export const getdbNames = (data: any) => {
  return fetchServiceData("/service/sql/db_names", data);
};
//数据库类型下拉框选项
export const getdbTypes = (data: any) => {
  return fetchServiceData("/service/sql/db_types", data);
};
//请求数统计(SQL/NoSQL)
export const getStatRequestCount = (data: any) => {
  return fetchServiceData("/service/sql/stat_request_count", data);
};
//错误数统计(SQL/NoSQL)
export const getStatErrorCount = (data: any) => {
  return fetchServiceData("/service/sql/stat_error_count", data);
};
//平均耗时数统计(SQL/NoSQL)
export const getStatAvgDuration = (data: any) => {
  return fetchServiceData("/service/sql/stat_avg_duration", data);
};
//请求数统计(MQ)
export const getMQStatRequestCount = (data: any) => {
  return fetchServiceData("/service/mq/stat_request_count", data);
};
//错误数统计(MQ)
export const getMQStatErrorCount = (data: any) => {
  return fetchServiceData("/service/mq/stat_error_count", data);
};
//平均耗时数统计(MQ)
export const getMQStatAvgDuration = (data: any) => {
  return fetchServiceData("/service/mq/stat_avg_duration", data);
};
