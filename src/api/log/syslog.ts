import { get, post } from "@/http/axios";
// 获取列表
export const syslogList = (data: any) => {
  return get({}, `/syslog/mapping/list`, data);
};

// 新增
export const addSyslog = (data: any) => {
  return post({}, `/syslog/mapping/add`, data);
};

// 删除
export const deleteSyslog = (id: string) => {
  return get({}, `/syslog/mapping/delete/${id}`);
};

// 编辑
export const editSyslog = (data: any) => {
  return post({}, `/syslog/mapping/edit`, data);
};

// 获取详情
export const getSyslogDetail = (id: string) => {
  return get({}, `/syslog/mapping/${id}`);
};

// 获取日志类型列表
export const getLogTypeList = () => {
  return get({}, "/syslog/type/list");
};
//获取默认映射
export const getDefaultMapping = () => {
  return get({}, `/syslog/mapping/getDefaultMapping`);
};
//app列表
export const getAllApp = () => {
  return get({}, "/syslog/mapping/getAllApp");
};
