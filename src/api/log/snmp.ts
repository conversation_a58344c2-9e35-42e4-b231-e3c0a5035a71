import { get, post } from "@/http/axios";
//查询snmp配置列表
export const snmpList = (data: any) => {
  return get({}, "/snmp/config/list", data);
};
//oid列表
export const oidList = () => {
  return get({}, "/snmp/oid/list");
};
//查询Collect所在主机信息
export const collectList = () => {
  return get({}, "/snmp/collect/queryAll");
};
//获取所有app
export const getAllApp = () => {
  return get({}, "/sys/app/getAllApp");
};
// 新增snmp配置
export const addSnmp = (data: any) => {
  return post({}, "/snmp/config/add", data);
};
//编辑配置
export const editSnmp = (data: any) => {
  return post({}, "/snmp/config/edit", data);
};
//删除配置
export const deleteSnmp = (id: string) => {
  return get({}, `/snmp/config/delete/${id}`);
};
//获取配置详情
export const detailSnmp = (id: string) => {
  return get({}, `/snmp/config/${id}`);
};
//查询snmp执行列表
export const snmpHostList = (data: any) => {
  return get({}, "/snmp/host/data/list", data);
};

// 查询snmp客户端列表
export const snmpClientList = (data: any) => {
  return get({}, "/snmp/collect/list", data);
};

// 查询采集所在主机
export const collectHostList = (data: any) => {
  return get({}, "/snmp/collect/queryAll", data);
};

// 查询配置详情
export const detailSnmpCollect = (data: any) => {
  return get({}, `/snmp/collect/${data.id}`);
};

// 删除配置
export const deleteSnmpCollect = (data: any) => {
  return get({}, `/snmp/collect/delete/${data.id}`);
};

// 新增采集模配置
export const addSnmpCollect = (data: any) => {
  return post({}, "/snmp/collect/add", data);
};

// 编辑采集配置
export const editSnmpCollect = (data: any) => {
  return post({}, "/snmp/collect/edit", data);
};
