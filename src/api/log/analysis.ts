import { get, post, getBlob } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();

// 日志分析报告列表
export const getLogAnalysisList = (data: any) => {
  const datas = {
    appId: useApplicationStore.appId,
    ...data
  };
  return get({}, "/logs/report_list", datas);
};

// 日志分析报告下载接口
export const downloadLogAnalysisReport = (data: any) => {
  return getBlob({}, `/logs/report_download`, data);
};

// 日志分析报告异步导出接口
export const exportLogAnalysisReport = (data: any, bodyData: any) => {
  const request = {
    request: {
      appid: useApplicationStore.appId,
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      query: data.query
    },
    pageRequest: {
      page: 1,
      rows: 10
    },
    logEntity: {
      level: bodyData.level,
      message: bodyData.message,
      sourceIp: bodyData.sourceIp,
      sourceName: bodyData.sourceName,
      sourceType: bodyData.sourceType,
      metadata: bodyData.metadata
    }
  };
  return post({}, "/logs/async_analysis", request);
};
