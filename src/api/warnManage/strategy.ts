import { get, post } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appId = useApplicationStore.appId;

//获取所有告警模块
export const moduleList = () => {
  return get({}, "/alarm/module/list");
};
//获取所有告警指标
export const metricsList = () => {
  return get({}, "/alarm/metrics/list");
};
//查询应用下的所有告警对象组键值对
export const notifierGroup = (data: any) => {
  return get({}, "/alarm/notifier_group/entries", data);
};
//告警规则分页查询
export const ruleList = (data: any) => {
  return get({}, "/alarm/rule/list", data);
};
//保存告警规则
export const ruleAdd = (data: any) => {
  return post({}, "/alarm/rule/add", data);
};
//加载告警规则
export const ruleDetail = (id: any) => {
  return get({}, "/alarm/rule/detail/" + id + "?appid=" + appId);
};
//修改告警规则
export const ruleEdit = (data: any) => {
  return post({}, "/alarm/rule/edit", data);
};
//修改告警规则状态
export const ruleChange = (id: any, status: any) => {
  return get({}, "/alarm/rule/update_enable/" + id + "?appid=" + appId + "&status=" + status);
};
//删除告警规则
export const ruleDelete = (id: number) => {
  return get({}, "/alarm/rule/delete/" + id + "?appid=" + appId);
};
