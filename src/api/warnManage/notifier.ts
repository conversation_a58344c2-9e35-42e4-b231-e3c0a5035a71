import { get, post } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appId = useApplicationStore.appId;

//告警通知对象列表
export function notifierList(data: any) {
  return get({}, "/alarm/notifier/list", data);
}
//新增告警通知对象
export function notifierAdd(data: any) {
  return post({}, "/alarm/notifier/add", data);
}
//修改告警通知对象
export function notifierEdit(data: any) {
  return post({}, "/alarm/notifier/edit", data);
}
//加载对象详情
export function notifierDetail(id: number) {
  return get({}, "/alarm/notifier/detail/" + id + "?appid=" + appId);
}
//删除告警通知对象
export function notifierDelete(id: number) {
  return get({}, "/alarm/notifier/delete/" + id + "?appid=" + appId);
}
//分页查询当前对象组下的所有对象
export function notifierByGroup(data: any) {
  return get({}, "/alarm/notifier_group/list_by_notifier", data);
}
//为通知对象绑定对象组
export function notifierBindGroup(id: any, params: any) {
  return post(
    {},
    "/alarm/notifier/bind_group/" + id + "?groupIds=" + params.groupIds + "&appid=" + appId,
    params
  );
}
//告警通知对象组列表
export function notifierGroupList(data: any) {
  return get({}, "/alarm/notifier_group/list", data);
}
//解除绑定关系
export function unbindNotifier(groupId: any, notifierId: any) {
  return get(
    {},
    "/alarm/group_notifier_rel/unbind?groupId=" +
      groupId +
      "&notifierId=" +
      notifierId +
      "&appid=" +
      appId
  );
}
